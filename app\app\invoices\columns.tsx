// import { ColumnDef } from "@tanstack/react-table"
// import { Button } from "@/components/ui/button";
// import { Checkbox } from "@/components/ui/checkbox";
// import { Badge } from "@/components/ui/badge";
// import { ArrowUpDown, MoreHorizontal, CheckCircle2, Circle } from "lucide-react"
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu"
// import { Customer } from "@/lib/api/clients/models";
// import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
// import { format } from "date-fns";
// import { updateInvoice } from "@/lib/api/invoices/services";
// import { Invoice, PaymentStatus } from "@/lib/api/invoices/models";
// import Cookies from "js-cookie";

// interface GetColumnsProps {
//   customers: Customer[];
//   router: AppRouterInstance;
// }

// export function getColumns({ customers, router }: GetColumnsProps): ColumnDef<Invoice>[] {
//   return [
//     {
//       id: "select",
//       header: ({ table }) => (
//         <Checkbox
//           checked={
//             table.getIsAllPageRowsSelected() ||
//             (table.getIsSomePageRowsSelected() && "indeterminate")
//           }
//           onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
//           aria-label="Select all"
//         />
//       ),
//       cell: ({ row }) => (
//         <Checkbox
//           checked={row.getIsSelected()}
//           onCheckedChange={(value) => row.toggleSelected(!!value)}
//           aria-label="Select row"
//         />
//       ),
//       enableSorting: false,
//       enableHiding: false,
//     },
//     {
//       accessorKey: "invoice_number",
//       header: ({ column }) => {
//         return (
//           <Button
//             variant="ghost"
//             onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//             className="p-0 hover:bg-transparent font-bold"
//           >
//             Invoice Number
//             <ArrowUpDown className="ml-2 h-4 w-4" />
//           </Button>
//         );
//       },
//       cell: ({ row }) => {
//         const invoice = row.original;
//         return (
//           <Button
//             variant="link"
//             className="p-0 h-auto font-bold hover:no-underline"
//             onClick={() => router.push(`/app/invoices/${invoice.id}`)}
//           >
//             {invoice.invoice_number}
//           </Button>
//         );
//       },
//       enableSorting: true,
//     },
//     {
//       accessorKey: "customer_id",
//       header: ({ column }) => {
//         return (
//           <Button
//             variant="ghost"
//             onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//             className="p-0 hover:bg-transparent font-bold"
//           >
//             Customer
//             <ArrowUpDown className="ml-2 h-4 w-4" />
//           </Button>
//         );
//       },
//       cell: ({ row }) => {
//         const customer = customers.find((c) => c.id === row.getValue("customer_id"));
//         return <div className="text-sm">{customer?.name || "N/A"}</div>;
//       },
//       enableSorting: true,
//     },
//     {
//       accessorKey: "invoice_date",
//       header: ({ column }) => {
//         return (
//           <Button
//             variant="ghost"
//             onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//             className="p-0 hover:bg-transparent font-bold"
//           >
//             Date
//             <ArrowUpDown className="ml-2 h-4 w-4" />
//           </Button>
//         );
//       },
//       cell: ({ row }) => {
//         const date = new Date(row.getValue("invoice_date"));
//         return <div className="text-sm">{format(date, "MMM d, yyyy")}</div>;
//       },
//       enableSorting: true,
//     },
//     {
//       accessorKey: "due_date",
//       header: ({ column }) => (
//         <Button
//           variant="ghost"
//           onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//         >
//           Due Date
//           <ArrowUpDown />
//         </Button>
//       ),
//       cell: ({ row }) => <div>{row.getValue("due_date") || "N/A"}</div>,
//       enableSorting: true,
//     },
//     {
//       accessorKey: "payment_status",
//       header: ({ column }) => {
//         return (
//           <Button
//             variant="ghost"
//             onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//             className="p-0 hover:bg-transparent font-bold"
//           >
//             Status
//             <ArrowUpDown className="ml-2 h-4 w-4" />
//           </Button>
//         );
//       },
//       cell: ({ row }) => {
//         const invoice = row.original;
//         const status = invoice.payment_status;
//         const token = Cookies.get("auth_token");
//         const store_id = Cookies.get("active_store");

//         const handleStatusChange = async () => {
//           if (!token || !store_id) return;

//           try {
//             const newStatus = status === PaymentStatus.Paid ? PaymentStatus.Unpaid : PaymentStatus.Paid;
//             await updateInvoice(token, store_id, invoice.id, {
//               ...invoice,
//               payment_status: newStatus,
//               payment_date: newStatus === PaymentStatus.Paid ? new Date().toISOString() : undefined,
//             });
//             router.refresh();
//           } catch (error) {
//             console.error("Error updating payment status:", error);
//           }
//         };

//         return (
//           <Button
//             variant="ghost"
//             size="sm"
//             className="p-0 h-auto hover:bg-transparent"
//             onClick={handleStatusChange}
//           >
//             {status === PaymentStatus.Paid ? (
//               <CheckCircle2 className="h-4 w-4 text-green-500" />
//             ) : (
//               <Circle className="h-4 w-4 text-red-500" />
//             )}
//           </Button>
//         );
//       },
//       enableSorting: true,
//     },
//     {
//       accessorKey: "total_amount",
//       header: ({ column }) => {
//         return (
//           <Button
//             variant="ghost"
//             onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//             className="p-0 hover:bg-transparent font-bold"
//           >
//             Amount
//             <ArrowUpDown className="ml-2 h-4 w-4" />
//           </Button>
//         );
//       },
//       cell: ({ row }) => {
//         const amount = parseFloat(row.getValue("total_amount"));
//         return (
//           <div className="text-sm font-medium">
//             {amount.toLocaleString("en-US", {
//               style: "currency",
//               currency: "USD",
//             })}
//           </div>
//         );
//       },
//       enableSorting: true,
//     },
//     {
//       id: "actions",
//       enableHiding: false,
//       cell: ({ row }) => {
//         const invoice = row.original;

//         return (
//           <DropdownMenu>
//             <DropdownMenuTrigger asChild>
//               <Button variant="ghost" className="h-8 w-8 p-0">
//                 <span className="sr-only">Open menu</span>
//                 <MoreHorizontal />
//               </Button>
//             </DropdownMenuTrigger>
//             <DropdownMenuContent align="end">
//               <DropdownMenuLabel>Actions</DropdownMenuLabel>
//               <DropdownMenuItem onClick={() => navigator.clipboard.writeText(invoice.id)}>
//                 Copy Invoice ID
//               </DropdownMenuItem>
//               <DropdownMenuSeparator />
//               <DropdownMenuItem
//                 onClick={() => {
//                   router.push(`/app/invoices/${invoice.id}`)
//                 }}
//               >View Details</DropdownMenuItem>
//               <DropdownMenuItem
//                 onClick={() => {
//                   router.push(`/app/invoices/${invoice.id}/edit`);
//                 }}
//               >
//                 Edit Invoice
//               </DropdownMenuItem>

//             </DropdownMenuContent>
//           </DropdownMenu>
//         );
//       },
//     },
//   ];
// }

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ArrowUpDown,
  MoreHorizontal,
  CheckCircle2,
  Circle,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Customer } from "@/lib/api/clients/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { format } from "date-fns";
import { updateInvoice } from "@/lib/api/invoices/services";
import { Invoice, PaymentStatus } from "@/lib/api/invoices/models";
import Cookies from "js-cookie";

interface GetColumnsProps {
  customers: Customer[];
  router: AppRouterInstance;
}

export function getColumns({
  customers,
  router,
}: GetColumnsProps): ColumnDef<Invoice>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "invoice_number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Invoice Number
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const invoice = row.original;
        return (
          <Button
            variant="link"
            className="p-0 h-auto font-bold hover:no-underline"
            onClick={() => router.push(`/app/invoices/${invoice.id}`)}
          >
            {invoice.invoice_number}
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "customer_id",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Customer
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const customer = customers.find(
          (c) => c.id === row.getValue("customer_id"),
        );
        return <div className="text-sm">{customer?.name || "N/A"}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "invoice_date",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Date
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("invoice_date"));
        return <div className="text-sm">{format(date, "MMM d, yyyy")}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "due_date",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Due Date
          <ArrowUpDown />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue("due_date") || "N/A"}</div>,
      enableSorting: true,
    },
    {
      accessorKey: "payment_status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const invoice = row.original;
        const status = invoice.payment_status;
        const token = Cookies.get("auth_token");
        const store_id = Cookies.get("active_store");

        const handleStatusChange = async () => {
          if (!token || !store_id) return;

          try {
            const newStatus =
              status === PaymentStatus.Paid
                ? PaymentStatus.Unpaid
                : PaymentStatus.Paid;

            // Conditionally set payment_date based on the new status
            const updateData: Partial<Invoice> = {
              payment_status: newStatus,
            };
            if (newStatus === PaymentStatus.Paid) {
              updateData.payment_date = new Date().toISOString();
            } else {
              updateData.payment_date = undefined;
            }

            await updateInvoice(token, store_id, invoice.id, updateData as Invoice);
            router.refresh(); // Refresh the data
          } catch (error) {
            console.error("Error updating payment status:", error);
          }
        };

        return (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto hover:bg-transparent"
            onClick={handleStatusChange}
          >
            {status === PaymentStatus.Paid ? (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            ) : (
              <Circle className="h-4 w-4 text-red-500" />
            )}
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "total_amount",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Amount
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("total_amount"));
        return (
          <div className="text-sm font-medium">
            {amount.toLocaleString("en-US", {
              style: "currency",
              currency: "USD",
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const invoice = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(invoice.id)}
              >
                Copy Invoice ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  router.push(`/app/invoices/${invoice.id}`);
                }}
              >
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  router.push(`/app/invoices/${invoice.id}/edit`);
                }}
              >
                Edit Invoice
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}