import { ColumnDef } from "@tanstack/react-table"
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal, CheckCircle2, Circle } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Customer } from "@/lib/api/clients/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { format } from "date-fns";
import { updateInvoice, getInvoiceById } from "@/lib/api/invoices/services";
import { Invoice, PaymentStatus } from "@/lib/api/invoices/models";
// import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import Cookies from "js-cookie";
import { toast } from "sonner";

interface GetColumnsProps {
  customers: Customer[];
  router: AppRouterInstance;
  store: Store;
}

export function getColumns({ customers, router, store }: GetColumnsProps): ColumnDef<Invoice>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "invoice_number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Invoice Number
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const invoice = row.original;
        return (
          <Button
            variant="link"
            className="p-0 h-auto font-bold hover:no-underline"
            onClick={() => router.push(`/app/invoices/${invoice.id}`)}
          >
            {invoice.invoice_number}
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "customer_id",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Customer
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const customer = customers.find((c) => c.id === row.getValue("customer_id"));
        return <div className="text-sm">{customer?.name || "N/A"}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "invoice_date",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Date
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("invoice_date"));
        return <div className="text-sm">{format(date, "MMM d, yyyy")}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "due_date",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Due Date
          <ArrowUpDown />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue("due_date") || "N/A"}</div>,
      enableSorting: true,
    },
    {
      accessorKey: "payment_status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const invoice = row.original;
        const status = invoice.payment_status;
        const token = Cookies.get("auth_token");
        const store_id = Cookies.get("active_store");

        const handleStatusChange = async () => {
          if (!token || !store_id) return;

          try {
            const newStatus =
              status === PaymentStatus.Paid
                ? PaymentStatus.Unpaid
                : PaymentStatus.Paid;

            // First fetch the current invoice data
            const currentInvoice = await getInvoiceById(token, store_id, invoice.id);

            // Create the updated invoice with all required fields
            const updatedInvoice: Invoice = {
              ...currentInvoice,
              payment_status: newStatus
            };

            await updateInvoice(token, store_id, invoice.id, updatedInvoice);
            
            // Show success toast
            toast.success(`Invoice status updated to ${newStatus}`);
            
            // Force a state change by updating the URL with a timestamp
            const timestamp = new Date().getTime();
            router.push(`/app/invoices?t=${timestamp}`);
            
            // Refresh the data
            router.refresh();
          } catch (error) {
            console.error("Error updating payment status:", error);
            toast.error("Failed to update invoice status");
          }
        };

        return (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto hover:bg-transparent"
            onClick={handleStatusChange}
          >
            {status === PaymentStatus.Paid ? (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            ) : (
              <Circle className="h-4 w-4 text-red-500" />
            )}
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "total_amount",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Amount
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: async ({ row }) => {
        const amount = parseFloat(row.getValue("total_amount"));
        

        return (
          <div className="text-sm font-medium">
            {amount.toLocaleString("en-US", {
              style: "currency",
              currency: store.currency,
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const invoice = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(invoice.id)}>
                Copy Invoice ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  router.push(`/app/invoices/${invoice.id}`)
                }}
              >View Details</DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  router.push(`/app/invoices/${invoice.id}/edit`);
                }}
              >
                Edit Invoice
              </DropdownMenuItem>

            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}