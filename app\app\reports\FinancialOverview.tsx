import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { MonthlyFinancialData } from '@/lib/api/reports/models';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface FinancialOverviewProps {
    financialData: MonthlyFinancialData[] | null;
}

const FinancialOverview = ({ financialData }: FinancialOverviewProps) => {
  if (!financialData) {
    return <div>Loading... or Error</div>;
  }

  const labels = financialData.map(item => item.month);
  const profitData = financialData.map(item => item.profit);
  const lossData = financialData.map(item => item.loss);

  const chartData = {
    labels: labels,
    datasets: [
      {
        label: 'Profit',
        data: profitData,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
      },
      {
        label: 'Loss',
        data: lossData,
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Monthly Profit/Loss',
      },
    },
  };

  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Financial Overview</h2>
      <Line options={options} data={chartData} />
    </div>
  );
};

export default FinancialOverview;