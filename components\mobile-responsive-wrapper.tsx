"use client";

import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface MobileResponsiveWrapperProps {
  children: ReactNode;
  className?: string;
  variant?: "page" | "card" | "form" | "table" | "dialog";
  padding?: "none" | "sm" | "md" | "lg";
  maxWidth?: "none" | "sm" | "md" | "lg" | "xl" | "full";
}

/**
 * A wrapper component that provides consistent mobile-responsive styling
 * across the application. Optimized for Capacitor mobile apps.
 */
export function MobileResponsiveWrapper({
  children,
  className,
  variant = "page",
  padding = "md",
  maxWidth = "full",
}: MobileResponsiveWrapperProps) {
  const baseClasses = "w-full";
  
  const variantClasses = {
    page: "min-h-full",
    card: "bg-card border rounded-lg sm:rounded-xl shadow-sm",
    form: "space-y-4 sm:space-y-6",
    table: "overflow-x-auto",
    dialog: "max-h-[90vh] overflow-y-auto",
  };

  const paddingClasses = {
    none: "",
    sm: "p-2 sm:p-3",
    md: "p-3 sm:p-4 md:p-6",
    lg: "p-4 sm:p-6 md:p-8",
  };

  const maxWidthClasses = {
    none: "",
    sm: "max-w-sm mx-auto",
    md: "max-w-md mx-auto",
    lg: "max-w-lg mx-auto",
    xl: "max-w-xl mx-auto",
    full: "max-w-full",
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        paddingClasses[padding],
        maxWidthClasses[maxWidth],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Mobile-optimized button component with larger touch targets
 */
interface MobileButtonProps {
  children: ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
  disabled?: boolean;
  className?: string;
}

export function MobileButton({
  children,
  onClick,
  variant = "primary",
  size = "md",
  fullWidth = false,
  disabled = false,
  className,
}: MobileButtonProps) {
  const baseClasses = "inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50";
  
  const variantClasses = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    ghost: "hover:bg-accent hover:text-accent-foreground",
  };

  const sizeClasses = {
    sm: "h-9 px-3 text-sm",
    md: "h-11 px-4 text-base sm:h-10 sm:px-4 sm:text-sm", // Larger on mobile
    lg: "h-12 px-6 text-lg sm:h-11 sm:px-6 sm:text-base", // Larger on mobile
  };

  const widthClasses = fullWidth ? "w-full" : "";

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        widthClasses,
        className
      )}
    >
      {children}
    </button>
  );
}

/**
 * Mobile-optimized input component with larger touch targets
 */
interface MobileInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  type?: "text" | "email" | "password" | "number" | "tel";
  disabled?: boolean;
  className?: string;
  label?: string;
  error?: string;
}

export function MobileInput({
  placeholder,
  value,
  onChange,
  type = "text",
  disabled = false,
  className,
  label,
  error,
}: MobileInputProps) {
  const inputClasses = "flex h-12 sm:h-10 w-full rounded-lg border border-input bg-background px-4 py-3 sm:px-3 sm:py-2 text-base sm:text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50";

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        className={cn(inputClasses, className)}
      />
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}

/**
 * Mobile-optimized grid component
 */
interface MobileGridProps {
  children: ReactNode;
  cols?: 1 | 2 | 3 | 4;
  gap?: "sm" | "md" | "lg";
  className?: string;
}

export function MobileGrid({
  children,
  cols = 1,
  gap = "md",
  className,
}: MobileGridProps) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
  };

  const gapClasses = {
    sm: "gap-2 sm:gap-3",
    md: "gap-3 sm:gap-4",
    lg: "gap-4 sm:gap-6",
  };

  return (
    <div
      className={cn(
        "grid",
        gridClasses[cols],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Mobile-optimized card component
 */
interface MobileCardProps {
  children: ReactNode;
  title?: string;
  description?: string;
  className?: string;
  padding?: "sm" | "md" | "lg";
}

export function MobileCard({
  children,
  title,
  description,
  className,
  padding = "md",
}: MobileCardProps) {
  const paddingClasses = {
    sm: "p-3 sm:p-4",
    md: "p-4 sm:p-6",
    lg: "p-6 sm:p-8",
  };

  return (
    <div
      className={cn(
        "bg-card border rounded-lg sm:rounded-xl shadow-sm",
        paddingClasses[padding],
        className
      )}
    >
      {(title || description) && (
        <div className="mb-4 sm:mb-6">
          {title && (
            <h3 className="text-lg sm:text-xl font-semibold leading-none tracking-tight">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm sm:text-base text-muted-foreground mt-2">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
}
