"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ExchangeForm } from "@/app/app/items/exchanges/ExchangeForm";
import { Item } from "@/lib/api/items/models";
import { fetchItems } from "@/lib/api/items/service";
import { createExchange } from "@/lib/api/items/exchanges/service";
import { ExchangeSchema } from "@/lib/api/items/exchanges/models";
import { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";
import { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";

export default function CreateExchangePage() {
  const [items, setItems] = useState<Item[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const fetchedItems = await fetchItems(token, store_id);
          setItems(fetchedItems);
          const fetchedReceipts = await fetchReceipts(token, store_id);
          setReceipts(fetchedReceipts);
          const fetchedSales = await fetchSales(token, store_id);
          setSales(fetchedSales);
        } catch (error) {
          console.error("Failed to fetch items:", error);
        }
      }
    };
    fetchData();
  }, [token, store_id]);


  const handleSubmit = async (data: ExchangeSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await createExchange(token, store_id, data);
  };

  return (
    <ExchangeForm
      onSubmit={handleSubmit}
      items={items}
      receipts={receipts}
      sales={sales}
    />
  );
}
