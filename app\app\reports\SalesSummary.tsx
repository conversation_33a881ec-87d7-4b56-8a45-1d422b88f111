import React from 'react';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { DailyTotalSales, ProductPerformance } from "@/lib/api/reports/models";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const monthLabels = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

interface SalesSummaryProps {
  salesData: DailyTotalSales[] | null;
  productData: ProductPerformance[] | null;
}

const SalesSummary = ({ salesData, productData }: SalesSummaryProps) => {
    if (!salesData || !productData) {
        return <div>Loading... or Error</div>;
    }

    const salesByMonth = Array(12).fill(0);
    salesData.forEach(({ date, total }) => {
        const monthIndex = new Date(date).getMonth();
        salesByMonth[monthIndex] += total;
    });

    const categorySales = {
        labels: productData.map(item => item.category),
        data: productData.map(item => item.quantity),
    };

  const lineChartData = {
    labels: monthLabels,
    datasets: [
      {
        label: 'Sales',
        data: salesByMonth,
        borderColor: 'rgb(53, 162, 235)',
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
      },
      {
        label: 'Target',
        data: Array(12).fill(1000),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        borderDash: [5, 5],
      },
    ],
  };

  const barChartData = {
    labels: categorySales.labels,
    datasets: [
      {
        label: 'Sales',
        data: categorySales.data,
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' as const },
    },
  };

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-lg font-semibold mb-4">Overall Sales Performance</h2>
      <div className="mb-6 flex-grow">
        <Line options={chartOptions} data={lineChartData} />
      </div>
      <div className="flex-grow">
        <Bar options={chartOptions} data={barChartData} />
      </div>
    </div>
  );
};

export default SalesSummary;