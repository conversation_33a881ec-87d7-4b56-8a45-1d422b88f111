// lib/api/users/onboarding/models.ts

export interface StoreOnboardingData {
    address: string;
    category: string;
    country: string;
    currency: string;
    city: string;
    email: string;
    name: string;
    opening_hours:string;
    phone_number: string;
    status: string; // Keep status, but manage it server-side
  }

  export interface UserOnboardingData {
    email: string;
    name: string;
    password: string;  // Keep for now.  Separate authentication flow recommended.
    plan_id: number;
    profile: string;
    role_id: number;
    store_id: string;
    user_defined_role_id: number;
  }

  export interface OnboardingData {
    store: StoreOnboardingData;
    user: UserOnboardingData;
  }

  export interface OnboardingResponse {
    success: boolean;
    message?: string;
    storeId?: string;
    userId?: string;
  }
