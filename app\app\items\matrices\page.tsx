"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, Loader2, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

import { getColumns } from "./columns";
import Cookies from "js-cookie";
import { Brand } from "@/lib/api/brands/models";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchCategories } from "@/lib/api/categories/service";
import { Category } from "@/lib/api/categories/models";
import { ItemMatrix } from "@/lib/api/items/matrices/models";
import { deleteItemMatrix, getItemMatrices } from "@/lib/api/items/matrices/service";
import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import { ScrollArea } from "@/components/ui/scroll-area";

function LoadingSpinner() {
    return (
        <div className="flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin" />
        </div>
    );
}

function ErrorAlert({ error, setError }: { error: string | null, setError: (error: string | null) => void }) {
    if (!error) return null;

    return (
        <Alert variant="error" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
        </Alert>
    );
}

export default function Page() {
    const isMobile = useMediaQuery("(max-width: 768px)");
    const router = useRouter();
    const [data, setItemMatrices] = useState<ItemMatrix[]>([]);
    const [brands, setBrands] = useState<Brand[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [store, setStore] = useState<Store | null>(null);
    const [error, setError] = useState<string | null>(null);

    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = useState({});
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(10);

    const token = Cookies.get("auth_token");
    const store_id = Cookies.get("active_store");

    useEffect(() => {
        async function getItemsMatrix() {
            if (token && store_id) {
                setIsInitialLoading(true);
                setError(null);
                try {
                    const [fetchedItemMatrices, fetchedBrands, fetchedCategories, fetchedStore] = await Promise.all([
                        getItemMatrices(token, store_id),
                        fetchBrands(token, store_id),
                        fetchCategories(token, store_id),
                        getStore(store_id, token)
                    ]);

                    setItemMatrices(fetchedItemMatrices);
                    setBrands(fetchedBrands);
                    setCategories(fetchedCategories);
                    setStore(fetchedStore);
                } catch (error: any) {
                    console.error("Error fetching data:", error);
                    setError(error.message || "Failed to load matrices. Please try again.");
                } finally {
                    setIsInitialLoading(false);
                }
            }
        }
        getItemsMatrix();
    }, [store_id, token]);

    const handleDeleteSelected = async () => {
        if (!token || !store_id) {
            setError("Authentication token or store ID is missing.");
            return;
        }
        const selectedRows = table.getSelectedRowModel().rows;
        const selectedMatricesIds = selectedRows.map((row) => row.original.id);

        try {
            for (const matrixId of selectedMatricesIds) {
                await deleteItemMatrix(token, matrixId, store_id);
            }
            setItemMatrices((prev) =>
                prev.filter((matrix) => !selectedMatricesIds.includes(matrix.id))
            );
            setRowSelection({});
            setIsDeleteDialogOpen(false);
            router.refresh();
        } catch (error: any) {
            console.error("Error deleting matrices:", error);
            setError(error.message || "Failed to delete matrices. Please try again.");
        }
    };

    // const columns = getColumns(brands, categories, router, store?.currency);
    const columns = useMemo(() => getColumns(brands, categories, router, store?.currency), [brands, categories, router, store?.currency]);

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
    const pageCount = Math.ceil(filteredRows.length / pageSize);
    const start = pageIndex * pageSize;
    const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

    const showSelectedItems = (
        <ul className="mt-2 space-y-1">
            {table.getSelectedRowModel().rows.map((row) => (
                <li key={row.id} className="text-sm text-muted-foreground">• {row.original.name}</li>
            ))}
        </ul>
    );

    return (
        <div className="w-full">
            <ErrorAlert error={error} setError={setError} />

            {/* Controls */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
                <Input
                    placeholder="Filter matrices..."
                    value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
                    onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
                    className="w-full sm:max-w-sm"
                    disabled={isInitialLoading}
                />

                <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0">
                    <Button
                        onClick={() => router.push("/app/items/matrices/create")}
                        className="w-full sm:w-auto"
                        disabled={isInitialLoading}
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        New Matrix
                    </Button>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                                Columns <ChevronDown className="ml-2 h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {table
                                .getAllColumns()
                                .filter((column) => column.getCanHide())
                                .map((column) => (
                                    <DropdownMenuCheckboxItem
                                        key={column.id}
                                        className="capitalize"
                                        checked={column.getIsVisible()}
                                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                                    >
                                        {column.id}
                                    </DropdownMenuCheckboxItem>
                                ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Desktop Table View */}
            {!isMobile && (
                <div className="rounded-md border relative w-full overflow-x-auto grid grid-cols-1">
                    <Table className="w-full min-w-[600px] caption-bottom text-sm">
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext(),
                                                )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {isInitialLoading ? (
                                <TableRow>
                                    <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                                        <LoadingSpinner />
                                    </TableCell>
                                </TableRow>
                            ) : paginatedRows.length ? (
                                paginatedRows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                                            <p>No matrices found.</p>
                                            <Button
                                                variant="link"
                                                onClick={() => router.push("/app/items/matrices/create")}
                                                className="mt-2"
                                            >
                                                Create your first matrix
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            )}

            {/* Mobile Card View */}
            {isMobile && (
                <div className="grid grid-cols-1 gap-4">
                    {isInitialLoading && <LoadingSpinner />}
                    {!isInitialLoading && paginatedRows.length === 0 && (
                        <div className="text-center text-muted-foreground p-4">No matrices found.</div>
                    )}
                    {paginatedRows.map(row => {
                        const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
                        const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
                        const dataCells = row.getVisibleCells().filter(
                            cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'name'
                        );
                        return (
                            <div
                                key={row.id}
                                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
                                data-state={row.getIsSelected() ? "selected" : undefined}
                            >
                                {/* Header: Name, Checkbox, Actions */}
                                <div className="flex justify-between items-start mb-2">
                                    <label className="flex items-center space-x-3">
                                        {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                                        <span
                                            className="font-bold text-lg cursor-pointer hover:text-primary break-words"
                                            onClick={() => router.push(`/app/items/matrices/${row.original.id}`)}
                                        >
                                            {row.getValue("name")}
                                        </span>
                                    </label>
                                    <div className="absolute top-2 right-2">
                                        {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                                    </div>
                                </div>
                                <hr className="border-border" />
                                {/* Details */}
                                <div className="space-y-3 pt-2">
                                    {dataCells.map(cell => (
                                        <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                                            <div className="font-medium text-muted-foreground">
                                                {formatHeader(cell.column.id)}
                                            </div>
                                            <div className="text-right break-words">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}

            {/* Pagination and actions */}
            <div className="flex items-center justify-between space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {isInitialLoading
                        ? "Loading..."
                        : `${table.getFilteredSelectedRowModel().rows.length} of ${table.getFilteredRowModel().rows.length} row(s) selected.`}
                </div>
                <TablePagination
                    pageIndex={pageIndex}
                    pageSize={pageSize}
                    pageCount={pageCount}
                    totalItems={filteredRows.length}
                    onPageChange={setPageIndex}
                    onPageSizeChange={setPageSize}
                />
            </div>
        </div>
    );
}