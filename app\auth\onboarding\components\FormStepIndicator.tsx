// app/auth/registration/components/FormStepIndicator.tsx

"use client";

import React from 'react';
import { CheckCircle } from 'lucide-react';

interface FormStepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps?: string[];
}

const FormStepIndicator: React.FC<FormStepIndicatorProps> = ({ 
  currentStep, 
  totalSteps,
  steps = ['User Details', 'Store Details'] 
}) => {
  return (
    <div className="w-full mb-8">
      <ol className="flex items-center w-full">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const isCompleted = index + 1 < currentStep;
          const isActive = index + 1 === currentStep;
          const isLast = index === totalSteps - 1;
          
          return (
            <li 
              key={index} 
              className={`flex items-center ${isLast ? 'w-auto' : 'w-full'}`}
            >
              <div className="flex flex-col items-center">
                <div className={`
                  flex items-center justify-center w-8 h-8 rounded-full 
                  ${isCompleted ? 'bg-green-500' : isActive ? 'bg-blue-600' : 'bg-gray-200'}
                  transition-colors duration-300
                `}>
                  {isCompleted ? (
                    <CheckCircle className="w-5 h-5 text-white" />
                  ) : (
                    <span className={`text-sm font-medium ${isActive ? 'text-white' : 'text-gray-600'}`}>
                      {index + 1}
                    </span>
                  )}
                </div>
                <span className={`
                  mt-2 text-xs font-medium
                  ${isActive ? 'text-blue-600 font-semibold' : isCompleted ? 'text-green-500' : 'text-gray-500'}
                `}>
                  {steps[index] || `Step ${index + 1}`}
                </span>
              </div>
              
              {!isLast && (
                <div className={`
                  w-full h-0.5 mx-2
                  ${index + 1 < currentStep ? 'bg-green-500' : 'bg-gray-200'}
                  transition-colors duration-300
                `} />
              )}
            </li>
          );
        })}
      </ol>
    </div>
  );
};

export default FormStepIndicator;



