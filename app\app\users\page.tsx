"use client";

import * as React from "react";
import { Plus, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useRouter } from "next/navigation";
import { getColumns } from "./columns";
import { UserInfoResponse } from "@/lib/api/users/models";
import { list_users } from "@/lib/api/users/service";
import { TablePagination } from "@/components/ui/table-pagination";
import Cookies from "js-cookie";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function Page() {
  const router = useRouter();
  const [data, setUsers] = React.useState<UserInfoResponse[]>([]);
  const [isInitialLoading, setIsInitialLoading] = React.useState(true);
  const [pageIndex, setPageIndex] = React.useState(0);
  const [pageSize, setPageSize] = React.useState(10);

  React.useEffect(() => {
    async function fetchData() {
      setIsInitialLoading(true);
      try {
        const token = Cookies.get("auth_token");
        const store_id = Cookies.get("active_store");

        if (!token || !store_id) {
          throw new Error("Authentication token or store ID is missing");
        }

        const users = await list_users(token, store_id);
        setUsers(users);
      } catch (error) {
        console.error("Error fetching users:", error);
      } finally {
        setIsInitialLoading(false);
      }
    }
    fetchData();
  }, []);

  const columns = getColumns(router);

  // Calculate pagination
  const pageCount = Math.ceil(data.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedData = data.slice(start, end);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Users</h2>
        <div className="flex items-center space-x-2">
          <Button onClick={() => router.push("/app/users/new")}>
            <Plus className="mr-2 h-4 w-4" />
            New User
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isInitialLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <LoadingSpinner />
                </TableCell>
              </TableRow>
            ) : paginatedData.length ? (
              paginatedData.map((row) => (
                <TableRow key={row.id}>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      {column.cell({ row: { original: row } })}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <TablePagination
        pageIndex={pageIndex}
        pageSize={pageSize}
        pageCount={pageCount}
        totalItems={data.length}
        onPageChange={setPageIndex}
        onPageSizeChange={setPageSize}
      />
    </div>
  );
}