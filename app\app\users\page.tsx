"use client";

import { useEffect, useState } from "react";
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";

import { getColumns } from "./columns";
import { User } from "@/lib/api/users/models";
import { delete_user, list_users } from "@/lib/api/users/service";

import Cookies from "js-cookie";

function LoadingSpinner() {
    return (
        <div className="flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin" />
        </div>
    );
}

export default function Page() {
    const router = useRouter();
    const [data, setUsers] = useState<User[]>([]);

    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [rowSelection, setRowSelection] = useState({});
    const [isInitialLoading, setIsInitialLoading] = useState(true); // Add loading state

    const token = Cookies.get("auth_token");
    const store_id = Cookies.get("active_store");

    useEffect(() => {
        async function getUsers() {
            if (token && store_id) {
                setIsInitialLoading(true); // Start loading
                try {
                    const fetchedUsers = await list_users(token, store_id);
                    setUsers(fetchedUsers);
                } catch (error) {
                    console.error("Error fetching suppliers:", error);
                } finally {
                    setIsInitialLoading(false); // Stop loading
                }
            }
        }
        getUsers();
    }, [store_id, token]);

    const handleDeleteSelected = async () => {
        if (!token || !store_id) {
            console.error("Authentication token or store ID is missing.");
            return;
        }
        const selectedRows = table.getSelectedRowModel().rows;
        const selectedUserIds = selectedRows.map((row) => row.original.id);

        try {
            for (const customerId of selectedUserIds) {
                // await deleteUser(token, store_id, customerId);
                await delete_user(token, store_id, selectedUserIds[0]);
                setIsDeleteDialogOpen(false);
                router.refresh();
            }
            setUsers((prev) =>
                prev.filter((customer) => !selectedUserIds.includes(customer.id)),
            );
            setRowSelection({});
            setIsDeleteDialogOpen(false);
        } catch (error) {
            console.error("Error deleting customers:", error);
        }
    };

    const columns = getColumns(router);

    const table = useReactTable({
        defaultColumn: {
            minSize: 50,
            maxSize: 400,
            size: 180,
        },
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });
    const showSelectedItems = (
        <ul>
            {table
                .getSelectedRowModel()
                .rows.map((row) => (
                    <li key={row.id}>- {row.original.name}</li>
                ))}
        </ul>
    );

    return (
        <div className="w-full">
            {/* Top Section: Input, New Staff Button, and Columns Dropdown */}
            <div className="flex flex-col sm:flex-row items-center justify-between gap-8 py-4">
                <Input
                    placeholder="Filter users..."
                    value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
                    onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
                    className="max-w-sm"
                    disabled={isInitialLoading} // Disable input while loading
                />

                <div className="flex gap-2">
                    <Button
                        onClick={() => router.push("/app/users/create")}
                        className="w-full sm:w-auto"
                        disabled={isInitialLoading} // Disable button while loading
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        New Staff
                    </Button>
                </div>

                {/* Columns Dropdown moved to the far right */}
                <div className="ml-auto">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" disabled={isInitialLoading}> {/* Disable button while loading */}
                                Columns <ChevronDown />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {table
                                .getAllColumns()
                                .filter((column) => column.getCanHide())
                                .map((column) => (
                                    <DropdownMenuCheckboxItem
                                        key={column.id}
                                        className="capitalize"
                                        checked={column.getIsVisible()}
                                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                                    >
                                        {column.id}
                                    </DropdownMenuCheckboxItem>
                                ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Table */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table
                            .getHeaderGroups()
                            .map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext(),
                                                )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                    </TableHeader>
                    <TableBody>
                        {isInitialLoading ? (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    <LoadingSpinner />
                                </TableCell>
                            </TableRow>
                        ) : table.getRowModel().rows?.length ? (
                            table
                                .getRowModel()
                                .rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext(),
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Delete Button and Pagination at the Bottom */}
            <div className="flex items-center justify-between space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {table.getFilteredSelectedRowModel().rows.length} of{" "}
                    {table.getFilteredRowModel().rows.length} row(s) selected.
                </div>
                <div className="space-x-2">
                    {Object.keys(rowSelection).length > 0 && (
                        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                            <DialogTrigger asChild>
                                <Button variant="destructive" size="sm" disabled={isInitialLoading}> {/* Disable button while loading */}
                                    <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Delete Users</DialogTitle>
                                    <DialogDescription>
                                        Are you sure you want to delete the selected users(s)?
                                        {showSelectedItems}
                                        <b>This action cannot be undone. </b>
                                    </DialogDescription>
                                </DialogHeader>
                                <div className="flex justify-end space-x-2">
                                    <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                                        Cancel
                                    </Button>
                                    <Button variant="destructive" onClick={handleDeleteSelected}>
                                        Delete
                                    </Button>
                                </div>
                            </DialogContent>
                        </Dialog>
                    )}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.previousPage()}
                        disabled={!table.getCanPreviousPage() || isInitialLoading}
                    >
                        Previous
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.nextPage()}
                        disabled={!table.getCanNextPage() || isInitialLoading}
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    );
}