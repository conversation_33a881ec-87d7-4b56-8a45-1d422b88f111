"use client";

import * as React from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import { Plus, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useRouter } from "next/navigation";
import { getColumns } from "./columns";
import { UserInfoResponse } from "@/lib/api/users/models";
import { list_users } from "@/lib/api/users/service";
import { TablePagination } from "@/components/ui/table-pagination";
import Cookies from "js-cookie";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function Page() {
  const router = useRouter();
  const [data, setUsers] = React.useState<UserInfoResponse[]>([]);
  const [isInitialLoading, setIsInitialLoading] = React.useState(true);
  const [pageIndex, setPageIndex] = React.useState(0);
  const [pageSize, setPageSize] = React.useState(10);

  React.useEffect(() => {
    async function fetchData() {
      setIsInitialLoading(true);
      try {
        const token = Cookies.get("auth_token");
        const store_id = Cookies.get("active_store");

        if (!token || !store_id) {
          throw new Error("Authentication token or store ID is missing");
        }

        const users = await list_users(token, store_id);
        setUsers(users);
      } catch (error) {
        console.error("Error fetching users:", error);
      } finally {
        setIsInitialLoading(false);
      }
    }
    fetchData();
  }, []);

  const columns = getColumns(router);
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Calculate pagination
  const pageCount = Math.ceil(data.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedData = data.slice(start, end);
  // Filter state for search input
  const [filter, setFilter] = React.useState("");
  // Filtered data for search
  const filteredData = filter
    ? data.filter(
        (user) =>
          (user.name && user.name.toLowerCase().includes(filter.toLowerCase())) ||
          (user.email && user.email.toLowerCase().includes(filter.toLowerCase()))
      )
    : data;
  const pageCountFiltered = Math.ceil(filteredData.length / pageSize);
  const paginatedFilteredData = filteredData.slice(start, end);

  return (
    <div className="w-full">
      {/* --- Header section matching customers page --- */}
      <div className="flex flex-col items-start sm:flex-row sm:items-center gap-4 py-4">
        <input
          type="text"
          placeholder="Filter users..."
          value={filter}
          onChange={(e) => {
            setFilter(e.target.value);
            setPageIndex(0);
          }}
          className="w-full sm:max-w-sm border rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
          disabled={isInitialLoading}
        />
        <Button
          onClick={() => router.push("/app/users/new")}
          className="w-full sm:w-auto mt-2 sm:mt-0"
          disabled={isInitialLoading}
        >
          <Plus className="mr-2 h-4 w-4" />
          New User
        </Button>
      </div>

      {/* --- START OF CONDITIONAL RENDERING --- */}

      {/*-- TABLE VIEW FOR DESKTOP --*/}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-auto">
          <Table className="w-full caption-bottom text-sm">
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column.id}>{column.header}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <LoadingSpinner />
                  </TableCell>
                </TableRow>
              ) : paginatedFilteredData.length ? (
                paginatedFilteredData.map((row) => (
                  <TableRow key={row.id}>
                    {columns.map((column) => (
                      <TableCell key={column.id}>
                        {column.cell({ row: { original: row } })}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/*-- CARD VIEW FOR MOBILE (USERS) --*/}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedFilteredData.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No results.</div>
          )}
          {paginatedFilteredData.map((row) => {
            // Find the special cells for custom placement
            const actionsColumn = columns.find((col) => col.id === 'actions');
            // Filter out special cells AND the name/id to render the rest as key-value pairs
            const dataColumns = columns.filter(
              (col) => col.id !== 'actions' && col.id !== 'name' && col.id !== 'id'
            );
            return (
              <div
                key={row.id}
                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
              >
                {/* Part 1: Header with Name and Actions Menu */}
                <div className="flex justify-between items-start mb-2">
                  <span className="font-bold text-lg break-words">
                    {row.name || row.email || row.id}
                  </span>
                  <div className="absolute top-2 right-2">
                    {actionsColumn && actionsColumn.cell({ row: { original: row } })}
                  </div>
                </div>
                <hr className="border-border" />
                {/* Part 2: Details with proper alignment */}
                <div className="space-y-3 pt-2">
                  {dataColumns.map((col) => (
                    <div key={col.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(col.id)}
                      </div>
                      <div className="text-right break-words">
                        {col.cell({ row: { original: row } })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
      {/* --- END OF CONDITIONAL RENDERING --- */}

      {/* Mobile-Responsive Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-2 py-4">
        <div className="text-sm text-muted-foreground text-center sm:text-left">
          {isInitialLoading
            ? "Loading..."
            : `${paginatedFilteredData.length} of ${filteredData.length} user(s) shown.`}
        </div>

        {/* Desktop Pagination */}
        {!isMobile && (
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCountFiltered}
            totalItems={filteredData.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        )}

        {/* Mobile Pagination */}
        {isMobile && (
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => setPageIndex(Math.max(0, pageIndex - 1))}
              disabled={pageIndex === 0 || isInitialLoading}
            >
              Previous
            </Button>
            <span className="text-sm font-medium">
              Page {pageIndex + 1} of {Math.max(1, pageCountFiltered)}
            </span>
            <Button
              onClick={() => setPageIndex(Math.min(pageCountFiltered - 1, pageIndex + 1))}
              disabled={pageIndex >= pageCountFiltered - 1 || isInitialLoading}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}