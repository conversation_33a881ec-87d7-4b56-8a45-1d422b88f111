# Mobile Responsive Guide for StoreYako

This guide provides comprehensive mobile responsiveness patterns optimized for your Capacitor mobile app.

## Quick Start

### 1. Use the MobileResponsiveWrapper

```tsx
import { MobileResponsiveWrapper } from "@/components/mobile-responsive-wrapper";

export default function MyPage() {
  return (
    <MobileResponsiveWrapper variant="page" padding="md">
      <h1 className="text-mobile-xl font-bold mb-4">My Page</h1>
      {/* Your content */}
    </MobileResponsiveWrapper>
  );
}
```

### 2. Use Mobile-Optimized Components

```tsx
import { 
  MobileButton, 
  MobileInput, 
  MobileGrid, 
  MobileCard 
} from "@/components/mobile-responsive-wrapper";

export default function MyForm() {
  return (
    <MobileResponsiveWrapper variant="form">
      <MobileCard title="User Information" padding="md">
        <MobileGrid cols={2} gap="md">
          <MobileInput 
            label="First Name" 
            placeholder="Enter first name"
          />
          <MobileInput 
            label="Last Name" 
            placeholder="Enter last name"
          />
        </MobileGrid>
        
        <MobileButton variant="primary" fullWidth>
          Save Changes
        </MobileButton>
      </MobileCard>
    </MobileResponsiveWrapper>
  );
}
```

## Layout Improvements Made

### 1. Header Optimizations
- **Mobile**: Reduced height from `h-16` to `h-14`
- **Touch Targets**: Larger sidebar trigger button
- **Store Switcher**: Limited width on mobile to prevent overflow
- **Avatar**: Responsive sizing with proper flex-shrink

### 2. Content Area Optimizations
- **Height Calculation**: Adjusted for smaller mobile header
- **Padding**: Mobile-first approach with responsive scaling
- **Overflow Prevention**: Added max-width container

### 3. Sidebar Improvements
- **State Persistence**: Menu items now remember their open/closed state
- **Mobile Sheet**: Optimized for touch interactions

## CSS Utility Classes

### Touch-Friendly Sizing
```css
.touch-target          /* Minimum 44px touch target */
.btn-mobile-sm         /* Mobile-optimized small button */
.btn-mobile-md         /* Mobile-optimized medium button */
.btn-mobile-lg         /* Mobile-optimized large button */
.input-mobile          /* Mobile-optimized input height */
```

### Typography
```css
.text-mobile-xs        /* Responsive extra small text */
.text-mobile-sm        /* Responsive small text */
.text-mobile-base      /* Responsive base text */
.text-mobile-lg        /* Responsive large text */
.text-mobile-xl        /* Responsive extra large text */
```

### Spacing
```css
.space-mobile-xs       /* Responsive extra small spacing */
.space-mobile-sm       /* Responsive small spacing */
.space-mobile-md       /* Responsive medium spacing */
.space-mobile-lg       /* Responsive large spacing */
.p-mobile-xs           /* Responsive extra small padding */
.p-mobile-sm           /* Responsive small padding */
.p-mobile-md           /* Responsive medium padding */
.p-mobile-lg           /* Responsive large padding */
```

### Layout Helpers
```css
.mobile-container      /* Prevents horizontal overflow */
.mobile-dialog         /* Full-screen on mobile, modal on desktop */
.mobile-table-wrapper  /* Horizontal scroll for tables */
.mobile-card-grid      /* Responsive card grid */
.mobile-form-row       /* Responsive form layout */
```

## Common Patterns

### 1. Page Layout
```tsx
<MobileResponsiveWrapper variant="page" padding="md">
  <div className="space-mobile-lg">
    <h1 className="text-mobile-xl font-bold">Page Title</h1>
    <MobileGrid cols={2} gap="md">
      {/* Content */}
    </MobileGrid>
  </div>
</MobileResponsiveWrapper>
```

### 2. Form Layout
```tsx
<MobileResponsiveWrapper variant="form" maxWidth="lg">
  <MobileCard title="Form Title" padding="lg">
    <div className="space-mobile-md">
      <div className="mobile-form-row">
        <MobileInput label="Field 1" />
        <MobileInput label="Field 2" />
      </div>
      <MobileButton variant="primary" fullWidth>
        Submit
      </MobileButton>
    </div>
  </MobileCard>
</MobileResponsiveWrapper>
```

### 3. Table Layout
```tsx
<MobileResponsiveWrapper variant="table">
  <div className="mobile-table-wrapper">
    <table className="w-full">
      {/* Table content */}
    </table>
  </div>
</MobileResponsiveWrapper>
```

### 4. Dialog/Modal Layout
```tsx
<Dialog>
  <DialogContent className="mobile-dialog">
    <MobileResponsiveWrapper variant="dialog" padding="lg">
      {/* Dialog content */}
    </MobileResponsiveWrapper>
  </DialogContent>
</Dialog>
```

## Best Practices

### 1. Touch Targets
- Minimum 44px height/width for interactive elements
- Use `touch-target` class for small icons/buttons
- Provide adequate spacing between touch targets

### 2. Typography
- Use `text-mobile-*` classes for responsive text sizing
- Larger text on mobile for better readability
- Maintain proper line heights

### 3. Spacing
- Use `space-mobile-*` and `p-mobile-*` for consistent spacing
- More generous spacing on mobile
- Responsive scaling for larger screens

### 4. Navigation
- Persistent sidebar state for better UX
- Touch-friendly menu items
- Clear visual hierarchy

### 5. Forms
- Larger input fields on mobile (`input-mobile`)
- Full-width buttons on mobile
- Proper form validation feedback

### 6. Tables
- Always wrap tables in `mobile-table-wrapper`
- Consider card layouts for complex data on mobile
- Horizontal scrolling for wide tables

## Capacitor-Specific Optimizations

### 1. Safe Areas
```css
.mobile-safe-area-top     /* Accounts for status bar */
.mobile-safe-area-bottom  /* Accounts for home indicator */
```

### 2. Native Feel
- Larger touch targets (44px minimum)
- Smooth transitions and animations
- Platform-appropriate styling

### 3. Performance
- Optimized for mobile rendering
- Minimal layout shifts
- Efficient CSS classes

## Migration Strategy

1. **Start with Layout**: Update your main layout file (already done)
2. **Wrap Pages**: Use `MobileResponsiveWrapper` for page containers
3. **Update Forms**: Replace standard inputs/buttons with mobile components
4. **Fix Tables**: Wrap tables in mobile-table-wrapper
5. **Test on Device**: Always test on actual mobile devices

## Testing Checklist

- [ ] Touch targets are at least 44px
- [ ] Text is readable without zooming
- [ ] Forms are easy to fill out
- [ ] Tables scroll horizontally when needed
- [ ] Navigation is intuitive
- [ ] No horizontal overflow
- [ ] Proper spacing on all screen sizes
- [ ] Fast loading and smooth interactions
