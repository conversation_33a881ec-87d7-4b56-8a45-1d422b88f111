// app/app/items/page.tsx
"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import dynamic from "next/dynamic";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, ShoppingCart, Loader2, RefreshCw, Scan, X, MoreHorizontal } from "lucide-react"; // Added RefreshCw

// Dynamic import for barcode scanner (SSR safe)
const BarcodeScanner = dynamic(
  () => import("react-qr-barcode-scanner"),
  { ssr: false }
);
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import Cookies from "js-cookie";
import { Item } from "@/lib/api/items/models";
import { Brand } from "@/lib/api/brands/models";
import { fetchItems, fetchItemByBarcode } from "@/lib/api/items/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchCategories } from "@/lib/api/categories/service";
import { Category } from "@/lib/api/categories/models";
import { useRouter } from "next/navigation";
import { PaymentMethod, SellItemSchema, SellItemsSchema } from "@/lib/api/sales/models";
import { sellItems, SellItemsResult } from "@/lib/api/sales/service";
import { useWebSocket, PaymentStatusMessage } from "@/lib/hooks/useWebSocket"; // Adjust path if needed
import { getColumns } from "./columns"; // Adjust path if needed
import { CheckoutDialog } from "../../components/CheckoutDialog"; // Adjust path if needed
import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import { useMediaQuery } from "@/hooks/use-media-query";
import toast from 'react-hot-toast';

// --- Helper Components ---
function LoadingSpinner() {
  return <Loader2 className="h-8 w-8 animate-spin text-primary" />;
}

function ModalOverlay({ children }: { children: React.ReactNode }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50">
      <div className="bg-background p-4 rounded-lg shadow-xl border">
          {children}
          <p className="text-center text-sm mt-2 text-muted-foreground">Processing...</p>
      </div>
    </div>
  );
}

// A helper function to format the column ID into a readable header
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

// --- Cart Item Definition ---
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  has_discount: boolean;
  discount: number;
}

// --- Main Page Component ---
export default function Page() {
  const router = useRouter();

  // Mobile detection
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Data state
  const [data, setItems] = useState<Item[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [store, setStore] = useState<Store | null>(null);
  // UI State
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  // Barcode Scanner State
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [lastScannedCode, setLastScannedCode] = useState<string>("");
  const [cameraError, setCameraError] = useState<string>("");
  const [cameraReady, setCameraReady] = useState(false);
  // Payment State
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [mpesaPhoneNumber, setMpesaPhoneNumber] = useState("");
  const [itemDiscounts, setItemDiscounts] = useState<{ [itemId: string]: boolean }>({});
  // Tanstack Table State
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  // Pagination State
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  // Auth & Store Context
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  // WebSocket State
  const { connectionStatus, lastMessage, reconnect } = useWebSocket(!!token);
  const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);

  // --- Data Refresh Function ---
  const refreshItemsData = useCallback(() => {
    if (token && store_id) {
      fetchItems(token, store_id)
        .then(setItems)
        .catch(e => {
          console.error("Failed to refresh items", e);
          toast.error("Could not refresh item list.");
        });
    }
  }, [token, store_id]);

  // --- Cart Management Functions ---
  const addToCart = useCallback((item: Item) => {
    if (item.quantity === 0) { toast.error(`${item.name} is out of stock.`); return; };
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        if (existingItem.quantity >= item.quantity) { toast.error(`Maximum stock (${item.quantity}) for ${item.name} reached.`); return prevCart; }
        toast.success(`${item.name} quantity updated.`);
        return prevCart.map(cartItem => cartItem.id === item.id ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem );
      }
      toast.success(`${item.name} added to cart.`);
      return [...prevCart, { id: item.id, name: item.name, price: item.default_price, quantity: 1, image: item.image, has_discount: item.has_discount, discount: item.discount || 0 }];
    });
  }, []);

  const updateCartItemQuantity = useCallback((itemId: string, quantity: number) => {
    const itemInStock = data.find(item => item.id === itemId);
    if (!itemInStock) return;
    setCart(prevCart => prevCart.map(cartItem => cartItem.id === itemId ? { ...cartItem, quantity: Math.max(1, Math.min(quantity, itemInStock.quantity)) } : cartItem ));
  }, [data]);

  const updateCartItemPrice = useCallback((itemId: string, price: number) => {
    setCart(prevCart => prevCart.map(item => item.id === itemId ? { ...item, price: Math.max(0, price) } : item ));
  }, []);

  const removeFromCart = useCallback((itemId: string) => {
    setCart(prevCart => {
      const itemToRemove = prevCart.find(item => item.id === itemId);
      const newCart = prevCart.filter(item => item.id !== itemId);
      if (itemToRemove) { toast.success(`${itemToRemove.name} removed.`); }
      if (newCart.length === 0) { setIsCartOpen(false); }
      return newCart;
    });
  }, []);

  const toggleItemDiscount = useCallback((itemId: string) => {
    setItemDiscounts(prev => ({ ...prev, [itemId]: !prev[itemId] }));
  }, []);

  const getItemTotal = useCallback((item: CartItem) => {
    const baseTotal = item.price * item.quantity;
    return (itemDiscounts[item.id] && item.has_discount && item.discount) ? baseTotal * (1 - item.discount / 100) : baseTotal;
  }, [itemDiscounts]);

  const cartTotal = useMemo(() => cart.reduce((total, item) => total + getItemTotal(item), 0), [cart, getItemTotal]);

  // --- Barcode Scanning Functions ---
  const handleBarcodeScanned = useCallback(async (barcode: string) => {
    if (!token || !store_id || isScanning || barcode === lastScannedCode) return;

    setIsScanning(true);
    setLastScannedCode(barcode);

    try {
      const item = await fetchItemByBarcode(token, store_id, barcode);
      addToCart(item);
      toast.success(`${item.name} added to cart via barcode!`);
      setIsScannerOpen(false);
    } catch (error: any) {
      console.error("Barcode scan error:", error);
      toast.error(`Item not found for barcode: ${barcode}`);
    } finally {
      setIsScanning(false);
      // Reset last scanned code after a delay to allow rescanning
      setTimeout(() => setLastScannedCode(""), 2000);
    }
  }, [token, store_id, isScanning, lastScannedCode, addToCart]);

  const openBarcodeScanner = useCallback(() => {
    // Open scanner immediately - let the permission request happen when user clicks "Enable Camera"
    setIsScannerOpen(true);
    setLastScannedCode("");
    setCameraError("");
    setCameraReady(false);

    // Set a timeout to mark camera as ready if it doesn't initialize within 5 seconds
    setTimeout(() => {
      if (!cameraReady) {
        console.log('Camera timeout reached, setting ready');
        setCameraReady(true);
      }
    }, 5000);
  }, [cameraReady]);

  const closeBarcodeScanner = useCallback(() => {
    setIsScannerOpen(false);
    setIsScanning(false);
    setLastScannedCode("");
    setCameraError("");
    setCameraReady(false);
  }, []);

  // --- Initial Data Fetching ---
  useEffect(() => {
    async function getInitialData() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const [fetchedItems, fetchedBrands, fetchedCategories, fetchedStore] = await Promise.all([
            fetchItems(token, store_id), fetchBrands(token, store_id), fetchCategories(token, store_id), getStore(store_id, token)
          ]);
          setItems(fetchedItems); setBrands(fetchedBrands); setCategories(fetchedCategories); setStore(fetchedStore);
        } catch (error: any) {
          console.error("Error fetching initial data:", error);
          toast.error(`Failed to load store data: ${error.message || 'Unknown error'}`);
        } finally {
          setIsInitialLoading(false);
        }
      } else {
         setIsInitialLoading(false);
         if (!token) toast.error("Authentication missing. Please log in.");
         else if (!store_id) toast.error("No active store selected.");
      }
    }
    getInitialData();
  }, [store_id, token]);

  // --- Item Deletion Logic ---
  const handleDeleteSelected = async () => {
     const selectedRows = table.getFilteredSelectedRowModel().rows;
     const count = selectedRows.length;
     if (count === 0 || !token || !store_id) return;

     setIsDeleteDialogOpen(false); // Close confirmation dialog if it was open

     toast((t) => ( // Confirmation toast
       <span className="flex flex-col gap-2 items-center">
         Delete {count} item(s)? This cannot be undone.
         <div className="flex gap-2 mt-1">
            <Button
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90 h-9 rounded-md px-3"
                onClick={async () => {
                toast.dismiss(t.id);
                setIsLoading(true);
                const deletingToastId = toast.loading(`Deleting ${count} item(s)...`);
                try {
                    // --- Replace with your actual DELETE API call(s) ---
                    console.log("Deleting:", selectedRows.map(r => r.original.id));
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network
                    // --- End Replace ---
                    toast.success(`${count} item(s) deleted.`, { id: deletingToastId });
                    refreshItemsData(); // Refresh the list
                    setRowSelection({}); // Clear selection
                } catch (error: any) {
                    toast.error(`Deletion failed: ${error.message || 'Unknown error'}`, { id: deletingToastId });
                } finally { setIsLoading(false); }
                }}
            > Confirm Delete </Button>
            <Button className="border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3" onClick={() => toast.dismiss(t.id)}>Cancel</Button>
         </div>
       </span>
     ), { duration: 10000 });
  };

  // --- Centralized Checkout API Call Logic ---
  const performCheckout = useCallback(async (itemsToSell: SellItemSchema[], totalAmount: number) => {
    let errorMsg: string | null = null;
    if (!token || !store_id) errorMsg = "Authentication missing.";
    else if (itemsToSell.length === 0) errorMsg = "Cart is empty.";
    else if (!paymentMethod) errorMsg = "Select a payment method.";
    else {
      const mpesaRegex = /^(?:254|\+254|0)?(7(?:(?:[0-9][0-9])|(?:[0-9][0-9])|(?:[0-9][0-9]))[0-9]{6})$/;
      if (paymentMethod === "mpesa" && (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber))) {
        errorMsg = "Enter a valid M-Pesa number (e.g., 2547...).";
      }
    }
    if (errorMsg) { toast.error(errorMsg); return { ok: false, status: 400, error: errorMsg } as SellItemsResult; }

    const sellItemsPayload: SellItemsSchema = { items: itemsToSell, payment_method: paymentMethod!, mpesa_payload: paymentMethod === "mpesa" ? { amount: totalAmount, phone_number: mpesaPhoneNumber } : null };

    setIsLoading(true);
    try {
      return await sellItems(token!, sellItemsPayload);
    } catch (error: any) {
      console.error("Checkout API call failed:", error);
      toast.error(`Checkout failed: ${error.message || 'Network error'}`);
      return { ok: false, status: 500, error: error.message || "API Error" } as SellItemsResult;
    } finally {
      setIsLoading(false);
    }
  }, [token, store_id, paymentMethod, mpesaPhoneNumber]);

  // --- Cart Checkout Handler ---
  const handleCheckout = async () => {
    if (!store_id) { toast.error("Store not selected."); return; }
    const itemsToSell: SellItemSchema[] = cart.map(item => ({ item_uuid: item.id, price: getItemTotal(item), quantity: item.quantity, store_id: store_id }));

    setPendingReceiptId(null);

    const result = await performCheckout(itemsToSell, cartTotal);

    if (result.ok) {
      if (result.status === 200) { // Sync Success
        toast.success(`Sale completed! Receipt: ${result.data.receipt_number}`);
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({}); setIsCartOpen(false);
        refreshItemsData();
      } else if (result.status === 202) { // Async Pending
        setPendingReceiptId(result.data.receiptId);
        toast.loading(`M-Pesa request sent. Check phone... (Ref: ${result.data.receiptId})`, { id: result.data.receiptId, duration: 45000 });
        setIsCartOpen(false);
      }
    }
  };

   // --- WebSocket Message Handling ---
  useEffect(() => {
    if (lastMessage && pendingReceiptId && lastMessage.type === 'payment_status' && lastMessage.receiptId === pendingReceiptId) {
      const currentPendingId = pendingReceiptId;
      setPendingReceiptId(null);
      toast.dismiss(currentPendingId);

      const { status, message, mpesaReceiptNumber } = lastMessage;
      if (status === 'completed') {
        toast.success(`Payment Completed! Ref: ${mpesaReceiptNumber || 'N/A'}`, { duration: 6000 });
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({});
        setTimeout(refreshItemsData, 100); // Refresh after state updates
      } else {
        toast.error(`Payment Failed: ${message || 'Transaction unsuccessful.'}`, { duration: 6000 });
        setPaymentMethod(null); // Reset payment method on failure
      }
    }
  }, [lastMessage, pendingReceiptId, refreshItemsData]);

  // --- Table Setup ---
  const columns = getColumns(brands, categories, router, addToCart, store?.currency);
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: { pageIndex, pageSize }
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater({ pageIndex, pageSize });
        setPageIndex(newPagination.pageIndex);
        setPageSize(newPagination.pageSize);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    enableRowSelection: true,
    meta: { addToCart }
  });

  // Pagination logic for mobile
  const filteredRows = table.getFilteredRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const paginatedRows = filteredRows.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize);

  // --- JSX Rendering ---
  return (
    <div className="w-full max-w-full p-3 sm:p-4 md:p-6 space-y-4 relative overflow-hidden">
      {isLoading && <ModalOverlay><LoadingSpinner /></ModalOverlay>}

      {/* Top Controls */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-2">
        <Input
          placeholder="Filter items by name..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm h-11 sm:h-9 text-base sm:text-sm"
          disabled={isInitialLoading || isLoading}
        />
        <div className="flex flex-col gap-2 sm:flex-row sm:gap-2 w-full sm:w-auto">
          <div className="flex gap-2">
            <Button
              onClick={() => router.push("/app/items/create")}
              disabled={isInitialLoading || isLoading}
              className="flex-1 sm:flex-none h-11 sm:h-9 text-base sm:text-sm"
            >
              <Plus className="mr-1.5 h-4 w-4" /> New Item
            </Button>
            <Button
              onClick={openBarcodeScanner}
              disabled={isInitialLoading || isLoading}
              className="flex-1 sm:flex-none h-11 sm:h-9 text-base sm:text-sm bg-secondary text-secondary-foreground hover:bg-secondary/80"
            >
              <Scan className="mr-1.5 h-4 w-4" /> Scan
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              variant={"outline"}
              onClick={() => setIsCartOpen(true)}
              disabled={isInitialLoading || isLoading || cart.length === 0}
              className="flex-1 sm:flex-none h-11 sm:h-9 text-base sm:text-sm"
            >
              <ShoppingCart className="mr-1.5 h-4 w-4" /> Cart ({cart.length})
            </Button>
            {!isMobile && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant={"outline"}
                    disabled={isInitialLoading || isLoading}
                    className="flex-1 sm:flex-none h-11 sm:h-9 text-base sm:text-sm"
                  >
                    Columns <ChevronDown className="ml-1.5 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {table.getAllColumns().filter((c) => c.getCanHide()).map((c) => (
                    <DropdownMenuCheckboxItem key={c.id} className="capitalize" checked={c.getIsVisible()} onCheckedChange={(v) => c.toggleVisibility(!!v)}> {c.id.replace(/_/g, ' ')} </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>

      {/* --- START OF CONDITIONAL RENDERING --- */}

      {/*-- TABLE VIEW FOR DESKTOP --*/}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-auto">
          <Table className="w-full caption-bottom text-sm">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <LoadingSpinner />
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No items found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/*-- CARD VIEW FOR MOBILE (ITEMS) --*/}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedRows.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No results.</div>
          )}
          {paginatedRows.map(row => {
            // Find the special cells for custom placement
            const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
            const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');

            // Filter out special cells AND the name to render the rest as key-value pairs
            const dataCells = row.getVisibleCells().filter(
              cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'name'
            );

            const item = row.original;

            return (
              <div
                key={row.id}
                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
                data-state={row.getIsSelected() ? "selected" : undefined}
              >
                {/* Part 1: Header with Name, Checkbox, and Dropdown Menu Only */}
                <div className="flex justify-between items-start mb-2">
                  <label className="flex items-center space-x-3 flex-1 mr-12">
                    {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                    <span
                      className="font-bold text-lg cursor-pointer hover:text-primary break-words"
                      onClick={() => router.push(`/app/items/${row.original.id}`)}
                    >
                      {row.getValue("name")}
                    </span>
                  </label>

                  {/* Only show the dropdown menu (MoreHorizontal) in the top right */}
                  <div className="absolute top-2 right-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => {
                            router.push(`/app/items/${item.id}`);
                          }}
                        >
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            router.push(`/app/items/${item.id}/edit`);
                          }}
                        >
                          Edit Item
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Divider line for structure */}
                <hr className="border-border" />

                {/* Part 2: Details with proper alignment */}
                <div className="space-y-3 pt-2">
                  {dataCells.map(cell => (
                    <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(cell.column.id)}
                      </div>
                      <div className="text-right break-words">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </div>
                  ))}

                  {/* Add to Cart Button in Content Section */}
                  <div className="pt-3 border-t border-border">
                    <Button
                      onClick={() => addToCart(item)}
                      disabled={item.quantity === 0}
                      className="w-full h-12 text-base font-semibold"
                    >
                      <ShoppingCart className="h-5 w-5 mr-2" />
                      {item.quantity === 0 ? 'Out of Stock' : 'Add to Cart'}
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Mobile-Responsive Bottom Controls & Pagination */}
      <div className="space-y-4">
        {/* Delete Selected Button - Mobile First */}
        {table.getFilteredSelectedRowModel().rows.length > 0 && (
          <div className="flex justify-center">
            <Button
              disabled={isLoading}
              onClick={handleDeleteSelected}
              className="w-full sm:w-auto h-12 sm:h-9 text-base sm:text-sm px-6 bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              <Trash2 className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
              Delete Selected ({table.getFilteredSelectedRowModel().rows.length})
            </Button>
          </div>
        )}

        {/* Pagination Section */}
        <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-2 py-4">
          <div className="text-sm text-muted-foreground text-center sm:text-left">
            {isInitialLoading
              ? "Loading..."
              : `${table.getFilteredSelectedRowModel().rows.length} of ${table.getFilteredRowModel().rows.length} row(s) selected.`}
          </div>

          {/* Desktop Pagination */}
          {!isMobile && (
            <TablePagination
              pageIndex={pageIndex}
              pageSize={pageSize}
              pageCount={pageCount}
              totalItems={filteredRows.length}
              onPageChange={setPageIndex}
              onPageSizeChange={setPageSize}
            />
          )}

          {/* Mobile Pagination */}
          {isMobile && (
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => setPageIndex(Math.max(0, pageIndex - 1))}
                disabled={pageIndex === 0 || isLoading}
                className="h-12 px-6 text-base border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              >
                Previous
              </Button>
              <span className="text-sm font-medium">
                Page {pageIndex + 1} of {Math.max(1, pageCount)}
              </span>
              <Button
                onClick={() => setPageIndex(Math.min(pageCount - 1, pageIndex + 1))}
                disabled={pageIndex >= pageCount - 1 || isLoading}
                className="h-12 px-6 text-base border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Cart Checkout Dialog */}
      <CheckoutDialog
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        cart={cart}
        onUpdateQuantity={updateCartItemQuantity}
        onUpdatePrice={updateCartItemPrice}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
        isLoading={isLoading}
        storeCurrency={store?.currency}
        paymentMethod={paymentMethod}
        mpesaPhoneNumber={mpesaPhoneNumber}
        onPaymentMethodChange={setPaymentMethod}
        onMpesaPhoneNumberChange={setMpesaPhoneNumber}
        itemDiscounts={itemDiscounts}
        onToggleItemDiscount={toggleItemDiscount}
        cartTotal={cartTotal}
      />

      {/* Barcode Scanner Dialog */}
      <Dialog open={isScannerOpen} onOpenChange={setIsScannerOpen}>
        <DialogContent className="barcode-scanner-dialog w-[100vw] h-[100vh] max-w-none p-0 overflow-hidden flex flex-col sm:w-[90vw] sm:h-[90vh] sm:max-w-2xl sm:rounded-lg">
          <DialogHeader className="flex-shrink-0 p-3 sm:p-4 border-b bg-background">
            <DialogTitle className="flex items-center justify-between text-base sm:text-lg">
              <div className="flex items-center min-w-0 flex-1">
                <Scan className="mr-2 h-5 w-5 flex-shrink-0" />
                <span className="truncate">Scan Barcode</span>
              </div>
              <Button
                onClick={closeBarcodeScanner}
                className="h-10 w-10 sm:h-8 sm:w-8 hover:bg-accent hover:text-accent-foreground flex-shrink-0 ml-2"
              >
                <X className="h-6 w-6 sm:h-5 sm:w-5" />
              </Button>
            </DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              Point your camera at a barcode to add the item to your cart
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 flex flex-col items-center justify-center p-2 sm:p-4 bg-gray-900 min-h-0 overflow-hidden">
            {cameraError ? (
              <div className="text-center text-white px-4 max-w-sm mx-auto">
                <div className="mb-6">
                  <Scan className="h-20 w-20 sm:h-16 sm:w-16 mx-auto text-gray-400" />
                </div>
                <h3 className="text-xl sm:text-lg font-semibold mb-4 sm:mb-2">Camera Access Required</h3>
                <p className="text-base sm:text-sm opacity-75 mb-6 sm:mb-4 leading-relaxed">{cameraError}</p>
                <div className="space-y-4 sm:space-y-2">
                  <Button
                    onClick={async () => {
                      try {
                        console.log('Requesting camera permission...');

                        // This will trigger the browser's permission dialog
                        const stream = await navigator.mediaDevices.getUserMedia({
                          video: {
                            facingMode: 'environment' // Use back camera on mobile
                          }
                        });

                        console.log('Camera permission granted, stream obtained');

                        // Stop the stream immediately - we just wanted to get permission
                        stream.getTracks().forEach(track => {
                          track.stop();
                          console.log('Stopped camera track:', track.label);
                        });

                        // Clear error and reset scanner
                        setCameraError("");
                        setCameraReady(false);
                        toast.success("Camera access granted! Scanner will initialize...");

                        // Give a moment for the toast to show, then try to initialize camera
                        setTimeout(() => {
                          setCameraReady(false);
                        }, 1000);

                      } catch (error: any) {
                        console.error("Camera permission error:", error);

                        let errorMessage = "";
                        let instructions = "";

                        if (error.name === 'NotAllowedError') {
                          errorMessage = "Camera permission was denied.";
                          instructions = "Please click 'Allow' when your browser asks for camera permission, or enable camera access in your browser settings.";
                        } else if (error.name === 'NotFoundError') {
                          errorMessage = "No camera found on this device.";
                          instructions = "Please ensure your device has a camera and try again.";
                        } else if (error.name === 'NotReadableError') {
                          errorMessage = "Camera is being used by another application.";
                          instructions = "Please close other apps that might be using the camera and try again.";
                        } else {
                          errorMessage = "Failed to access camera.";
                          instructions = "Please check your camera permissions and try again.";
                        }

                        setCameraError(`${errorMessage} ${instructions}`);
                        toast.error(errorMessage);
                      }
                    }}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 w-full h-12 sm:h-10 text-base sm:text-sm"
                  >
                    Enable Camera Access
                  </Button>
                  <div className="text-sm sm:text-xs opacity-50 leading-relaxed space-y-2">
                    <p>If camera access fails:</p>
                    <ul className="text-left space-y-1 text-xs">
                      <li>• Refresh the page and try again</li>
                      <li>• Check browser settings → Site permissions → Camera</li>
                      <li>• Ensure no other app is using the camera</li>
                    </ul>
                  </div>
                </div>
              </div>
            ) : isScannerOpen ? (
              <div className="relative w-full h-full max-w-lg mx-auto flex-1 min-h-0">
                <BarcodeScanner
                  width="100%"
                  height="100%"
                  facingMode="environment"
                  delay={300}
                  onUpdate={(err, result) => {
                    // Handle camera errors
                    if (err) {
                      console.error("Camera error:", err);
                      // Set camera ready to true even on error to remove loading overlay
                      setCameraReady(true);
                      setCameraError("Camera error: " + ((err as any)?.message || "Unable to access camera. Please check camera permissions."));
                      return;
                    }

                    // Set camera ready when we start getting updates (even without result)
                    if (!cameraReady) {
                      setCameraReady(true);
                      console.log("Camera initialized successfully");
                    }

                    if (result && !isScanning) {
                      // Handle different result formats from the barcode scanner
                      let scannedText = '';
                      try {
                        if (typeof result === 'string') {
                          scannedText = result;
                        } else if (result && typeof result === 'object') {
                          // Try different property names that might contain the barcode
                          scannedText = (result as any).getText?.() ||
                                       (result as any).text ||
                                       (result as any).data ||
                                       String(result);
                        }

                        if (scannedText && typeof scannedText === 'string') {
                          handleBarcodeScanned(scannedText);
                        }
                      } catch (error) {
                        console.warn('Error processing barcode result:', error);
                      }
                    }
                  }}
                  onError={(error) => {
                    // Camera failed to load
                    console.error("Camera load error:", error);
                    setCameraReady(true);
                    setCameraError("Failed to load camera. Please check permissions and try again.");
                  }}
                />

                {/* Camera loading overlay */}
                {!cameraReady && (
                  <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
                    <div className="text-center text-white">
                      <Loader2 className="h-10 w-10 sm:h-8 sm:w-8 animate-spin mx-auto mb-3 sm:mb-2" />
                      <p className="text-base sm:text-sm">Initializing camera...</p>
                    </div>
                  </div>
                )}

                {/* Scanning overlay */}
                {isScanning && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="bg-white p-4 sm:p-3 rounded-lg flex items-center gap-2 mx-4">
                      <Loader2 className="h-6 w-6 sm:h-5 sm:w-5 animate-spin" />
                      <span className="text-base sm:text-sm">Processing barcode...</span>
                    </div>
                  </div>
                )}

                {/* Scanning frame overlay */}
                {cameraReady && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 sm:w-64 sm:h-64 border-2 border-white border-opacity-50 rounded-lg">
                      <div className="absolute top-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
                      <div className="absolute top-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
                      <div className="absolute bottom-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
                      <div className="absolute bottom-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-white">
                <Loader2 className="h-10 w-10 sm:h-8 sm:w-8 animate-spin mx-auto mb-3 sm:mb-2" />
                <p className="text-base sm:text-sm">Loading scanner...</p>
              </div>
            )}

            <div className="mt-6 sm:mt-4 text-center text-white px-4">
              {cameraReady && !cameraError && (
                <p className="text-base sm:text-sm opacity-75">
                  Position the barcode within the frame
                </p>
              )}
              {lastScannedCode && (
                <p className="text-sm sm:text-xs mt-3 sm:mt-2 opacity-50">
                  Last scanned: {lastScannedCode}
                </p>
              )}
            </div>
          </div>

          <div className="flex-shrink-0 p-4 sm:p-4 border-t bg-background">
            <Button
              onClick={closeBarcodeScanner}
              className="w-full h-12 sm:h-10 text-base sm:text-sm border border-input bg-background hover:bg-accent hover:text-accent-foreground"
            >
              Close Scanner
            </Button>
          </div>
        </DialogContent>
      </Dialog>

       {/* WebSocket Status Indicator UI */}
      <div className="fixed bottom-4 right-4 flex items-center gap-2 p-2 bg-background border rounded-full shadow-md text-xs z-40">
        <div title={`Status: ${connectionStatus}`} className={`w-3 h-3 rounded-full ${
          connectionStatus === 'open' ? 'bg-green-500 pulse-stable' : // Use CSS for pulsing
          connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
          'bg-red-500'
        }`} />
        <span className="hidden sm:inline">
          {connectionStatus === 'open' ? 'Live Sync' : connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
        </span>
        {connectionStatus !== 'open' && connectionStatus !== 'connecting' && (
          <Button onClick={reconnect} className="h-6 px-1 text-xs text-primary hover:bg-accent hover:text-accent-foreground" title="Attempt Reconnect"> <RefreshCw className="h-3 w-3" /> </Button>
        )}
         {pendingReceiptId && ( <span className="hidden sm:inline text-blue-600 animate-pulse ml-1" title="Waiting for M-Pesa confirmation">(Waiting...)</span> )}
      </div>
      {/* Add CSS for pulse-stable if desired: @keyframes pulse-stable { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } } .pulse-stable { animation: pulse-stable 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; } */}

    </div>
  );
}