// app/app/items/page.tsx
"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, ShoppingCart, Loader2, RefreshCw } from "lucide-react"; // Added RefreshCw
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import Cookies from "js-cookie";
import { Item } from "@/lib/api/items/models";
import { Brand } from "@/lib/api/brands/models";
import { fetchItems } from "@/lib/api/items/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchCategories } from "@/lib/api/categories/service";
import { Category } from "@/lib/api/categories/models";
import { useRouter } from "next/navigation";
import { PaymentMethod, SellItemSchema, SellItemsSchema } from "@/lib/api/sales/models";
import { sellItems, SellItemsResult } from "@/lib/api/sales/service";
import { useWebSocket, PaymentStatusMessage } from "@/lib/hooks/useWebSocket"; // Adjust path if needed
import { getColumns } from "./columns"; // Adjust path if needed
import { CheckoutDialog } from "../../components/CheckoutDialog"; // Adjust path if needed
import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import toast from 'react-hot-toast';

// --- Helper Components ---
function LoadingSpinner() {
  return <Loader2 className="h-8 w-8 animate-spin text-primary" />;
}

function ModalOverlay({ children }: { children: React.ReactNode }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50">
      <div className="bg-background p-4 rounded-lg shadow-xl border">
          {children}
          <p className="text-center text-sm mt-2 text-muted-foreground">Processing...</p>
      </div>
    </div>
  );
}

// --- Cart Item Definition ---
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  has_discount: boolean;
  discount: number;
}

// --- Main Page Component ---
export default function Page() {
  const router = useRouter();
  // Data state
  const [data, setItems] = useState<Item[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [store, setStore] = useState<Store | null>(null);
  // UI State
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  // Payment State
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [mpesaPhoneNumber, setMpesaPhoneNumber] = useState("");
  const [itemDiscounts, setItemDiscounts] = useState<{ [itemId: string]: boolean }>({});
  // Tanstack Table State
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  // Auth & Store Context
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  // WebSocket State
  const { connectionStatus, lastMessage, reconnect } = useWebSocket(!!token);
  const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);

  // --- Data Refresh Function ---
  const refreshItemsData = useCallback(() => {
    if (token && store_id) {
      fetchItems(token, store_id)
        .then(setItems)
        .catch(e => {
          console.error("Failed to refresh items", e);
          toast.error("Could not refresh item list.");
        });
    }
  }, [token, store_id]);

  // --- Cart Management Functions ---
  const addToCart = useCallback((item: Item) => {
    if (item.quantity === 0) { toast.error(`${item.name} is out of stock.`); return; };
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        if (existingItem.quantity >= item.quantity) { toast.error(`Maximum stock (${item.quantity}) for ${item.name} reached.`); return prevCart; }
        toast.success(`${item.name} quantity updated.`);
        return prevCart.map(cartItem => cartItem.id === item.id ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem );
      }
      toast.success(`${item.name} added to cart.`);
      return [...prevCart, { id: item.id, name: item.name, price: item.default_price, quantity: 1, image: item.image, has_discount: item.has_discount, discount: item.discount || 0 }];
    });
  }, []);

  const updateCartItemQuantity = useCallback((itemId: string, quantity: number) => {
    const itemInStock = data.find(item => item.id === itemId);
    if (!itemInStock) return;
    setCart(prevCart => prevCart.map(cartItem => cartItem.id === itemId ? { ...cartItem, quantity: Math.max(1, Math.min(quantity, itemInStock.quantity)) } : cartItem ));
  }, [data]);

  const updateCartItemPrice = useCallback((itemId: string, price: number) => {
    setCart(prevCart => prevCart.map(item => item.id === itemId ? { ...item, price: Math.max(0, price) } : item ));
  }, []);

  const removeFromCart = useCallback((itemId: string) => {
    setCart(prevCart => {
      const itemToRemove = prevCart.find(item => item.id === itemId);
      const newCart = prevCart.filter(item => item.id !== itemId);
      if (itemToRemove) { toast.success(`${itemToRemove.name} removed.`); }
      if (newCart.length === 0) { setIsCartOpen(false); }
      return newCart;
    });
  }, []);

  const toggleItemDiscount = useCallback((itemId: string) => {
    setItemDiscounts(prev => ({ ...prev, [itemId]: !prev[itemId] }));
  }, []);

  const getItemTotal = useCallback((item: CartItem) => {
    const baseTotal = item.price * item.quantity;
    return (itemDiscounts[item.id] && item.has_discount && item.discount) ? baseTotal * (1 - item.discount / 100) : baseTotal;
  }, [itemDiscounts]);

  const cartTotal = useMemo(() => cart.reduce((total, item) => total + getItemTotal(item), 0), [cart, getItemTotal]);

  // --- Initial Data Fetching ---
  useEffect(() => {
    async function getInitialData() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const [fetchedItems, fetchedBrands, fetchedCategories, fetchedStore] = await Promise.all([
            fetchItems(token, store_id), fetchBrands(token, store_id), fetchCategories(token, store_id), getStore(store_id, token)
          ]);
          setItems(fetchedItems); setBrands(fetchedBrands); setCategories(fetchedCategories); setStore(fetchedStore);
        } catch (error: any) {
          console.error("Error fetching initial data:", error);
          toast.error(`Failed to load store data: ${error.message || 'Unknown error'}`);
        } finally {
          setIsInitialLoading(false);
        }
      } else {
         setIsInitialLoading(false);
         if (!token) toast.error("Authentication missing. Please log in.");
         else if (!store_id) toast.error("No active store selected.");
      }
    }
    getInitialData();
  }, [store_id, token]);

  // --- Item Deletion Logic ---
  const handleDeleteSelected = async () => {
     const selectedRows = table.getFilteredSelectedRowModel().rows;
     const count = selectedRows.length;
     if (count === 0 || !token || !store_id) return;

     setIsDeleteDialogOpen(false); // Close confirmation dialog if it was open

     toast((t) => ( // Confirmation toast
       <span className="flex flex-col gap-2 items-center">
         Delete {count} item(s)? This cannot be undone.
         <div className="flex gap-2 mt-1">
            <Button
                variant="destructive" size="sm"
                onClick={async () => {
                toast.dismiss(t.id);
                setIsLoading(true);
                const deletingToastId = toast.loading(`Deleting ${count} item(s)...`);
                try {
                    // --- Replace with your actual DELETE API call(s) ---
                    console.log("Deleting:", selectedRows.map(r => r.original.id));
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network
                    // --- End Replace ---
                    toast.success(`${count} item(s) deleted.`, { id: deletingToastId });
                    refreshItemsData(); // Refresh the list
                    setRowSelection({}); // Clear selection
                } catch (error: any) {
                    toast.error(`Deletion failed: ${error.message || 'Unknown error'}`, { id: deletingToastId });
                } finally { setIsLoading(false); }
                }}
            > Confirm Delete </Button>
            <Button variant="outline" size="sm" onClick={() => toast.dismiss(t.id)}>Cancel</Button>
         </div>
       </span>
     ), { duration: 10000 });
  };

  // --- Centralized Checkout API Call Logic ---
  const performCheckout = useCallback(async (itemsToSell: SellItemSchema[], totalAmount: number) => {
    let errorMsg: string | null = null;
    if (!token || !store_id) errorMsg = "Authentication missing.";
    else if (itemsToSell.length === 0) errorMsg = "Cart is empty.";
    else if (!paymentMethod) errorMsg = "Select a payment method.";
    else {
      const mpesaRegex = /^(?:254|\+254|0)?(7(?:(?:[0-9][0-9])|(?:[0-9][0-9])|(?:[0-9][0-9]))[0-9]{6})$/;
      if (paymentMethod === "mpesa" && (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber))) {
        errorMsg = "Enter a valid M-Pesa number (e.g., 2547...).";
      }
    }
    if (errorMsg) { toast.error(errorMsg); return { ok: false, status: 400, error: errorMsg } as SellItemsResult; }

    const sellItemsPayload: SellItemsSchema = { items: itemsToSell, payment_method: paymentMethod!, mpesa_payload: paymentMethod === "mpesa" ? { amount: totalAmount, phone_number: mpesaPhoneNumber } : undefined };

    setIsLoading(true);
    try {
      return await sellItems(token!, sellItemsPayload);
    } catch (error: any) {
      console.error("Checkout API call failed:", error);
      toast.error(`Checkout failed: ${error.message || 'Network error'}`);
      return { ok: false, status: 500, error: error.message || "API Error" } as SellItemsResult;
    } finally {
      setIsLoading(false);
    }
  }, [token, store_id, paymentMethod, mpesaPhoneNumber]);

  // --- Cart Checkout Handler ---
  const handleCheckout = async () => {
    if (!store_id) { toast.error("Store not selected."); return; }
    const itemsToSell: SellItemSchema[] = cart.map(item => ({ item_uuid: item.id, price: getItemTotal(item), quantity: item.quantity, store_id: store_id }));

    setPendingReceiptId(null);

    const result = await performCheckout(itemsToSell, cartTotal);

    if (result.ok) {
      if (result.status === 200) { // Sync Success
        toast.success(`Sale completed! Receipt: ${result.data.receipt_number}`);
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({}); setIsCartOpen(false);
        refreshItemsData();
      } else if (result.status === 202) { // Async Pending
        setPendingReceiptId(result.data.receiptId);
        toast.loading(`M-Pesa request sent. Check phone... (Ref: ${result.data.receiptId})`, { id: result.data.receiptId, duration: 45000 });
        setIsCartOpen(false);
      }
    }
  };

   // --- WebSocket Message Handling ---
  useEffect(() => {
    if (lastMessage && pendingReceiptId && lastMessage.type === 'payment_status' && lastMessage.receiptId === pendingReceiptId) {
      const currentPendingId = pendingReceiptId;
      setPendingReceiptId(null);
      toast.dismiss(currentPendingId);

      const { status, message, mpesaReceiptNumber } = lastMessage;
      if (status === 'completed') {
        toast.success(`Payment Completed! Ref: ${mpesaReceiptNumber || 'N/A'}`, { duration: 6000 });
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({});
        setTimeout(refreshItemsData, 100); // Refresh after state updates
      } else {
        toast.error(`Payment Failed: ${message || 'Transaction unsuccessful.'}`, { duration: 6000 });
        setPaymentMethod(null); // Reset payment method on failure
      }
    }
  }, [lastMessage, pendingReceiptId, refreshItemsData]);

  // --- Table Setup ---
  const columns = getColumns(brands, categories, router, addToCart, store?.currency);
  const table = useReactTable({
    data, columns, state: { sorting, columnFilters, columnVisibility, rowSelection },
    onSortingChange: setSorting, onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(), getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility, onRowSelectionChange: setRowSelection,
    enableRowSelection: true, meta: { addToCart }
  });

  // --- JSX Rendering ---
  return (
    <div className="w-full p-4 md:p-6 space-y-4 relative">
      {isLoading && <ModalOverlay><LoadingSpinner /></ModalOverlay>}

      {/* Top Controls */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-2">
        <Input
          placeholder="Filter items by name..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="max-w-sm w-full sm:w-auto h-9"
          disabled={isInitialLoading || isLoading}
        />
        <div className="flex gap-2 w-full sm:w-auto justify-end flex-wrap">
          <Button size="sm" onClick={() => router.push("/app/items/create")} disabled={isInitialLoading || isLoading}>
            <Plus className="mr-1.5 h-4 w-4" /> New Item
          </Button>
          <Button size="sm" onClick={() => setIsCartOpen(true)} variant="outline" disabled={isInitialLoading || isLoading || cart.length === 0}>
            <ShoppingCart className="mr-1.5 h-4 w-4" /> Cart ({cart.length})
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="outline" disabled={isInitialLoading || isLoading}> Columns <ChevronDown className="ml-1.5 h-4 w-4" /> </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllColumns().filter((c) => c.getCanHide()).map((c) => (
                <DropdownMenuCheckboxItem key={c.id} className="capitalize" checked={c.getIsVisible()} onCheckedChange={(v) => c.toggleVisibility(!!v)}> {c.id.replace(/_/g, ' ')} </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((hg) => ( <TableRow key={hg.id}> {hg.headers.map((h) => ( <TableHead key={h.id}>{h.isPlaceholder ? null : flexRender(h.column.columnDef.header, h.getContext())}</TableHead> ))} </TableRow> ))}
          </TableHeader>
          <TableBody>
            {isInitialLoading ? ( <TableRow><TableCell colSpan={columns.length} className="h-48 text-center"><LoadingSpinner /></TableCell></TableRow>
            ) : table.getRowModel().rows?.length ? ( table.getRowModel().rows.map((row) => ( <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>{row.getVisibleCells().map((cell) => ( <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell> ))}</TableRow> ))
            ) : ( <TableRow><TableCell colSpan={columns.length} className="h-24 text-center text-muted-foreground">No items found.</TableCell></TableRow> )}
          </TableBody>
        </Table>
      </div>

      {/* Bottom Controls */}
      <div className="flex items-center justify-between space-x-2">
        <div className="flex-1 text-sm text-muted-foreground"> {table.getFilteredSelectedRowModel().rows.length} selected </div>
        <div className="space-x-2 flex items-center flex-wrap">
           {table.getFilteredSelectedRowModel().rows.length > 0 && (
              <Button variant="destructive" size="sm" disabled={isLoading} onClick={handleDeleteSelected}>
                 <Trash2 className="mr-1.5 h-4 w-4" /> Delete ({table.getFilteredSelectedRowModel().rows.length})
              </Button>
           )}
          <Button size="sm" variant="outline" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage() || isLoading}>Prev</Button>
          <Button size="sm" variant="outline" onClick={() => table.nextPage()} disabled={!table.getCanNextPage() || isLoading}>Next</Button>
        </div>
      </div>

      {/* Cart Checkout Dialog */}
      <CheckoutDialog
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        cart={cart}
        onUpdateQuantity={updateCartItemQuantity}
        onUpdatePrice={updateCartItemPrice}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
        isLoading={isLoading}
        storeCurrency={store?.currency}
        paymentMethod={paymentMethod}
        mpesaPhoneNumber={mpesaPhoneNumber}
        onPaymentMethodChange={setPaymentMethod}
        onMpesaPhoneNumberChange={setMpesaPhoneNumber}
        itemDiscounts={itemDiscounts}
        onToggleItemDiscount={toggleItemDiscount}
        cartTotal={cartTotal}
      />

       {/* WebSocket Status Indicator UI */}
      <div className="fixed bottom-4 right-4 flex items-center gap-2 p-2 bg-background border rounded-full shadow-md text-xs z-40">
        <div title={`Status: ${connectionStatus}`} className={`w-3 h-3 rounded-full ${
          connectionStatus === 'open' ? 'bg-green-500 pulse-stable' : // Use CSS for pulsing
          connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
          'bg-red-500'
        }`} />
        <span className="hidden sm:inline">
          {connectionStatus === 'open' ? 'Live Sync' : connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
        </span>
        {connectionStatus !== 'open' && connectionStatus !== 'connecting' && (
          <Button variant="ghost" size="sm" onClick={reconnect} className="h-6 px-1 text-xs text-primary hover:bg-muted" title="Attempt Reconnect"> <RefreshCw className="h-3 w-3" /> </Button>
        )}
         {pendingReceiptId && ( <span className="hidden sm:inline text-blue-600 animate-pulse ml-1" title="Waiting for M-Pesa confirmation">(Waiting...)</span> )}
      </div>
      {/* Add CSS for pulse-stable if desired: @keyframes pulse-stable { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } } .pulse-stable { animation: pulse-stable 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; } */}

    </div>
  );
}