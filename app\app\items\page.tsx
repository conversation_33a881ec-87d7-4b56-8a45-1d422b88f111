// app/app/items/page.tsx
"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import { BASE_URL } from "@/app/configs/constants";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, ShoppingCart, Loader2, RefreshCw } from "lucide-react";
// Removed unused imports
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import Cookies from "js-cookie";
import { Item } from "@/lib/api/items/models";
import { Brand } from "@/lib/api/brands/models";
import { fetchItems } from "@/lib/api/items/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchCategories } from "@/lib/api/categories/service";
import { Category } from "@/lib/api/categories/models";
import { useRouter } from "next/navigation";
import { PaymentMethod, SellItemSchema, SellItemsSchema } from "@/lib/api/sales/models";
import { sellItems, SellItemsResult } from "@/lib/api/sales/service";
import { useWebSocket } from "@/lib/hooks/useWebSocket"; // Adjust path if needed
import { getColumns } from "./columns"; // Adjust path if needed
import { CheckoutDialog } from "../../components/CheckoutDialog"; // Adjust path if needed
import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import { toast } from 'sonner';
import { usePrintReceipt } from "@/lib/hooks/usePrintReceipt";

// --- Helper Components ---
function LoadingSpinner() {
  // Simple spinner definition
  return <Loader2 className="h-8 w-8 animate-spin text-primary" />;
}

function ModalOverlay({ children }: { children: React.ReactNode }) {
  // Loading overlay for async operations like checkout
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50">
      <div className="bg-background p-4 rounded-lg shadow-xl border">
        {children}
        <p className="text-center text-sm mt-2 text-muted-foreground">Processing...</p>
      </div>
    </div>
  );
}

// --- Cart Item Definition ---
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  has_discount: boolean;
  discount: number;
}

// --- Main Page Component ---
export default function Page() {
  const router = useRouter();
  const { printReceipt } = usePrintReceipt();
  // Data state
  const [data, setItems] = useState<Item[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [store, setStore] = useState<Store | null>(null);
  // UI State
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // For async actions like checkout/delete
  const [isInitialLoading, setIsInitialLoading] = useState(true); // Specifically for initial data load
  // Payment State
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [mpesaPhoneNumber, setMpesaPhoneNumber] = useState("");
  const [itemDiscounts, setItemDiscounts] = useState<{ [itemId: string]: boolean }>({});
  // Tanstack Table State
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  // Auth & Store Context
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  // WebSocket State
  const { connectionStatus, lastMessage, reconnect } = useWebSocket(!!token);
  const [saleStatusMessage, setSaleStatusMessage] = useState<string | null>(null);
  const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);

  // Function to fetch and print receipt
  const fetchAndPrintReceipt = async (receiptId: string) => {
    try {
      const response = await fetch(`${BASE_URL}/receipts/${receiptId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        const receipt = await response.json();
        printReceipt(receipt);
      }
    } catch (error) {
      console.error('Error fetching receipt:', error);
      toast.error('Failed to fetch receipt for printing');
    }
  };

  // --- Data Refresh Function ---
  const refreshItemsData = useCallback(() => {
    if (token && store_id) {
      fetchItems(token, store_id)
        .then(setItems)
        .catch(e => {
          console.error("Failed to refresh items", e);
          toast.error("Could not refresh item list.");
        });
    }
  }, [token, store_id]);

  const addToCart = useCallback((item: Item) => {
    if (item.quantity === 0) {
      toast.error(`${item.name} is out of stock.`);
      return;
    }
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        if (existingItem.quantity >= item.quantity) {
          toast.error(`Maximum stock (${item.quantity}) for ${item.name} reached.`);
          return prevCart;
        }
        return prevCart.map(cartItem => cartItem.id === item.id ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem);
      }
      return [...prevCart, {
        id: item.id, name: item.name, price: item.default_price, quantity: 1,
        image: item.image, has_discount: item.has_discount, discount: item.discount || 0
      }];
    });
    toast.success(`${item.name} ${cart.find(cartItem => cartItem.id === item.id) ? 'quantity updated.' : 'added to cart.'}`);
  }, [cart]);

  const updateCartItemQuantity = useCallback((itemId: string, quantity: number) => {
    const itemInStock = data.find(item => item.id === itemId);
    if (!itemInStock) return;
    setCart(prevCart => prevCart.map(cartItem =>
      cartItem.id === itemId
        ? { ...cartItem, quantity: Math.max(1, Math.min(quantity, itemInStock.quantity)) }
        : cartItem
    ));
  }, [data]);

  const updateCartItemPrice = useCallback((itemId: string, price: number) => {
    setCart(prevCart => prevCart.map(item =>
      item.id === itemId
        ? { ...item, price: Math.max(0, price) }
        : item
    ));
  }, []);

  const removeFromCart = useCallback((itemId: string) => {
    let removedItemName: string | undefined;
    setCart(prevCart => {
      const itemToRemove = prevCart.find(item => item.id === itemId);
      removedItemName = itemToRemove?.name;
      const newCart = prevCart.filter(item => item.id !== itemId);
      if (newCart.length === 0) { setIsCartOpen(false); }
      return newCart;
    });
    setItemDiscounts(prev => { const newState = { ...prev }; delete newState[itemId]; return newState; });
    if (removedItemName) {
      toast.success(`${removedItemName} removed from cart.`);
    }
  }, []);

  const toggleItemDiscount = useCallback((itemId: string) => {
    setItemDiscounts(prev => ({ ...prev, [itemId]: !prev[itemId] }));
  }, []);

  const getItemTotal = useCallback((item: CartItem) => {
    const baseTotal = item.price * item.quantity;
    return (itemDiscounts[item.id] && item.has_discount && item.discount > 0)
      ? baseTotal * (1 - item.discount / 100)
      : baseTotal;
  }, [itemDiscounts]);

  const cartTotal = useMemo(() => cart.reduce((total, item) => total + getItemTotal(item), 0), [cart, getItemTotal]);

  // Reset payment state when cart changes
  useEffect(() => {
    if (cart.length === 0) {
      setPaymentMethod(null);
      setMpesaPhoneNumber("");
      setSaleStatusMessage(null);
      setIsLoading(false);
    }
  }, [cart]);

  // --- Initial Data Fetching ---
  useEffect(() => {
    async function getInitialData() {
      if (token && store_id) {
        setIsInitialLoading(true); // Start initial loading
        try {
          const [fetchedItems, fetchedBrands, fetchedCategories, fetchedStore] = await Promise.all([
            fetchItems(token, store_id),
            fetchBrands(token, store_id),
            fetchCategories(token, store_id),
            getStore(store_id, token)
          ]);
          setItems(fetchedItems);
          setBrands(fetchedBrands);
          setCategories(fetchedCategories);
          setStore(fetchedStore);
        } catch (error: any) {
          console.error("Error fetching initial data:", error);
          toast.error(`Failed to load store data: ${error.message || 'Unknown error'}`);
        } finally {
          setIsInitialLoading(false); // Stop initial loading regardless of success/failure
        }
      } else {
        setIsInitialLoading(false); // Stop loading if no token/store_id
        if (!token) toast.error("Authentication missing. Please log in.");
        else if (!store_id) toast.error("No active store selected.");
      }
    }
    getInitialData();
  }, [store_id, token]);

  // --- Item Deletion Logic ---
  const handleDeleteSelected = async () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    const count = selectedRows.length;
    if (count === 0 || !token || !store_id) return;

    toast(
      `Delete ${count} item(s)?`,
      {
        description: "This action cannot be undone.",
        duration: Infinity,
        action: {
          label: "Confirm Delete",
          onClick: async () => {
            setIsLoading(true); // Use general loading state
            const toastId = toast.loading(`Deleting ${count} item(s)...`);
            try {
              // --- Replace with your actual DELETE API call(s) ---
              console.log("Simulating Deletion of IDs:", selectedRows.map(r => r.original.id));
              await new Promise(resolve => setTimeout(resolve, 1500));
              // --- End Replace ---
              toast.success(`${count} item(s) deleted successfully.`, { id: toastId });
              refreshItemsData();
              setRowSelection({});
            } catch (error: any) {
              console.error("Deletion failed:", error);
              toast.error(`Deletion failed: ${error.message || 'Unknown error'}`, { id: toastId });
            } finally {
              setIsLoading(false); // Stop general loading state
            }
          },
        },
        cancel: {
          label: "Cancel",
          onClick: () => {},
        },
      }
    );
  };

  // --- Centralized Checkout API Call Logic ---
  const performCheckout = useCallback(async (itemsToSell: SellItemSchema[], totalAmount: number) => {
    let errorMsg: string | null = null;
    if (!token || !store_id) errorMsg = "Authentication credentials missing.";
    else if (itemsToSell.length === 0) errorMsg = "Your cart is empty.";
    else if (!paymentMethod) errorMsg = "Please select a payment method.";
    else {

      const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;
      if (paymentMethod === "mpesa" && (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber))) {
        errorMsg = "Please enter a valid M-Pesa number (e.g., 2547...).";
      }
    }
    if (errorMsg) { toast.error(errorMsg); return { ok: false, status: 400, error: errorMsg } as SellItemsResult; }

    const sellItemsPayload: SellItemsSchema = {
      items: itemsToSell,
      payment_method: paymentMethod!,
      mpesa_payload: paymentMethod === "mpesa" ? { amount: totalAmount, phone_number: mpesaPhoneNumber } : null
    };

    setIsLoading(true); // Use general loading state
    const loadingToastId = toast.loading('Processing sale...');
    try {
      const result = await sellItems(token!, sellItemsPayload);
      toast.dismiss(loadingToastId);
      return result;
    } catch (error: any) {
      console.error("Checkout API call failed:", error);
      toast.error(`Checkout failed: ${error.message || 'Network error'}`, { id: loadingToastId });
      return { ok: false, status: 500, error: error.message || "API Call Error" } as SellItemsResult;
    } finally {
      // Don't set isLoading=false here for Mpesa, handled by WebSocket/timeout
      if (paymentMethod !== 'mpesa') {
        setIsLoading(false); // Stop general loading state for sync methods
      }
    }
  }, [token, store_id, paymentMethod, mpesaPhoneNumber]);

  // --- Cart Checkout Handler ---
  const handleCheckout = async () => {
    if (!token || !store_id) {
      toast.error("Authentication required.");
      return;
    }

    // Dismiss any existing toasts before starting new checkout
    toast.dismiss();
    setIsLoading(true);
    setSaleStatusMessage("Processing sale...");

    try {
      const payload: SellItemsSchema = {
        items: cart.map((item) => ({
          item_uuid: item.id,
          quantity: item.quantity,
          price: item.price,
          store_id: store_id
        })),
        payment_method: paymentMethod!,
        mpesa_payload: paymentMethod === "mpesa" ? {
          phone_number: mpesaPhoneNumber,
          amount: cartTotal,
        } : null,
      };

      const result = await sellItems(token, payload);

      if (result.ok) {
        if (result.status === 200) {
          // Dismiss any existing toasts before showing success
          toast.dismiss();
          const successMessage = `Sale successful! Receipt: ${result.data.receipt_number}`;
          setSaleStatusMessage(successMessage);
          toast.success(successMessage);
          setIsLoading(false);
          setIsCartOpen(false);
          setCart([]); // Clear cart after successful payment
          // Handle cash payment - print receipt immediately
          if (paymentMethod === 'cash') {
            fetchAndPrintReceipt(result.data.receipt_number);
          }
        } else if (result.status === 202) {
          const toastId = toast.loading("Waiting for M-Pesa payment...");
          setSaleStatusMessage(`Waiting for M-Pesa payment confirmation...`);
          setPendingReceiptId(result.data.receiptId);
          // Keep loading true while waiting for WebSocket response

          // Set a timeout to dismiss the loading toast if no response after 30 seconds
          setTimeout(() => {
            if (pendingReceiptId === result.data.receiptId) {
              toast.dismiss(toastId);
              toast.error("M-Pesa payment confirmation timed out. Please check your phone for the status.");
              setIsLoading(false);
            }
          }, 30000);
        }
      } else {
        // Dismiss any existing toasts before showing error
        toast.dismiss();
        const errorMessage = `Sale failed: ${result.error}`;
        setSaleStatusMessage(errorMessage);
        toast.error(errorMessage);
        setIsLoading(false);
        // Keep dialog open to show error
      }
    } catch (error: any) {
      // Dismiss any existing toasts before showing error
      toast.dismiss();
      console.error("Error during checkout:", error);
      const errorMessage = `Checkout failed: ${error.message}`;
      setSaleStatusMessage(errorMessage);
      toast.error(errorMessage);
      setIsLoading(false);
      // Keep dialog open to show error
    }
  };

  // WebSocket message handler
  useEffect(() => {
    if (lastMessage && lastMessage.receiptId === pendingReceiptId) {
      console.log(`Processing WebSocket message for receipt ${pendingReceiptId}:`, lastMessage);
      // Dismiss any existing loading toasts
      toast.dismiss();
      
      if (lastMessage.status === 'completed') {
        setSaleStatusMessage(`Payment Completed! Receipt: ${lastMessage.receiptId}. M-Pesa Ref: ${lastMessage.mpesaReceiptNumber || 'N/A'}`);
        toast.success("Payment completed successfully!");
        setPendingReceiptId(null);
        setIsLoading(false);
        setIsCartOpen(false);
        setCart([]); // Clear cart after successful payment
        // Print receipt after successful payment
        fetchAndPrintReceipt(lastMessage.receiptId);
      } else {
        const errorMessage = `Payment Failed: ${lastMessage.message}`;
        setSaleStatusMessage(errorMessage);
        toast.error(errorMessage);
        setPendingReceiptId(null);
        setIsLoading(false);
        // Keep dialog open to show error
      }
    }
  }, [lastMessage, pendingReceiptId]);

  // Clean up toasts when dialog is closed
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      toast.dismiss(); // Dismiss all toasts when dialog is closed
    }
    setIsCartOpen(open);
  };

  // --- Table Setup ---
  const columns = useMemo(() => getColumns(brands, categories, router, addToCart, store?.currency), [brands, categories, router, addToCart, store?.currency]);
  const table = useReactTable({
    data, columns, state: { sorting, columnFilters, columnVisibility, rowSelection },
    onSortingChange: setSorting, onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(), getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility, onRowSelectionChange: setRowSelection,
    enableRowSelection: true, meta: { addToCart }
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  // --- JSX Rendering ---
  return (
    <div className="w-full p-4 md:p-6 space-y-4 relative">
      {/* General Loading Overlay (use `isLoading`, not `isInitialLoading`) */}
      {isLoading && <ModalOverlay><LoadingSpinner /></ModalOverlay>}

      {/* Top Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-2">
        <Input
          placeholder="Filter items by name..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm h-9" // Full width on mobile, max-width on larger screens
          disabled={isInitialLoading || isLoading}
        />

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto justify-end flex-wrap mt-2 sm:mt-0"> {/* Adjusted margin top */}
          <Button size="sm" onClick={() => router.push("/app/items/create")} disabled={isInitialLoading || isLoading} className="w-full sm:w-auto">
            <Plus className="mr-1.5 h-4 w-4" /> New Item
          </Button>
          <Button size="sm" onClick={() => setIsCartOpen(true)} variant="outline" disabled={isInitialLoading || isLoading || cart.length === 0} className="w-full sm:w-auto">
            <ShoppingCart className="mr-1.5 h-4 w-4" /> Cart ({cart.length})
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="outline" disabled={isInitialLoading || isLoading} className="w-full sm:w-auto">
                Columns <ChevronDown className="ml-1.5 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllColumns().filter((c) => c.getCanHide()).map((c) => (
                <DropdownMenuCheckboxItem
                  key={c.id} className="capitalize" checked={c.getIsVisible()}
                  onCheckedChange={(v) => c.toggleVisibility(!!v)}
                > {c.id.replace(/_/g, ' ')} </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Data Table Section */}
      <div className="rounded-md border relative w-full overflow-auto  grid grid-cols-1">
        <Table className="w-full caption-bottom text-sm">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {/* ============ Check Initial Loading State HERE ============ */}
            {isInitialLoading ? (
              <TableRow>
                {/* THE CELL TO CENTER THE SPINNER IN */}
                <TableCell
                  colSpan={table.getAllColumns().length}
                  // ***** THIS IS THE CRITICAL LINE FOR CENTERING *****
                  className="h-48 p-0"
                // ***** MAKE SURE IT HAS flex items-center justify-center *****
                >
                  <div className="w-full h-full flex items-center justify-center">
                    <LoadingSpinner />
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedRows.length ? (
              // Render table rows
              paginatedRows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              // No data state
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center text-muted-foreground">
                  No items found.
                </TableCell>
              </TableRow>
            )}
            {/* ============ End Loading/Data Check ============ */}
          </TableBody>
        </Table>
      </div>

      {/* Bottom Controls */}
      <div className="flex items-center justify-between space-x-2 py-1">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2 flex items-center flex-wrap">
          {table.getFilteredSelectedRowModel().rows.length > 0 && (
            <Button variant="destructive" size="sm" disabled={isLoading} onClick={handleDeleteSelected}>
              <Trash2 className="mr-1.5 h-4 w-4" /> Delete ({table.getFilteredSelectedRowModel().rows.length})
            </Button>
          )}
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>

      {/* Checkout Dialog Component */}
      <CheckoutDialog
        isOpen={isCartOpen}
        onOpenChange={handleDialogOpenChange}
        cart={cart}
        onUpdateQuantity={updateCartItemQuantity}
        onUpdatePrice={updateCartItemPrice}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
        isLoading={isLoading}
        storeCurrency={store?.currency}
        paymentMethod={paymentMethod}
        mpesaPhoneNumber={mpesaPhoneNumber}
        onPaymentMethodChange={setPaymentMethod}
        onMpesaPhoneNumberChange={setMpesaPhoneNumber}
        itemDiscounts={itemDiscounts}
        onToggleItemDiscount={toggleItemDiscount}
        cartTotal={cartTotal}
        saleStatusMessage={saleStatusMessage}
      />

      {/* WebSocket Status Indicator */}
      <div className="fixed bottom-4 right-4 flex items-center gap-2 p-2 bg-background border rounded-full shadow-md text-xs z-40">
        <div title={`Connection: ${connectionStatus}`} className={`w-3 h-3 rounded-full ${connectionStatus === 'open' ? 'bg-green-500 animate-pulse' :
          connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
          }`} />
        <span className="hidden sm:inline">
          {connectionStatus === 'open' ? 'Live' : connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
        </span>
        {(connectionStatus === 'closed' || connectionStatus === 'error') && (
          <Button variant="ghost" size="sm" onClick={reconnect} className="h-6 px-1 text-xs text-primary hover:bg-muted" title="Attempt Reconnect">
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
        {pendingReceiptId && (
          <span className="hidden sm:inline text-blue-600 animate-pulse ml-1" title="Waiting for M-Pesa confirmation">(Waiting...)</span>
        )}
      </div>

    </div> // End main wrapping div
  ); // End return
} // End Page component