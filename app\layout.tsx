import type { Metada<PERSON> } from "next";
import "./globals.css";
import StoreProvider from "./StoreProvider";
import { roboto } from "./ui/fonts";
import { Toaster } from "sonner";
import ChunkErrorHandler from "./components/ChunkErrorHandler";

export const metadata: Metadata = {
  title: "StoreYako",
  description:
    "Cloud based Inventory Management System serving as Point of Sale (POS)",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={roboto.className}>
      <body>
        <StoreProvider>
          <ChunkErrorHandler />
          {children}
          <Toaster position="top-center" richColors />
        </StoreProvider>
      </body>
    </html>
  );
}
