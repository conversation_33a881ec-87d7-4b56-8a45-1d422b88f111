import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import StoreProvider from "./StoreProvider";
import { roboto } from "./ui/fonts";
import { Toaster } from "react-hot-toast";

export const metadata: Metadata = {
  title: "StoreYako",
  description:
    "Cloud based Inventory Management System serving as Point of Sale (POS)",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <StoreProvider>
      <html lang="en">
        <body
          className={`${roboto.className} text-xl text-gray-800 md:text-3xl md:leading-normal`}
        >
                  <Toaster position="top-center" reverseOrder={false} /> 
          {children}
        </body>
      </html>
    </StoreProvider>
  );
}
