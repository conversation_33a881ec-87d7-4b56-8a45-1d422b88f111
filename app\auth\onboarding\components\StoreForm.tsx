// app/auth/registration/components/StoreForm.tsx
import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { StoreOnboardingData } from '@/lib/api/users/onboarding/models';
import { searchCountries, getAllCountries } from '@/lib/api/users/onboarding/utils';
import { Label } from '@/components/ui/label';
import { Check, ChevronsUpDown, Search, Store, Mail, Phone, MapPin, Clock, ArrowRight, Building, Globe } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

interface StoreFormProps {
  storeData: StoreOnboardingData;
  onStoreDataChange: (data: Partial<StoreOnboardingData>) => void;
  onSubmit: () => void;
  isLastStep: boolean;
  userEmail: string;
}

type CountryData = {
  country: string;
  code: string;
  currency: string;
  currencyName: string;
};

const StoreForm: React.FC<StoreFormProps> = ({ storeData, onStoreDataChange, onSubmit, isLastStep, userEmail }) => {
  const [countries, setCountries] = useState<CountryData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCountryOpen, setIsCountryOpen] = useState(false);

  // Load all countries on component mount
  useEffect(() => {
    const data = getAllCountries();
    setCountries(data);
  }, []);

  // Set user email if empty
  useEffect(() => {
    if (!storeData.email) {
      onStoreDataChange({ email: userEmail });
    }
  }, [userEmail, onStoreDataChange, storeData.email]);

  // Filter countries based on search term
  const filteredCountries = searchTerm.length > 0
    ? countries.filter(country =>
      country.country.toLowerCase().includes(searchTerm.toLowerCase()))
    : countries;

  // Handle country selection
  const handleCountrySelect = (country: CountryData) => {
    onStoreDataChange({
      country: country.country,
      currency: country.currency
    });
    setIsCountryOpen(false);
  };

  // Check if all required fields are filled
  const isFormValid = storeData.name &&
    storeData.email &&
    storeData.country &&
    storeData.category &&
    storeData.phone_number &&
    storeData.address;

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-white border border-gray-100 shadow-sm rounded-xl overflow-hidden">
        <CardContent className="p-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-center">Set Up Your Store</h2>
              <p className="text-gray-500 text-center mb-8">Tell us more about your business</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Store Name */}
              <div className="space-y-2">
                <Label htmlFor="store-name" className="text-sm font-medium">Store Name</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <Store size={18} />
                  </div>
                  <Input
                    id="store-name"
                    placeholder="Your Store Name"
                    value={storeData.name}
                    onChange={(e) => onStoreDataChange({ name: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">Store Email</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <Mail size={18} />
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={storeData.email}
                    onChange={(e) => onStoreDataChange({ email: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category" className="text-sm font-medium">Business Category</Label>
                <Select
                  onValueChange={(value) => onStoreDataChange({ category: value })}
                  defaultValue={storeData.category}
                >
                  <SelectTrigger id="category" className="h-10">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="clothing">Clothing & Apparel</SelectItem>
                    <SelectItem value="food">Food & Beverage</SelectItem>
                    <SelectItem value="books">Books & Media</SelectItem>
                    <SelectItem value="furniture">Home & Furniture</SelectItem>
                    <SelectItem value="beauty">Beauty & Personal Care</SelectItem>
                    <SelectItem value="health">Health & Wellness</SelectItem>
                    <SelectItem value="art">Art & Crafts</SelectItem>
                    <SelectItem value="sports">Sports & Outdoors</SelectItem>
                    <SelectItem value="toys">Toys & Games</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Phone */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium">Phone Number</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <Phone size={18} />
                  </div>
                  <Input
                    id="phone"
                    placeholder="+****************"
                    value={storeData.phone_number}
                    onChange={(e) => onStoreDataChange({ phone_number: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* City */}
              <div className="space-y-2">
                <Label htmlFor="city" className="text-sm font-medium">City</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <Building size={18} />
                  </div>
                  <Input
                    id="city"
                    placeholder="New York"
                    value={storeData.city}
                    onChange={(e) => onStoreDataChange({ city: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Country Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="country" className="text-sm font-medium">Country</Label>
                <Popover open={isCountryOpen} onOpenChange={setIsCountryOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isCountryOpen}
                      className="w-full justify-between h-10"
                      id="country"
                    >
                      <div className="flex items-center">
                        <Globe size={18} className="mr-2 text-gray-400" />
                        <span className={storeData.country ? "text-foreground" : "text-muted-foreground"}>
                          {storeData.country || "Select a country"}
                        </span>
                      </div>
                      <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command className="w-full">
                      <div className="flex items-center border-b px-3">
                        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                        <CommandInput
                          placeholder="Search countries..."
                          className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                          value={searchTerm}
                          onValueChange={setSearchTerm}
                        />
                      </div>
                      <CommandList>
                        <CommandEmpty>No countries found.</CommandEmpty>
                        <CommandGroup className="max-h-64 overflow-auto">
                          {filteredCountries.map((country) => (
                            <CommandItem
                              key={country.code}
                              value={country.country}
                              onSelect={() => handleCountrySelect(country)}
                              className="flex items-center"
                            >
                              <span className="mr-2 text-xs bg-gray-100 rounded px-1 py-0.5">{country.code}</span>
                              {country.country}
                              {storeData.country === country.country && (
                                <Check className="ml-auto h-4 w-4 text-green-500" />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {storeData.currency && (
                  <p className="text-xs text-muted-foreground mt-1 flex items-center">
                    <span className="font-medium">Currency:</span>
                    <span className="ml-1 bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full">{storeData.currency}</span>
                  </p>
                )}
              </div>

              {/* Address */}
              <div className="space-y-2">
                <Label htmlFor="address" className="text-sm font-medium">Address</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <MapPin size={18} />
                  </div>
                  <Input
                    id="address"
                    placeholder="123 Main St, Apt 4B"
                    value={storeData.address}
                    onChange={(e) => onStoreDataChange({ address: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Opening Hours */}
              <div className="space-y-2">
                <Label htmlFor="opening_hours" className="text-sm font-medium">Opening Hours</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <Clock size={18} />
                  </div>
                  <Input
                    id="opening_hours"
                    placeholder="9:00 AM - 5:00 PM"
                    value={storeData.opening_hours}
                    onChange={(e) => onStoreDataChange({ opening_hours: e.target.value })}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div className="pt-6 flex justify-center">
              <Button
                type="button"
                onClick={onSubmit}
                disabled={!isLastStep || !isFormValid}
                className="w-full md:w-auto md:px-8 font-medium flex items-center justify-center gap-2"
                size="lg"
              >
                {isLastStep ? 'Complete Setup' : 'Next Step'}
                <ArrowRight size={16} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {storeData.currency && (
        <div className="flex justify-center mt-4">
          <div className="bg-blue-50 text-blue-700 rounded-lg px-4 py-2 text-sm">
            Your store will use <span className="font-semibold">{storeData.currency}</span> as its primary currency
          </div>
        </div>
      )}
    </div>
  );
};

export default StoreForm;

