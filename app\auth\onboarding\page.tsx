// app/auth/registration/page.tsx
"use client";
import React, { useState, useEffect } from 'react';
import { submitOnboardingData } from '@/lib/api/users/onboarding/services';
import { OnboardingData, StoreOnboardingData, UserOnboardingData } from '@/lib/api/users/onboarding/models';
import StoreForm from './components/StoreForm';
import UserForm from './components/UserForm';
import { useRouter } from 'next/navigation';
import FormStepIndicator from './components/FormStepIndicator';
import { v4 as uuidv4 } from 'uuid';
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";

const initialStoreData: StoreOnboardingData = {
  address: '',
  category: '',
  city: '',
  country: '',
  currency: '',
  email: '',
  name: '',
  notes: '',
  opening_hours:'',
  payment_methods: '',
  phone_number: '',
  postal_code: '',
  state: '',
  status: 'active',
  street_address: '',
  tax_rate: 0,
};

const initialUserData: UserOnboardingData = {
  email: '',
  name: '',
  password: '',
  plan_id: 0,
  profile: '',
  role_id: 1,
  store_id: '',
  user_defined_role_id: 0,
};

const RegistrationPage = () => {
  const [step, setStep] = useState(1);
  const [storeData, setStoreData] = useState<StoreOnboardingData>(initialStoreData);
  const [userData, setUserData] = useState<UserOnboardingData>(initialUserData);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const totalSteps = 2;

  // Animation variants for page transitions
  const pageVariants = {
    initial: { opacity: 0, x: 100 },
    in: { opacity: 1, x: 0 },
    out: { opacity: 0, x: -100 }
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.5
  };

  const handleStoreDataChange = (data: Partial<StoreOnboardingData>) => {
    setStoreData({ ...storeData, ...data });
  };

  const handleUserDataChange = (data: Partial<UserOnboardingData>) => {
    setUserData({ ...userData, ...data });
  };

  const handleUserSubmit = () => {
    setStep(2);
  };

  const handleStoreSubmit = async () => {
    if (step === 2) {
      setLoading(true);
      const storeId = uuidv4();
      const finalUserData: UserOnboardingData = {
        ...userData,
        store_id: storeId
      };
      const data: OnboardingData = {
        store: storeData,
        user: finalUserData,
      };

      try {
        const response = await submitOnboardingData(data);
        if (response.success) {
          toast({
            title: "Registration Successful! 🎉",
            description: "Welcome to our platform. Redirecting to your dashboard...",
          });
          
          // Small delay before redirecting for better UX
          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        } else {
          toast({
            variant: "destructive",
            title: "Registration Failed",
            description: response.message || "An error occurred during registration.",
          });
        }
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "Error",
          description: error.message,
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-2 text-gray-900">Create Your Account</h1>
            <p className="text-gray-600">Get started with your online store in just a few steps</p>
          </div>
          
          <FormStepIndicator 
            currentStep={step} 
            totalSteps={totalSteps} 
            steps={["Account Details", "Store Setup"]} 
          />

          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="user-form"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <UserForm
                  userData={userData}
                  onUserDataChange={handleUserDataChange}
                  onSubmit={handleUserSubmit}
                />
              </motion.div>
            )}
            
            {step === 2 && (
              <motion.div
                key="store-form"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <StoreForm
                  storeData={storeData}
                  onStoreDataChange={handleStoreDataChange}
                  onSubmit={handleStoreSubmit}
                  isLastStep={true}
                  userEmail={userData.email}
                />
                
                <div className="text-center mt-6">
                  <button 
                    onClick={handleBack} 
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    disabled={loading}
                  >
                    ← Go back to account details
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {loading && (
            <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
                <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                <p className="text-gray-700">Creating your account...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RegistrationPage;
