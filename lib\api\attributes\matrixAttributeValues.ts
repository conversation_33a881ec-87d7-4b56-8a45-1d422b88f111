import { BASE_URL } from "@/app/configs/constants";
import { MatrixAttributesValues } from "./models";

interface MatrixAttributeValuesSchema {
  position: number,
  item_id: string,
  attribute_value_ids: string
}

export async function createMatrixAttributeValues(
  authToken: string,
  storeId: string,
  payload: MatrixAttributeValuesSchema[]
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });
    if (response.status !== 201) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create attribute value: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error creating attribute value:", error);
    throw error;
  }
}

export async function fetchMatrixAttributeValues(
  authToken: string,
  storeId: string,
  matrixId: string,
): Promise<MatrixAttributesValues[]> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}/${matrixId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch attribute values: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    throw error;
  }
}


export async function updateMatrixAttributeValues(
  authToken: string,
  storeId: string,
  matrixId: string,
  payload: MatrixAttributeValuesSchema[]
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}/${matrixId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });
    if (response.status !== 200) {
      const errorMessage = await response.json();
      throw new Error(`Failed to update attribute values: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error updating attribute values:", error);
    throw error;
  }
}