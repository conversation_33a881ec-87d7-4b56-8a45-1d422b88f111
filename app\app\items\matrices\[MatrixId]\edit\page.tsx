// app/app/items/matrices/[MatrixId]/edit/page.tsx

"use client";

import React, { useEffect, useState, useMemo } from "react";
import Link from "next/link";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { Category } from "@/lib/api/categories/models";
import { Brand } from "@/lib/api/brands/models";
import { AttributeConfig } from "../../create/AttributesSection";
import { fetchAttributes } from "@/lib/api/attributes/attributeService";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import { fetchStoreAttributeValues } from "@/lib/api/attributes/attributeValues";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchBrands } from "@/lib/api/brands/service";
import {
  getItemMatrix,
  updateItemMatrix,
} from "@/lib/api/items/matrices/service";
import {
  fetchMatrixAttributeValues,
  updateMatrixAttributeValues,
} from "@/lib/api/attributes/matrixAttributeValues";
import { ItemMatrix, ItemMatrixSchema } from "@/lib/api/items/matrices/models";
import { MatrixForm } from "../../MatrixForm";
import { Button } from "@/components/ui/button";

interface EditMatrixPageProps {
  params: { MatrixId: string };
}

interface MatrixAttribute {
  id: string;
  attribute_value_ids: string;
  position: number;
}

export default function EditMatrixPage({ params }: EditMatrixPageProps) {
  const router = useRouter();
  const { MatrixId: matrixId } = params;

  const [availableAttributes, setAvailableAttributes] = useState<Attributes[]>([]);
  const [availableAttributeValues, setAvailableAttributeValues] = useState<AttributeValues[]>([]);
  const [vendors, setSupplierData] = useState<Supplier[]>([]);
  const [categories, setCategoryData] = useState<Category[]>([]);
  const [brands, setBrandData] = useState<Brand[]>([]);
  const [attributes, setAttributes] = useState<AttributeConfig[]>([]);
  const [initialData, setInitialData] = useState<ItemMatrix | null>(null);
  const [originalMatrixAttributeValues, setOriginalMatrixAttributeValues] = useState<MatrixAttribute[]>([]);
  const [matrixName, setMatrixName] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  const buildAttributeConfigs = useMemo(
    () => (
      matrixAttributes: MatrixAttribute[],
      attributeValuesData: AttributeValues[],
      attributesData: Attributes[],
    ): AttributeConfig[] => {
      const groupedAttributes: Record<
        string,
        { attributeId: string; valueIds: string[]; position: number }
      > = {};

      matrixAttributes.forEach((matrixAttr) => {
        const attrValue = attributeValuesData.find(
          (val) => val.id === matrixAttr.attribute_value_ids,
        );
        if (!attrValue) return;
        const attributeId = attrValue.attribute_id;
        if (!groupedAttributes[attributeId]) {
          groupedAttributes[attributeId] = {
            attributeId,
            valueIds: [],
            position: matrixAttr.position,
          };
        }
        groupedAttributes[attributeId].valueIds.push(matrixAttr.attribute_value_ids);
      });

      return Object.values(groupedAttributes)
        .map((group) => {
          const attribute = attributesData.find((attr) => attr.id === group.attributeId);
          if (!attribute) return null;

          const attrValues = attributeValuesData.filter(
            (val) => val.attribute_id === group.attributeId,
          );

          return {
            attribute,
            values: attrValues,
            selectedValues: group.valueIds,
          };
        })
        .filter((config): config is AttributeConfig => config !== null);
    },
    [],
  );

  useEffect(() => {
    if (!matrixId) {
      setError(new Error("Matrix ID is missing from the URL."));
      setIsLoading(false);
      return;
    }
    if (!token || !store_id) {
      setError(new Error("Authentication token or store ID is missing. Please log in again."));
      setIsLoading(false);
      return;
    }

    const fetchMatrixData = async () => {
      setIsLoading(true);
      setError(null);
      setIsNotFound(false);

      try {
        const [
          matrix,
          attributesData,
          attributeValuesData,
          matrixAttributesResponse,
          suppliersData,
          categoriesData,
          brandsData,
        ] = await Promise.all([
          getItemMatrix(token, store_id, matrixId),
          fetchAttributes(token, store_id),
          fetchStoreAttributeValues(token, store_id),
          fetchMatrixAttributeValues(token, store_id, matrixId),
          fetchSuppliers(token, store_id),
          fetchCategories(token, store_id),
          fetchBrands(token, store_id),
        ]);

        setAvailableAttributes(attributesData);
        setAvailableAttributeValues(attributeValuesData);
        setSupplierData(suppliersData);
        setCategoryData(categoriesData);
        setBrandData(brandsData);
        setMatrixName(matrix.name);

        const matrixAttributes: MatrixAttribute[] = matrixAttributesResponse.map(attr => ({
          id: attr.id,
          attribute_value_ids: attr.attribute_value_id,
          position: attr.position,
        }));

        setOriginalMatrixAttributeValues(matrixAttributes);

        const attributeConfigs = buildAttributeConfigs(
          matrixAttributes,
          attributeValuesData,
          attributesData,
        );

        setInitialData(matrix);
        setAttributes(attributeConfigs);

      } catch (err: any) {
        setError(err instanceof Error ? err : new Error("Failed to fetch matrix data for editing."));
        console.error("Failed to fetch matrix data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMatrixData();
  }, [token, store_id, matrixId, buildAttributeConfigs]);

  const handleAddAttribute = (attributeId: string) => {
    const selectedAttribute = availableAttributes.find(
      (attr) => attr.id === attributeId,
    );
    if (!selectedAttribute) return;
    const attributeValues = availableAttributeValues.filter(
      (val) => val.attribute_id === attributeId,
    );
    setAttributes((prev) => {
      const existingAttribute = prev.find((attr) => attr.attribute.id === attributeId);
      if (existingAttribute) {
        return prev.map((attr) =>
          attr.attribute.id === attributeId ? { ...attr, values: attributeValues } : attr,
        );
      }
      return [
        ...prev,
        { attribute: selectedAttribute, values: attributeValues, selectedValues: [] },
      ];
    });
  };

  const handleToggleAttributeValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          const newSelectedValues = attr.selectedValues.includes(valueId)
            ? attr.selectedValues.filter((id) => id !== valueId)
            : [...attr.selectedValues, valueId];
          return { ...attr, selectedValues: newSelectedValues };
        }
        return attr;
      }),
    );
  };

  const handleRemoveAttribute = (attributeId: string) => {
    setAttributes((prev) => prev.filter((a) => a.attribute.id !== attributeId));
  };

  const handleAddValue = (attributeId: string, value: string) => {
    const newValueId = `temp-${Date.now()}`;
    setAvailableAttributeValues((prev) => [
      ...prev,
      {
        id: newValueId,
        attribute_id: attributeId,
        value: value,
        store_id: store_id || "",
        date_created: new Date().toISOString(),
      } as AttributeValues,
    ]);
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          return {
            ...attr,
            values: [...attr.values, { id: newValueId, attribute_id: attributeId, value: value, store_id: store_id || "", date_created: new Date().toISOString() } as AttributeValues]
          };
        }
        return attr;
      }),
    );
  };

  const handleRemoveValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          return {
            ...attr,
            values: attr.values.filter((v) => v.id !== valueId),
            selectedValues: attr.selectedValues.filter((id) => id !== valueId),
          };
        }
        return attr;
      }),
    );
    setAvailableAttributeValues((prev) => prev.filter(val => val.id !== valueId));
  };

  const onAddBulkValues = async (attributeId: string, payload: AttributeValuesSchema) => {
    console.log(`Adding bulk values for attribute ${attributeId}:`, payload.values);
  };

  const handleSubmit = async (data: ItemMatrixSchema, currentAttributes: AttributeConfig[]) => {
    if (!token || !store_id || !matrixId) {
      console.error("Authentication token, store ID, or Matrix ID is missing.");
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const matrixData: ItemMatrixSchema = {
        ...data,
        store_id: store_id,
      };
      await updateItemMatrix(token, store_id, matrixId, matrixData);

      const existingValueIds = originalMatrixAttributeValues.map((v) => v.attribute_value_ids);
      const newValueIds = currentAttributes.flatMap((attr) => attr.selectedValues);

      const valuesToAdd = newValueIds.filter((id) => !existingValueIds.includes(id));

      const matrixAttrRecordsToRemove = originalMatrixAttributeValues.filter(
        (v) => !newValueIds.includes(v.attribute_value_ids)
      );

      if (newValueIds.length > 0) {
        const matrixAttributesPayload = currentAttributes.flatMap((attr, position) =>
          attr.selectedValues.map((valueId) => ({
            attribute_value_ids: valueId,
            item_id: matrixId,
            position,
          })),
        );
        await updateMatrixAttributeValues(token, store_id, matrixId, matrixAttributesPayload);
      }

      router.push("/app/items/matrices");

    } catch (submitError: any) {
      console.error("Failed to update item matrix:", submitError);
      setError(submitError instanceof Error ? submitError : new Error("Failed to save changes."));
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    router.push("/app/items/matrices");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading matrix details for editing...</p>
        </div>
      </div>
    );
  }

  if (error && !initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Matrix Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items/matrices">
              <Button variant="link">Return to Matrices</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Matrix Not Found</h2>
          <p className="text-gray-600 text-center">The matrix with ID ({matrixId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items/matrices">
              <Button variant="link">Return to Matrices</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Matrix data could not be prepared for editing.</p>
        <Link href="/app/items/matrices" className="ml-4">
          <Button variant="link">Return to Matrices</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/items" className="hover:text-blue-600">Items</Link>
            <span className="mx-2">/</span>
            <Link href="/app/items/matrices" className="hover:text-blue-600">Matrices</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit: {matrixName || `Matrix ${matrixId}`}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6">
        {error && isSaving === false && (
          <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Save Error: </strong>
            <span className="block sm:inline">{error.message}</span>
          </div>
        )}

        <MatrixForm
          initialData={initialData}
          attributes={attributes}
          availableAttributes={availableAttributes}
          availableAttributeValues={availableAttributeValues}
          vendors={vendors}
          categories={categories}
          brands={brands}
          storeId={store_id!}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          onAddAttribute={handleAddAttribute}
          onRemoveAttribute={handleRemoveAttribute}
          onToggleValue={handleToggleAttributeValue}
          onAddValue={handleAddValue}
          onRemoveValue={handleRemoveValue}
          onAddBulkValues={onAddBulkValues}
          loading={false}
          saving={isSaving}
        />
      </div>
    </div>
  );
}