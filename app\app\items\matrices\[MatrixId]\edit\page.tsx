// app/app/items/matrices/[MatrixId]/edit/page.tsx

"use client";

import React, { useEffect, useState, useMemo } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { Category } from "@/lib/api/categories/models";
import { Brand } from "@/lib/api/brands/models";
import { AttributeConfig } from "../../create/AttributesSection";
import { fetchAttributes } from "@/lib/api/attributes/attributeService";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import {
  fetchStoreAttributeValues,
} from "@/lib/api/attributes/attributeValues";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchBrands } from "@/lib/api/brands/service";
import {
  getItemMatrix,
  updateItemMatrix,
} from "@/lib/api/items/matrices/service";
import {
  fetchMatrixAttributeValues,
  updateMatrixAttributeValues,
  //updateMatrixAttributeValues,
  // deleteMatrixAttributeValue,
} from "@/lib/api/attributes/matrixAttributeValues";
import { ItemMatrix, ItemMatrixSchema } from "@/lib/api/items/matrices/models";
import { MatrixForm } from "../../MatrixForm";

interface EditMatrixPageProps {
  params: { MatrixId: string };
}

interface MatrixAttribute {
  id: string;
  attribute_value_ids: string;
  position: number;
}

export default function EditMatrixPage({ params }: EditMatrixPageProps) {
  const router = useRouter();
  const { MatrixId: matrixId } = params;

  const [availableAttributes, setAvailableAttributes] = useState<Attributes[]>([]);
  const [availableAttributeValues, setAvailableAttributeValues] = useState<AttributeValues[]>([]);
  const [vendors, setSupplierData] = useState<Supplier[]>([]);
  const [categories, setCategoryData] = useState<Category[]>([]);
  const [brands, setBrandData] = useState<Brand[]>([]);
  const [attributes, setAttributes] = useState<AttributeConfig[]>([]);
  const [initialData, setInitialData] = useState<ItemMatrix | null>(null);
  const [originalMatrixAttributeValues, setOriginalMatrixAttributeValues] = useState<MatrixAttribute[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");


  const buildAttributeConfigs = useMemo(
    () => (
      matrixAttributes: any[],
      attributeValuesData: AttributeValues[],
      attributesData: Attributes[],
    ): AttributeConfig[] => {
      const groupedAttributes: Record<
        string,
        { attributeId: string; valueIds: string[]; position: number }
      > = {};

      matrixAttributes.forEach((matrixAttr) => {
        const attrValue = attributeValuesData.find(
          (val) => val.id === matrixAttr.attribute_value_ids,
        );
        if (!attrValue) return;

        const attributeId = attrValue.attribute_id;

        if (!groupedAttributes[attributeId]) {
          groupedAttributes[attributeId] = {
            attributeId,
            valueIds: [],
            position: matrixAttr.position,
          };
        }

        groupedAttributes[attributeId].valueIds.push(matrixAttr.attribute_value_ids);
      });

      return Object.values(groupedAttributes).map((group) => {
        const attribute = attributesData.find((attr) => attr.id === group.attributeId);
        if (!attribute) {
          throw new Error(`Attribute with ID ${group.attributeId} not found.`);
        }

        const attrValues = attributeValuesData.filter(
          (val) => val.attribute_id === group.attributeId,
        );

        return {
          attribute,
          values: attrValues,
          selectedValues: group.valueIds,
        };
      });
    },
    [],
  );

  useEffect(() => {
    const fetchMatrixData = async () => {
      if (!token || !store_id || !matrixId) return;

      setLoading(true);
      try {
        const [
          matrix,
          attributesData,
          attributeValuesData,
          matrixAttributesResponse,
          suppliersData,
          categoriesData,
          brandsData,
        ] = await Promise.all([
          getItemMatrix(token, store_id, matrixId),
          fetchAttributes(token, store_id),
          fetchStoreAttributeValues(token, store_id),
          fetchMatrixAttributeValues(token, store_id, matrixId),
          fetchSuppliers(token, store_id),
          fetchCategories(token, store_id),
          fetchBrands(token, store_id),
        ]);

        setAvailableAttributes(attributesData);
        setAvailableAttributeValues(attributeValuesData);

        const matrixAttributes: MatrixAttribute[] = matrixAttributesResponse.map(attr => ({
          id: attr.id,
          attribute_value_ids: attr.attribute_value_id,
          position: attr.position,
        }));

        setOriginalMatrixAttributeValues(matrixAttributes);
        setSupplierData(suppliersData);
        setCategoryData(categoriesData);
        setBrandData(brandsData);

        // Build attribute config
        const attributeConfigs = buildAttributeConfigs(
          matrixAttributes,
          attributeValuesData,
          attributesData,
        );

        setInitialData(matrix);
        setAttributes(attributeConfigs);
      } catch (error) {
        console.error("Failed to fetch matrix data:", error);
        // Handle error (show notification, etc.)
      } finally {
        setLoading(false);
      }
    };

    fetchMatrixData();
  }, [token, store_id, matrixId, buildAttributeConfigs]);

  const handleAddAttribute = (attributeId: string) => {
    const selectedAttribute = availableAttributes.find(
      (attr) => attr.id === attributeId,
    );

    if (!selectedAttribute) return;

    const attributeValues = availableAttributeValues.filter(
      (val) => val.attribute_id === attributeId,
    );

    setAttributes((prev) => {
      const existingAttribute = prev.find(
        (attr) => attr.attribute.id === attributeId,
      );

      if (existingAttribute) {
        return prev.map((attr) =>
          attr.attribute.id === attributeId ? { ...attr, values: attributeValues } : attr,
        );
      }

      return [
        ...prev,
        {
          attribute: selectedAttribute,
          values: attributeValues,
          selectedValues: [],
        },
      ];
    });
  };

  const handleToggleAttributeValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          const newSelectedValues = attr.selectedValues.includes(valueId)
            ? attr.selectedValues.filter((id) => id !== valueId)
            : [...attr.selectedValues, valueId];

          return {
            ...attr,
            selectedValues: newSelectedValues,
          };
        }
        return attr;
      }),
    );
  };

  const handleRemoveAttribute = (attributeId: string) => {
    setAttributes((prev) => prev.filter((a) => a.attribute.id !== attributeId));
  };

  const handleAddValue = (attributeId: string, value: string) => {
    const newValueId = Date.now().toString(); // Replace with your actual logic to create a new value ID
    setAvailableAttributeValues((prev) => [
      ...prev,
      {
        id: newValueId,
        attribute_id: attributeId,
        value: value,
        store_id: store_id || "",
        date_created: new Date().toISOString(),
      } as AttributeValues,
    ]);
  };

  const handleRemoveValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          return {
            ...attr,
            values: attr.values.filter((v) => v.id !== valueId),
            selectedValues: attr.selectedValues.filter((id) => id !== valueId),
          };
        }
        return attr;
      }),
    );
  };

  const onAddBulkValues = async (
    attributeId: string,
    payload: AttributeValuesSchema,
  ) => {
    // Implement your actual API call and update state accordingly
    console.log(
      `Adding bulk values for attribute ${attributeId}:`,
      payload.values,
    );
  };

  const handleSubmit = async (data: ItemMatrixSchema, attributes: AttributeConfig[]) => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }

    try {
      setSaving(true);

      const matrixData: ItemMatrixSchema = {
        ...data,
        store_id: store_id,
      };

      await updateItemMatrix(token, store_id, matrixId, matrixData);

      // 2. Update the matrix attribute values
      // First, determine which values to add/remove
      const existingValueIds = originalMatrixAttributeValues.map((v) => v.attribute_value_ids);
      const newValueIds = attributes.flatMap((attr) => attr.selectedValues);

      // Values to add (not in existing)
      const valuesToAdd = newValueIds.filter((id) => !existingValueIds.includes(id));

      // Values to remove (in existing but not in new)
      const valuesToRemove = existingValueIds.filter((id) => !newValueIds.includes(id));

      // Remove values that are no longer selected
      for (const valueId of valuesToRemove) {
        const matrixAttrValue = originalMatrixAttributeValues.find(
          (v) => v.attribute_value_ids === valueId,
        );
        // if (matrixAttrValue) {
        //   await deleteMatrixAttributeValue(token, store_id, matrixAttrValue.id);
        // }
      }

      // Add new values
      if (valuesToAdd.length > 0) {
        const matrixAttributesValues = attributes.flatMap((attr, position) =>
          attr.selectedValues
            .filter((valueId) => valuesToAdd.includes(valueId))
            .map((valueId) => ({
              attribute_value_ids: valueId,
              item_id: matrixId,
              position,
            })),
        );

        await updateMatrixAttributeValues(token, store_id, matrixId, matrixAttributesValues);
      }

      router.push("/app/items/matrices");
    } catch (error) {
      console.error("Failed to update item matrix:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push("/app/items/matrices");
  };

  if (loading || !initialData) {
    return <div>Loading...</div>;
  }

  return (
    <MatrixForm
      initialData={initialData}
      attributes={attributes}
      availableAttributes={availableAttributes}
      availableAttributeValues={availableAttributeValues}
      vendors={vendors}
      categories={categories}
      brands={brands}
      storeId={store_id!}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      onAddAttribute={handleAddAttribute}
      onRemoveAttribute={handleRemoveAttribute}
      onToggleValue={handleToggleAttributeValue}
      onAddValue={handleAddValue}
      onRemoveValue={handleRemoveValue}
      onAddBulkValues={onAddBulkValues}
      loading={loading}
      saving={saving}
    />
  );
}