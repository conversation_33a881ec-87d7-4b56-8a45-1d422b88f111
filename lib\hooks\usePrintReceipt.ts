import { Receipt } from "@/lib/api/receipts/models";

export const usePrintReceipt = () => {
  const printReceipt = (receipt: Receipt) => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Add necessary styles for thermal receipt printing
    const styles = `
      @page {
        size: 80mm auto;  /* Standard thermal receipt width */
        margin: 0;
      }
      body {
        font-family: 'Courier New', Courier, monospace;
        margin: 0;
        padding: 10px;
        font-size: 12px;
        line-height: 1.4;
        width: 80mm;
      }
      .receipt {
        width: 100%;
      }
      .header {
        text-align: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #000;
      }
      .header h2 {
        font-size: 16px;
        margin: 0 0 5px 0;
        text-transform: uppercase;
        font-weight: bold;
      }
      .header p {
        margin: 2px 0;
      }
      .info {
        margin-bottom: 15px;
        padding: 10px 0;
        border-bottom: 1px dashed #000;
      }
      .info p {
        margin: 2px 0;
        font-size: 11px;
      }
      .items {
        margin-bottom: 15px;
        width: 100%;
      }
      .items table {
        width: 100%;
        border-collapse: collapse;
      }
      .items th {
        text-align: left;
        padding: 5px 4px;
        border-bottom: 1px solid #000;
        font-size: 11px;
        text-transform: uppercase;
      }
      .items td {
        text-align: left;
        padding: 4px;
        font-size: 12px;
      }
      .total {
        border-top: 1px dashed #000;
        margin: 10px 0;
        padding-top: 5px;
        font-weight: bold;
      }
      .total p {
        margin: 2px 0;
        text-align: right;
        font-size: 14px;
      }
      .footer {
        text-align: center;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px dashed #000;
      }
      .footer p {
        margin: 2px 0;
        font-size: 11px;
      }
      .powered-by {
        margin-top: 20px;
        text-align: center;
        font-size: 10px;
        color: #666;
      }
    `;

    const date = new Date(receipt.created_at);
    const formattedDate = date.toLocaleDateString();
    const formattedTime = date.toLocaleTimeString();

    // Format items table
    const itemsTable = receipt.items.map(item => `
      <tr>
        <td>${item.name}</td>
        <td style="text-align: center">${item.quantity}</td>
        <td style="text-align: right">${item.amount.toFixed(2)}</td>
      </tr>
    `).join('');

    // Write the receipt content
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt ${receipt.receipt_number}</title>
          <style>${styles}</style>
        </head>
        <body>
          <div class="receipt">
            <div class="header">
              <h2>${receipt.store.name}</h2>
              ${receipt.store.address ? `<p>${receipt.store.address}</p>` : ''}
              ${receipt.store.phone ? `<p>Tel: ${receipt.store.phone}</p>` : ''}
              <p>${receipt.store.city || ''}, ${receipt.store.country || ''}</p>
              ${receipt.store.postal_code ? `<p>P.O BOX ${receipt.store.postal_code}</p>` : ''}
            </div>
            
            <div class="info">
              <p><strong>Receipt #:</strong> ${receipt.receipt_number}</p>
              <p><strong>Date:</strong> ${formattedDate}</p>
              <p><strong>Time:</strong> ${formattedTime}</p>
            </div>

            <div class="items">
              <table>
                <thead>
                  <tr>
                    <th>Item</th>
                    <th style="text-align: center">Qty</th>
                    <th style="text-align: right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsTable}
                </tbody>
              </table>
            </div>

            <div class="total">
              <p>Total: ${receipt.store.currency || ''} ${receipt.total.toFixed(2)}</p>
            </div>

            <div class="footer">
              <p><strong>Served by:</strong> ${receipt.salesperson}</p>
              <p>Thank you for your business!</p>
              <p>Please come again</p>
            </div>
            
            <div class="powered-by">
              Powered by StoreYako POS
            </div>
          </div>
        </body>
      </html>
    `);

    // Print and close
    setTimeout(() => {
      printWindow.print();
      // Close the window after printing (or if printing is cancelled)
      setTimeout(() => {
        printWindow.close();
      }, 1000);
    }, 500);
  };

  return { printReceipt };
};
