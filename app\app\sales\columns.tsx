import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sale } from "@/lib/api/sales/models";
import { User } from "@/lib/api/users/models";
import { Item } from "@/lib/api/items/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export const getColumns = (users: User[], items: Item[], router: AppRouterInstance): ColumnDef<Sale>[] => [
  //export const columns: ColumnDef<Sale>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "item_id",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Item
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const item = items.find((item) => item.id === row.getValue("item_id"));
      return <div>{item?.name}</div>;
    },
    //<div>{row.getValue("item_id")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "salesperson_id",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Salesperson
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const user = users.find(
        (user) => user.id === row.getValue("salesperson_id"),
      );
      return <div>{user?.name}</div>;
    },
    //<div>{row.getValue("salesperson_id")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "quantity",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Quantity
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("quantity")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "sale_time",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Sale Time
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const rawTime = row.getValue<string>("sale_time");
      const formattedTime = new Date(rawTime).toDateString();
      return <div>{formattedTime}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("status")}</div>,
    enableSorting: true,
  },

  {
    accessorKey: "total",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Total
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("total")}</div>,
    enableSorting: true,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const sale = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(sale.id)}
            >
              Copy Sale ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View Details</DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                router.push(`/app/sales/${sale.id}/edit`);
              }}
            >
              Edit Customer
            </DropdownMenuItem>

          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
