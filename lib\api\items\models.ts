// export interface Item {
//   id: string;
//   store_id?: string;
//   name: string;
//   image?: string;
//   quantity: number;
//   brand: string;
//   category: string;
//   default_cost: number;
//   default_price: number;
//   has_discount: boolean;
//   discount?: number | null;
//   date_created: string;
//   date_updated?: string | null;
//   tags: Record<string, unknown>;
//   vendor_id?: string | null;
//   is_variant: boolean;
//   parent_item_id?: string | null;
//   sku?: string | null;
//   description?: string | null;
//   notes?: string | null;
//   barcode?: string | null;
// }

// export interface ItemSchema {
//   name: string;
//   quantity: number;
//   default_cost: number;
//   default_price: number;
//   brand: string;
//   has_discount: boolean;
//   date_created: string;
//   tags: string[],
//   category: string;
//   store_id: string;
//   vendor_id: string;
//   image: File | string;
//   description: string;
//   sku: string;
//   barcode: string;
//   discount?: number;
//   notes: string;
//   is_variant: boolean;
//   parent_item_id?: string | null;
// }


export interface Item {
  id: string;
  store_id?: string;
  name: string;
  image?: string;
  quantity: number;
  brand: string;
  category: string;
  default_cost: number;
  default_price: number;
  has_discount: boolean;
  discount?: number | null;
  date_created: string;
  date_updated?: string | null;
  tags: Record<string, unknown>;
  vendor_id?: string | null;
  is_variant: boolean;
  parent_item_id?: string | null;
  sku?: string | null;
  description?: string | null;
  notes?: string | null;
  barcode?: string | null;
}

export interface ItemSchema {
  name: string;
  quantity: number;
  default_cost: number;
  default_price: number;
  brand: string;
  has_discount: boolean;
  date_created: string;
  tags: string[];
  category: string;
  store_id: string;
  vendor_id: string;
  image: File | string;
  description: string;
  sku?: string;
  barcode?: string;
  discount?: number;
  notes: string;
  is_variant: boolean;
  parent_item_id?: string | null;
}