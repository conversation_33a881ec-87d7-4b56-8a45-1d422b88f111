// app/app/stores/StoreForm.tsx
"use client";

import React, { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { StoreSchema } from "@/lib/api/retailStores/models";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { useRouter } from "next/navigation";
import { uploadFile } from "@/lib/api/uploads/service"; // Import uploadFile
import Cookies from "js-cookie";


interface StoreFormProps {
    initialData?: StoreSchema;
    onSubmit: (data: StoreSchema) => Promise<void>;
    isEditing?: boolean;
}

export function StoreForm({
    initialData,
    onSubmit,
    isEditing = false,
}: StoreFormProps) {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<StoreSchema>({
        defaultValues: initialData || {
            name: "",
            currency: "USD",
            street_address: "",
            city: "",
            state: "",
            postal_code: "",
            country: "",
            phone_number: "",
            email: "",
            opening_hours: "",
            category: "",
            payment_methods: "",
            notes: "",
            status: "incomplete",
            image_url: "",
        },
    });

    const [formError, setFormError] = useState<string | null>(null);
    const [formSuccess, setFormSuccess] = useState<boolean>(false);
    const [uploading, setUploading] = useState(false);
    const router = useRouter();

    const currencies = [
        { code: "USD", label: "US Dollar" },
        { code: "EUR", label: "Euro" },
        { code: "GBP", label: "British Pound" },
        { code: "KES", label: "Kenyan Shilling" },
        { code: "UGX", label: "Ugandan Shilling" },
        { code: "RWF", label: "Rwandan Franc" },
        { code: "TZS", label: "Tanzanian Shilling" },
    ];

    const handleImageChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (file) {
                setValue("image", file);
            }
        },
        [setValue],
    );

    const handleFormSubmit = async (data: StoreSchema) => {
        setFormError(null);
        setFormSuccess(false);
        setUploading(true);

        try {
            const file = data.image;
            if (file instanceof File) {
                const authToken = Cookies.get("auth_token") || "";
                const folder = "store_logos";
                const imageUrl = await uploadFile(authToken, folder, file);
                data.image_url = imageUrl;
            }
            delete data.image;
            await onSubmit(data);
            setFormSuccess(true);
            reset(initialData);
            router.push("/app/stores");
        } catch (error) {
            setFormError((error as Error).message);
        } finally {
            setUploading(false);
        }
    };

    const handleCancel = () => {
        router.back();
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>{isEditing ? "Edit Store" : "Add New Store"}</CardTitle>
            </CardHeader>
            <form onSubmit={handleSubmit(handleFormSubmit)}>
                <CardContent className="space-y-6">
                    {formError && <AlertError message={formError} />}
                    {formSuccess && (
                        <AlertSuccess
                            message={`Store ${isEditing ? "updated" : "created"} successfully`}
                        />
                    )}
                    {/* Basic Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Basic Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="name">Store Name *</Label>
                            <Input
                                id="name"
                                {...register("name", { required: "Store name is required" })}
                                placeholder="Enter store name"
                            />
                            {errors.name && (
                                <p className="text-sm text-red-500">{errors.name.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="image_url">Store Logo</Label>
                            <Input
                                id="image_url"
                                type="file"
                                accept="image/*"
                                onChange={handleImageChange}
                            />
                        </div>
                        <div className="flex flex-col grid:md grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="currency">Currency *</Label>
                                <Select
                                    defaultValue={initialData?.currency}
                                    onValueChange={(value) => setValue("currency", value)}
                                >
                                    <SelectTrigger id="currency">
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {currencies.map((currency) => (
                                            <SelectItem key={currency.code} value={currency.code}>
                                                {currency.label} ({currency.code})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.currency && (
                                    <p className="text-sm text-red-500">
                                        {errors.currency.message}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="category">Store Category</Label>
                                <Input
                                    id="category"
                                    {...register("category")}
                                    placeholder="e.g., Retail, Restaurant, Service"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col md:grid md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="email">Email Address</Label>
                            <Input
                                id="email"
                                type="email"
                                {...register("email", {
                                    pattern: {
                                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                                        message: "Invalid email format",
                                    },
                                })}
                                placeholder="<EMAIL>"
                            />
                            {errors.email && (
                                <p className="text-sm text-red-500">{errors.email.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone_number">Phone Number</Label>
                            <Input
                                id="phone_number"
                                {...register("phone_number", {
                                    pattern: {
                                        value: /^\+?[\d\s-]+$/,
                                        message: "Invalid phone number format",
                                    },
                                })}
                                placeholder="****** 567 8900"
                            />
                            {errors.phone_number && (
                                <p className="text-sm text-red-500">
                                    {errors.phone_number.message}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Address Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Address Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="street_address">Street Address</Label>
                            <Input
                                id="street_address"
                                {...register("street_address")}
                                placeholder="123 Store Street"
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="city">City</Label>
                                <Input id="city" {...register("city")} placeholder="City" />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="state">State/Province</Label>
                                <Input id="state" {...register("state")} placeholder="State" />
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="postal_code">Postal Code</Label>
                                <Input
                                    id="postal_code"
                                    {...register("postal_code")}
                                    placeholder="Postal Code"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="country">Country</Label>
                                <Input
                                    id="country"
                                    {...register("country")}
                                    placeholder="Country"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Additional Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="opening_hours">Opening Hours</Label>
                            <Input
                                id="opening_hours"
                                {...register("opening_hours")}
                                placeholder="e.g., Mon-Fri: 9AM-6PM"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="notes">Additional Notes</Label>
                            <Textarea
                                id="notes"
                                {...register("notes")}
                                placeholder="Any additional information about the store..."
                                rows={3}
                            />
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={handleCancel}>
                        Cancel
                    </Button>
                    <Button type="submit" disabled={uploading}>
                        {uploading ? "Uploading..." : isEditing ? "Update Store" : "Create Store"}
                    </Button>
                </CardFooter>
            </form>
        </Card>
    );
}