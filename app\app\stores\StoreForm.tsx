// app/app/stores/StoreForm.tsx
"use client";

import React, { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { StoreSchema } from "@/lib/api/retailStores/models";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { useRouter } from "next/navigation";
import { uploadFileToFirebase, validateFile } from "@/lib/firebase/storage";
import Cookies from "js-cookie";
import { toast } from "sonner";

const storeFormSchema = z.object({
    name: z.string().min(1, "Store name is required"),
    currency: z.string().max(3, "Currency code must be 3 characters or less").optional(),
    street_address: z.string().max(100, "Street address must be 100 characters or less").optional(),
    city: z.string().max(50, "City must be 50 characters or less").optional(),
    state: z.string().max(50, "State must be 50 characters or less").optional(),
    postal_code: z.string().max(20, "Postal code must be 20 characters or less").optional(),
    address: z.string().max(100, "Address must be 100 characters or less").optional(),
    country: z.string().max(50, "Country must be 50 characters or less").optional(),
    phone_number: z.string().optional(),
    email: z.string().email("Invalid email format").optional(),
    opening_hours: z.string().max(100, "Opening hours must be 100 characters or less").optional(),
    category: z.string().max(100, "Category must be 100 characters or less").optional(),
    tax_rate: z.number().min(0).max(100).optional(),
    payment_methods: z.string().max(255, "Payment methods must be 255 characters or less").optional(),
    notes: z.string().max(1000, "Notes must be 1000 characters or less").optional(),
    status: z.string().max(20, "Status must be 20 characters or less").optional(),
    image_url: z.string().optional(),
    image: z.any().optional(),
});

type StoreFormData = z.infer<typeof storeFormSchema>;

interface StoreFormProps {
    initialData?: StoreSchema;
    onSubmit: (data: StoreSchema) => Promise<void>;
    isEditing?: boolean;
}

export function StoreForm({
    initialData,
    onSubmit,
    isEditing = false,
}: StoreFormProps) {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<StoreFormData>({
        resolver: zodResolver(storeFormSchema),
        defaultValues: initialData || {
            name: "",
            currency: "USD",
            street_address: "",
            city: "",
            state: "",
            postal_code: "",
            country: "",
            phone_number: "",
            email: "",
            opening_hours: "",
            category: "",
            payment_methods: "",
            notes: "",
            status: "incomplete",
            image_url: "",
        },
    });

    const [formError, setFormError] = useState<string | null>(null);
    const [formSuccess, setFormSuccess] = useState<boolean>(false);
    const [uploading, setUploading] = useState(false);
    const router = useRouter();

    const currencies = [
        { code: "USD", label: "US Dollar" },
        { code: "EUR", label: "Euro" },
        { code: "GBP", label: "British Pound" },
        { code: "KES", label: "Kenyan Shilling" },
        { code: "UGX", label: "Ugandan Shilling" },
        { code: "RWF", label: "Rwandan Franc" },
        { code: "TZS", label: "Tanzanian Shilling" },
    ];

    const handleImageChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (file) {
                setValue("image", file);
            }
        },
        [setValue],
    );

    const handleFormSubmit = async (data: StoreFormData) => {
        setFormError(null);
        setFormSuccess(false);
        setUploading(true);

        try {
            const file = data.image;
            if (file instanceof File) {
                // Validate file before upload
                const validation = validateFile(file);
                if (!validation.isValid) {
                    throw new Error(validation.error);
                }

                toast.info("Uploading store logo to Firebase...");
                const folder = "store_logos";
                const imageUrl = await uploadFileToFirebase(file, folder);
                data.image_url = imageUrl;
                toast.success("Store logo uploaded successfully!");
            }
            delete data.image;
            await onSubmit(data);
            setFormSuccess(true);
            reset(initialData);
            router.push("/app/stores");
        } catch (error) {
            setFormError((error as Error).message);
        } finally {
            setUploading(false);
        }
    };

    const handleCancel = () => {
        router.back();
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>{isEditing ? "Edit Store" : "Add New Store"}</CardTitle>
            </CardHeader>
            <form onSubmit={handleSubmit(handleFormSubmit)}>
                <CardContent className="space-y-6">
                    {formError && <AlertError message={formError} />}
                    {formSuccess && (
                        <AlertSuccess
                            message={`Store ${isEditing ? "updated" : "created"} successfully`}
                        />
                    )}
                    {/* Basic Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Basic Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="name">Store Name *</Label>
                            <Input
                                id="name"
                                className={errors.name ? "border-red-500" : ""}
                                {...register("name")}
                                placeholder="Enter store name"
                            />
                            {errors.name && (
                                <p className="text-sm text-red-500">{errors.name.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="image_url">Store Logo</Label>
                            <Input
                                id="image_url"
                                type="file"
                                accept="image/*"
                                onChange={handleImageChange}
                            />
                        </div>
                        <div className="flex flex-col grid:md grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="currency">Currency</Label>
                                <Select
                                    defaultValue={initialData?.currency}
                                    onValueChange={(value) => setValue("currency", value)}
                                >
                                    <SelectTrigger 
                                        id="currency"
                                        className={errors.currency ? "border-red-500" : ""}
                                    >
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {currencies.map((currency) => (
                                            <SelectItem key={currency.code} value={currency.code}>
                                                {currency.label} ({currency.code})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.currency && (
                                    <p className="text-sm text-red-500">
                                        {errors.currency.message}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="category">Store Category</Label>
                                <Input
                                    id="category"
                                    className={errors.category ? "border-red-500" : ""}
                                    {...register("category")}
                                    placeholder="e.g., Retail, Restaurant, Service"
                                />
                                {errors.category && (
                                    <p className="text-sm text-red-500">{errors.category.message}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex flex-col md:grid md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="email">Email Address</Label>
                            <Input
                                id="email"
                                type="email"
                                className={errors.email ? "border-red-500" : ""}
                                {...register("email")}
                                placeholder="<EMAIL>"
                            />
                            {errors.email && (
                                <p className="text-sm text-red-500">{errors.email.message}</p>
                            )}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone_number">Phone Number</Label>
                            <Input
                                id="phone_number"
                                className={errors.phone_number ? "border-red-500" : ""}
                                {...register("phone_number")}
                                placeholder="****** 567 8900"
                            />
                            {errors.phone_number && (
                                <p className="text-sm text-red-500">
                                    {errors.phone_number.message}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Address Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Address Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="street_address">Street Address</Label>
                            <Input
                                id="street_address"
                                className={errors.street_address ? "border-red-500" : ""}
                                {...register("street_address")}
                                placeholder="123 Store Street"
                            />
                            {errors.street_address && (
                                <p className="text-sm text-red-500">{errors.street_address.message}</p>
                            )}
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="city">City</Label>
                                <Input 
                                    id="city" 
                                    className={errors.city ? "border-red-500" : ""}
                                    {...register("city")} 
                                    placeholder="City" 
                                />
                                {errors.city && (
                                    <p className="text-sm text-red-500">{errors.city.message}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="state">State/Province</Label>
                                <Input 
                                    id="state" 
                                    className={errors.state ? "border-red-500" : ""}
                                    {...register("state")} 
                                    placeholder="State" 
                                />
                                {errors.state && (
                                    <p className="text-sm text-red-500">{errors.state.message}</p>
                                )}
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="postal_code">Postal Code</Label>
                                <Input
                                    id="postal_code"
                                    className={errors.postal_code ? "border-red-500" : ""}
                                    {...register("postal_code")}
                                    placeholder="Postal Code"
                                />
                                {errors.postal_code && (
                                    <p className="text-sm text-red-500">{errors.postal_code.message}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="country">Country</Label>
                                <Input
                                    id="country"
                                    className={errors.country ? "border-red-500" : ""}
                                    {...register("country")}
                                    placeholder="Country"
                                />
                                {errors.country && (
                                    <p className="text-sm text-red-500">{errors.country.message}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Additional Information</h3>

                        <div className="space-y-2">
                            <Label htmlFor="opening_hours">Opening Hours</Label>
                            <Input
                                id="opening_hours"
                                className={errors.opening_hours ? "border-red-500" : ""}
                                {...register("opening_hours")}
                                placeholder="e.g., Mon-Fri: 9AM-6PM"
                            />
                            {errors.opening_hours && (
                                <p className="text-sm text-red-500">{errors.opening_hours.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="notes">Additional Notes</Label>
                            <Textarea
                                id="notes"
                                className={errors.notes ? "border-red-500" : ""}
                                {...register("notes")}
                                placeholder="Any additional information about the store..."
                                rows={3}
                            />
                            {errors.notes && (
                                <p className="text-sm text-red-500">{errors.notes.message}</p>
                            )}
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={handleCancel}>
                        Cancel
                    </Button>
                    <Button type="submit" disabled={uploading}>
                        {uploading ? "Uploading..." : isEditing ? "Update Store" : "Create Store"}
                    </Button>
                </CardFooter>
            </form>
        </Card>
    );
}