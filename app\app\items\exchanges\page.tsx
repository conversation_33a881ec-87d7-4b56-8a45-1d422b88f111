"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Plus, Trash2, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";

import { getColumns } from "./columns";
import Cookies from "js-cookie";
import { Exchange } from "@/lib/api/items/exchanges/models";
import { fetchExchanges } from "@/lib/api/items/exchanges/service";
import { Item } from "@/lib/api/items/models";
import { User } from "@/lib/api/users/models";
import { fetchItems } from "@/lib/api/items/service";
import { list_users } from "@/lib/api/users/service";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function Page() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const router = useRouter();
  const [data, setExchanges] = useState<Exchange[]>([]);

  const [items, setItems] = useState<Item[]>([]);
  const [staff, setStaff] = useState<User[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const token = Cookies.get("auth_token");

  const store_id = Cookies.get("active_store");

  useEffect(() => {
    async function getExchanges() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const fetchedExchanges = await fetchExchanges(token, store_id);
          setExchanges(fetchedExchanges);
          const fetchedUsers = await list_users(token, store_id);
          // Map UserInfoResponse[] to User[]
          setStaff(
            fetchedUsers.map((user: any) => ({
              id: user.id,
              name: user.name,
              email: user.email,
              confirm: user.confirm,
              confirmedOn: user.confirmedOn,
              roleId: user.roleId ?? 0, // Provide a default or handle appropriately
              hasAccess: user.hasAccess,
              profile: user.profile,
              planId: user.planId,
              userDefinedRoleId: user.userDefinedRoleId,
              createdOn: user.createdOn,
              updatedAt: user.updatedAt,
              lastSeen: user.lastSeen,
            }))
          );
          const fetchedItems = await fetchItems(token, store_id);
          setItems(fetchedItems);
        } catch (error) {
          console.error("Error fetching suppliers:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }
    getExchanges();
  }, [store_id, token]);

  const handleDeleteSelected = async () => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedExchangeIds = selectedRows.map((row) => row.original.id);

    try {
      for (const customerId of selectedExchangeIds) {
        // await deleteExchange(token, store_id, customerId);
        router.refresh();
      }
      setExchanges((prev) =>
        prev.filter((customer) => !selectedExchangeIds.includes(customer.id)),
      );
      setRowSelection({});
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting customers:", error);
    }
  };

  const columns = useMemo(() => getColumns(items, staff, router), [items, staff, router]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  const showSelectedItems = (
    <ul>
      {table
        .getSelectedRowModel()
        .rows.map((row) => (
          <li key={row.id}>- {row.original.receipt_number}</li>
        ))}
    </ul>
  );

  return (
    <div className="w-full">
      {/* Top Section: Input, New Exchange Button, and Columns Dropdown */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
        <Input
          placeholder="Filter item exchanges..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm"
          disabled={isInitialLoading}
        />
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Button
            onClick={() => router.push("/app/items/exchanges/create")}
            className="w-full sm:w-auto"
            disabled={isInitialLoading}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Exchange
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                Columns <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Desktop Table View */}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-x-auto grid grid-cols-1">
          <Table className="w-full min-w-[600px] caption-bottom text-sm">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <LoadingSpinner />
                  </TableCell>
                </TableRow>
              ) : paginatedRows.length ? (
                paginatedRows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Mobile Card View */}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedRows.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No results.</div>
          )}
          {paginatedRows.map(row => {
            const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
            const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
            const dataCells = row.getVisibleCells().filter(
              cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'name'
            );
            return (
              <div
                key={row.id}
                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
                data-state={row.getIsSelected() ? "selected" : undefined}
              >
                {/* Header: Name, Checkbox, Actions */}
                <div className="flex justify-between items-start mb-2">
                  <label className="flex items-center space-x-3">
                    {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                    <span
                      className="font-bold text-lg cursor-pointer hover:text-primary break-words"
                      onClick={() => router.push(`/app/items/exchanges/${row.original.id}`)}
                    >
                      {row.getValue("name") || row.getValue("receipt_number") || row.id}
                    </span>
                  </label>
                  <div className="absolute top-2 right-2">
                    {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                  </div>
                </div>
                <hr className="border-border" />
                {/* Details */}
                <div className="space-y-3 pt-2">
                  {dataCells.map(cell => (
                    <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(cell.column.id)}
                      </div>
                      <div className="text-right break-words">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Delete Button and Mobile-Responsive Pagination */}
      <div className="space-y-4">
        {/* Delete Selected Button - Mobile First */}
        {Object.keys(rowSelection).length > 0 && (
          <div className="flex justify-center">
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" disabled={isInitialLoading} className="w-full sm:w-auto h-12 sm:h-9 text-base sm:text-sm">
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Selected ({Object.keys(rowSelection).length})
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Exchanges</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete the selected item(s)?
                    {showSelectedItems}
                    <b>This action cannot be undone. </b>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteSelected}>
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}

        {/* Pagination Section */}
        <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 sm:space-x-2 py-4">
          <div className="text-sm text-muted-foreground text-center sm:text-left">
            {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>

          {/* Desktop Pagination */}
          {!isMobile && (
            <TablePagination
              pageIndex={pageIndex}
              pageSize={pageSize}
              pageCount={pageCount}
              totalItems={filteredRows.length}
              onPageChange={setPageIndex}
              onPageSizeChange={setPageSize}
            />
          )}

          {/* Mobile Pagination */}
          {isMobile && (
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => setPageIndex(Math.max(0, pageIndex - 1))}
                disabled={pageIndex === 0}
                className="h-12 px-6 text-base border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              >
                Previous
              </Button>
              <span className="text-sm font-medium">
                Page {pageIndex + 1} of {Math.max(1, pageCount)}
              </span>
              <Button
                onClick={() => setPageIndex(Math.min(pageCount - 1, pageIndex + 1))}
                disabled={pageIndex >= pageCount - 1}
                className="h-12 px-6 text-base border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}