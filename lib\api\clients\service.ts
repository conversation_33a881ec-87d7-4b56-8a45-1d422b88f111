
import { Customer, NewCustomerSchema } from "./models";
import { BASE_URL } from "@/app/configs/constants";


export async function getRetailStoreClients(authToken: string, storeId: string): Promise<Customer[]> {
  try {
    const response = await fetch(`${BASE_URL}/customers/${storeId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch clients");
    }
    const clients = await response.json();
    return clients;
  } catch (error) {
    throw error;
  }
}


export async function createRetailStoreClient(authToken: string, store_id: string, payload: NewCustomerSchema): Promise<Customer> {
  try {
    const response = await fetch(`${BASE_URL}/customers/${store_id}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to create client");
    }
    const client = await response.json();
    return client;
  } catch (error) {
    throw error;
  }
}

export async function getRetailStoreClient(authToken: string, storeId: string, clientId: string): Promise<Customer> {
  try {
    const response = await fetch(`${BASE_URL}/customers/${storeId}/${clientId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch client");
    }
    const client = await response.json();
    return client;
  } catch (error) {
    throw error;
  }
}

export async function updateRetailStoreClient(authToken: string, storeId: string, clientId: string, payload: NewCustomerSchema): Promise<Customer> {
  try {
    const response = await fetch(`${BASE_URL}/customers/${storeId}/${clientId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update client");
    }
    const client = await response.json();
    return client;
  } catch (error) {
    throw error;
  }
}

export async function deleteRetailStoreClient(authToken: string, storeId: string ,clientId: string): Promise<void> {
  try {
    const response = await fetch(`${BASE_URL}/customers/${storeId}/${clientId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (response.status != 204) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete client");
    }
  } catch (error) {
    throw error;
  }
}
