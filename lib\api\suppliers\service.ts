import { BASE_URL } from "@/app/configs/constants";
import { Supplier, SupplierSchema } from "./models";

export async function fetchSuppliers(authToken: string, store_id: string)
  : Promise<Supplier[]> {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${store_id}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch suppliers");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

// TODO: #1 create a handler to fetch all single supplier


export async function createSupplier(authToken: string, store_id: string, supplier: SupplierSchema) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${store_id}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(supplier),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to create supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getSupplier(authToken: string, storeId: string,supplierId: string) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}/${supplierId}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}


export async function updateSupplier(authToken: string, store_id: string, supplierId: string, supplier: SupplierSchema) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${store_id}/${supplierId}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(supplier),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteSupplier(authToken: string, storeId: string, supplierId: string) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}/${supplierId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}
