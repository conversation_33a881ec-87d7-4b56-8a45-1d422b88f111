// app/app/items/matrices/create/page.tsx
"use client";

import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { Category } from "@/lib/api/categories/models";
import { Brand } from "@/lib/api/brands/models";
import { AttributeConfig } from "./AttributesSection";
import {
  fetchAttributes,
  createAttribute,
} from "@/lib/api/attributes/attributeService";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import {
  createAttributesValues,
  fetchStoreAttributeValues,
} from "@/lib/api/attributes/attributeValues";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { createItemMatrix } from "@/lib/api/items/matrices/service";
import { createMatrixAttributeValues } from "@/lib/api/attributes/matrixAttributeValues";
import { ItemMatrixSchema } from "@/lib/api/items/matrices/models";
import { AttributePayload } from "@/lib/api/attributes/models";
import { v4 as uuidv4 } from "uuid";
import { MatrixForm } from "../MatrixForm";


export default function CreateMatrixPage() {
  const router = useRouter();
  const [availableAttributes, setAvailableAttributes] = useState<Attributes[]>([]);
  const [availableAttributeValues, setAvailableAttributeValues] = useState<AttributeValues[]>([]);
  const [vendors, setSupplierData] = useState<Supplier[]>([]);
  const [categories, setCategoryData] = useState<Category[]>([]);
  const [brands, setBrandData] = useState<Brand[]>([]);
  const [attributes, setAttributes] = useState<AttributeConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);


  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");


  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) return;

      setLoading(true);
      try {
        const attributesData = await fetchAttributes(token, store_id);
        setAvailableAttributes(attributesData);

        const attributeValuesData = await fetchStoreAttributeValues(token, store_id);
        setAvailableAttributeValues(attributeValuesData);

        const suppliersData = await fetchSuppliers(token, store_id);
        setSupplierData(suppliersData);

        const categoriesData = await fetchCategories(token, store_id);
        setCategoryData(categoriesData);

        const brandsData = await fetchBrands(token, store_id);
        setBrandData(brandsData);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [token, store_id]);

  const handleAddAttribute = (attributeId: string) => {
    const selectedAttribute = availableAttributes.find(
      (attr) => attr.id === attributeId
    );

    if (selectedAttribute) {
      const attributeValues = availableAttributeValues.filter(
        (val) => val.attribute_id === attributeId
      );

      setAttributes((prev) => {
        const existingAttribute = prev.find(
          (attr) => attr.attribute.id === attributeId
        );

        if (existingAttribute) {
          return prev.map((attr) =>
            attr.attribute.id === attributeId
              ? { ...attr, values: attributeValues }
              : attr
          );
        }

        return [
          ...prev,
          {
            attribute: selectedAttribute,
            values: attributeValues,
            selectedValues: [],
          },
        ];
      });
    }
  };

  const handleToggleAttributeValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) =>
      prev.map((attr) => {
        if (attr.attribute.id === attributeId) {
          const currentSelectedValues = attr.selectedValues;
          const newSelectedValues = currentSelectedValues.includes(valueId)
            ? currentSelectedValues.filter((id) => id !== valueId)
            : [...currentSelectedValues, valueId];

          return {
            ...attr,
            selectedValues: newSelectedValues,
          };
        }
        return attr;
      })
    );
  };

  const handleRemoveAttribute = (attributeId: string) => {
    setAttributes((prev) => {
      if (prev && prev.length > 0) {
        const updatedAttributes = prev.filter(
          (a) => a.attribute.id !== attributeId,
        );

        return updatedAttributes.length > 0 ? updatedAttributes : [];
      } else {
        return prev;
      }
    });
  };

  const handleAddValue = (attributeId: string, value: string) => {
    const newValueId = uuidv4();
    setAvailableAttributeValues((prev) => [
      ...prev,
      {
        id: newValueId,
        attribute_id: attributeId,
        value: value,
        store_id: store_id,
        date_created: new Date().toISOString(),
      },
    ]);
  };

  const handleRemoveValue = (attributeId: string, valueId: string) => {
    setAttributes((prev) => {
      const updatedAttributes = prev
        .map((attr) => {
          if (attr.attribute.id === attributeId) {
            const newValues = attr.values.filter((v) => v.id !== valueId);
            const newSelectedValues = attr.selectedValues.filter((id) => id !== valueId);

            if (newValues.length === 0) return null;

            return newValues.length > 0 ? { ...attr, values: newValues, selectedValues: newSelectedValues } : null;

          }
          return attr;
        })
        .filter((attr): attr is AttributeConfig => attr !== null);

      return updatedAttributes;
    });
  };


  const onAddBulkValues = async (
    attributeId: string,
    payload: AttributeValuesSchema,
  ) => {
    try {
      if (!token || !store_id) return;

      await createAttributesValues(token, store_id, attributeId, payload);
      const attributeValues = await fetchStoreAttributeValues(
        token!,
        store_id!,
      );

      setAvailableAttributeValues((prev) => {
        const newValues = attributeValues.filter(
          (val) =>
            val.attribute_id === attributeId &&
            !prev.some((existingVal) => existingVal.id === val.id),
        );
        return [...prev, ...newValues];
      });

      setAttributes((prev) => (
        prev.map((attr) => {
          if (attr.attribute.id === attributeId) {
            const newAttributeValues = attributeValues.filter(
              (val) =>
                val.attribute_id === attributeId &&
                !attr.values.some((existingVal) => existingVal.id === val.id),
            );

            return {
              ...attr,
              values: [...attr.values, ...newAttributeValues],
            };
          }
          return attr;
        })
      ));
    } catch (error: unknown) {
      console.error(error);
    }
  };

  const handleSubmit = async (data: ItemMatrixSchema, attributes: AttributeConfig[]) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    try {
      setSaving(true);

      const matrixData = {
        ...data,
        store_id: store_id,
      };

      const matrix_data = await createItemMatrix(token, matrixData, store_id);

      if (matrix_data) {
        const matrix_id = matrix_data.id;
        const matrixAttributesValues = attributes.flatMap(
          (attr, position) =>
            attr.selectedValues.map((valueId) => ({
              attribute_value_ids: valueId,
              item_id: matrix_id,
              position,
            })),
        );

        await createMatrixAttributeValues(token, store_id, matrixAttributesValues);
        router.push("/app/items/matrices");
      }
    } catch (err) {
      const error = err as Error;
      console.error("Failed to create matrix:", error);
      throw new Error(error.message || "Failed to create matrix.");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push("/app/items/matrices");
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <MatrixForm
      attributes={attributes}
      availableAttributes={availableAttributes}
      availableAttributeValues={availableAttributeValues}
      vendors={vendors}
      categories={categories}
      brands={brands}
      storeId={store_id!}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      onAddAttribute={handleAddAttribute}
      onRemoveAttribute={handleRemoveAttribute}
      onToggleValue={handleToggleAttributeValue}
      onAddValue={handleAddValue}
      onRemoveValue={handleRemoveValue}
      onAddBulkValues={onAddBulkValues}
      loading={loading}
      saving={saving}
    />
  );
}