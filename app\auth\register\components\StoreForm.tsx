import React, { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { But<PERSON> } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from "@/components/ui/input";
import { searchCountries, getAllCountries } from '@/lib/api/users/onboarding/utils';
import { Check, ChevronsUpDown, Search, Store, Mail, Phone, MapPin, ArrowRight, Building, Globe } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

interface StoreFormProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  loading: boolean;
}

type CountryData = {
  country: string;
  code: string;
  currency: string;
  currencyName: string;
};

const StoreForm: React.FC<StoreFormProps> = ({ onSubmit, onBack, loading }) => {
  const { control, handleSubmit, watch, setValue, formState: { errors } } = useFormContext();
  const [countries, setCountries] = useState<CountryData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCountryOpen, setIsCountryOpen] = useState(false);

  const userEmail = watch("user.email");
  const storeCountry = watch("store.country");
  const storeCurrency = watch("store.currency");

  useEffect(() => {
    const data = getAllCountries();
    setCountries(data);
    if (!watch("store.email")) {
      setValue("store.email", userEmail);
    }
  }, [userEmail, setValue, watch]);

  const filteredCountries = searchTerm.length > 0
    ? countries.filter(country =>
      country.country.toLowerCase().includes(searchTerm.toLowerCase()))
    : countries;

  const handleCountrySelect = (country: CountryData) => {
    setValue("store.country", country.country);
    setValue("store.currency", country.currency);
    setIsCountryOpen(false);
  };

  return (
    <Card>
      <CardContent className="p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-center">Set Up Your Store</h2>
            <p className="text-muted-foreground text-center mb-8">Tell us more about your business</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Store Name */}
            <FormField
              control={control}
              name="store.name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Store Name</FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                      <Store size={18} />
                    </div>
                    <FormControl>
                      <Input placeholder="Your Store Name" {...field} value={field.value ?? ''} className="pl-10" />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={control}
              name="store.email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Store Email</FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                      <Mail size={18} />
                    </div>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} value={field.value ?? ''} className="pl-10" />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category */}
            <FormField
              control={control}
              name="store.category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Business Category</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="electronics">Electronics</SelectItem>
                      <SelectItem value="clothing">Clothing & Apparel</SelectItem>
                      <SelectItem value="food">Food & Beverage</SelectItem>
                      <SelectItem value="books">Books & Media</SelectItem>
                      <SelectItem value="furniture">Home & Furniture</SelectItem>
                      <SelectItem value="beauty">Beauty & Personal Care</SelectItem>
                      <SelectItem value="health">Health & Wellness</SelectItem>
                      <SelectItem value="art">Art & Crafts</SelectItem>
                      <SelectItem value="sports">Sports & Outdoors</SelectItem>
                      <SelectItem value="toys">Toys & Games</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone */}
            <FormField
              control={control}
              name="store.phone_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                      <Phone size={18} />
                    </div>
                    <FormControl>
                      <Input placeholder="+****************" {...field} value={field.value ?? ''} className="pl-10" />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* City */}
            <FormField
              control={control}
              name="store.city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                      <Building size={18} />
                    </div>
                    <FormControl>
                      <Input placeholder="New York" {...field} value={field.value ?? ''} className="pl-10" />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Country */}
            <FormItem>
              <FormLabel>Country</FormLabel>
              <Popover open={isCountryOpen} onOpenChange={setIsCountryOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isCountryOpen}
                    className="w-full justify-between h-10"
                  >
                    <div className="flex items-center">
                      <Globe size={18} className="mr-2 text-muted-foreground" />
                      <span className={storeCountry ? "text-foreground" : "text-muted-foreground"}>
                        {storeCountry || "Select a country"}
                      </span>
                    </div>
                    <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command className="w-full">
                    <div className="flex items-center border-b px-3">
                      <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                      <CommandInput
                        placeholder="Search countries..."
                        className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                        value={searchTerm}
                        onValueChange={setSearchTerm}
                      />
                    </div>
                    <CommandList>
                      <CommandEmpty>No countries found.</CommandEmpty>
                      <CommandGroup className="max-h-64 overflow-auto">
                        {filteredCountries.map((country, idx) => (
                          <CommandItem
                            key={country.code + '-' + country.country + '-' + idx}
                            value={country.country}
                            onSelect={() => handleCountrySelect(country)}
                            className="flex items-center"
                          >
                            <span className="mr-2 text-xs bg-muted rounded px-1 py-0.5">{country.code}</span>
                            {country.country}
                            {storeCountry === country.country && (
                              <Check className="ml-auto h-4 w-4 text-primary" />
                            )}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              {storeCurrency && (
                <p className="text-xs text-muted-foreground mt-1 flex items-center">
                  <span className="font-medium">Currency:</span>
                  <span className="ml-1 bg-accent text-accent-foreground px-2 py-0.5 rounded-full">
                    {storeCurrency}
                  </span>
                </p>
              )}
              <FormMessage />
            </FormItem>

            {/* Address */}
            <FormField
              control={control}
              name="store.address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                      <MapPin size={18} />
                    </div>
                    <FormControl>
                      <Input placeholder="123 Main St, Apt 4B" {...field} value={field.value ?? ''} className="pl-10" />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="ghost"
              onClick={onBack}
              disabled={loading}
            >
              Back
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? (
                <>Loading...</>
              ) : (
                <>
                  Complete Setup
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
          {/* Show validation errors for debugging */}
          {Object.keys(errors).length > 0 && (
            <pre className="text-xs text-red-500 bg-red-50 p-2 rounded mt-2 overflow-x-auto">
              {JSON.stringify(errors, null, 2)}
            </pre>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default StoreForm;