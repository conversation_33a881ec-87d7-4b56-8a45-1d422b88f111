"use client";

// import { useSelector, useDispatch } from "react-redux";
// import { RootState } from "@/lib/store";
// import { decrement, increment } from "@/lib/features/counter/counterSlice";
// import { Button } from "@/components/ui/button";
//
//
// function Counter() {
//
//     const count = useSelector((state: RootState) => state.counter.value);
//     const dispatch = useDispatch();
//     return (
//         <div className="p-6">
//             <p className="text-large">Count: {count}</p>
//             <div className="h-4 space-x-4">
//                 <Button onClick={() => dispatch(increment())}>Increment</Button>
//                 <Button onClick={() => dispatch(decrement())}>Decrement</Button>
//             </div>
//
//         </div>
//     );
// }
//
//
// export default Counter;


import { useState } from "react";
import { AlertCircle, X } from "lucide-react"; // Import X icon for the close button

import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Button } from "@/components/ui/button"; // Import the Button component from shadcn/ui

// function AlertError({ title, message }: { title: string; message: string }) {
function AlertSuccess() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null; // Return null to hide the alert when isVisible is false
  }

  return (
    <Alert variant="warning">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <AlertCircle className="h-4 w-4 mr-2" />
          <div>
            <AlertTitle>Success!</AlertTitle>
            <AlertDescription>Things went well</AlertDescription>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          aria-label="Close error alert"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  );
}

export default AlertSuccess;
