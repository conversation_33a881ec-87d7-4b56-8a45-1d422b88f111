"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

export default function Home() {
  return (
    <div className="mt-4 text-center">
      <Image
        src="/logo-out.png"
        alt="StoreYako Logo"
        width={113}
        height={43}
      />
      <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">StoreYako</h1>

      <p>
        Welcome to StoreYako, the best place to manage your store.
      </p>

      <br>
      </br>

      <div className="flex justify-center mt-8 mb-4">
        <Button
        onClick={() => {window.location.href = '/auth/login'}} 
        > Login </Button>
      </div>

      <h2 className="font-bold">Coming Soon ...</h2>

      <footer className="mt-8">
        <p>
          &copy; {new Date().getFullYear()} StoreYako. All rights reserved.
        </p>
      </footer>

    </div>
  );
}
