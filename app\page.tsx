//page.tsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Mail, Phone, Check, Cloud, CreditCard, Users, Store, Facebook, Twitter, Loader2, CheckCircle, AlertCircle, ArrowRight, Zap, BarChart3, TrendingUp, ShoppingBag, DollarSign, Package, Activity } from "lucide-react"; // Added ArrowRight, Zap, dashboard icons
import Image from "next/image";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { GoogleReCaptchaProvider, useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { BASE_URL, BETA_TESTING_EMAIL, FACEBOOK_URL, TWITTER_URL } from "./configs/constants";

// --- Schema without reCAPTCHA field ---
const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactFormData = z.infer<typeof contactSchema>;

// --- Reusable Contact Form Logic Component ---
function ContactFormLogic() {
  const [contactSuccess, setContactSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const { executeRecaptcha } = useGoogleReCaptcha();

  const contactForm = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: { name: "", email: "", message: "" },
  });

  const onContactSubmit = async (data: ContactFormData) => {
    setContactSuccess(false);
    setSubmitError(null);

    if (!executeRecaptcha) {
      console.error("Recaptcha not ready");
      setSubmitError("reCAPTCHA is not ready. Please try again later.");
      return;
    }

    let captcha_token: string | undefined;
    try {
      captcha_token = await executeRecaptcha('contactForm');
    } catch (error) {
      console.error("Error getting reCAPTCHA token:", error);
      setSubmitError("Failed to get reCAPTCHA token. Please try again.");
      return;
    }

    if (!captcha_token) {
      console.error("reCAPTCHA token not received.");
      setSubmitError("reCAPTCHA verification failed. Please try again.");
      return;
    }

    try {
      const response = await fetch(`${BASE_URL}/contact-us`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data, captcha_token }),
      });

      if (response.ok) {
        const successMessage = await response.text();
        console.log("API success response body:", successMessage);
        setContactSuccess(true);
        setSubmitError(null);
        contactForm.reset();
      } else {
        let errorData;
        const responseContentType = response.headers.get("content-type");
        try {
          if (responseContentType && responseContentType.includes("application/json")) {
            errorData = await response.json();
          } else {
            const errorText = await response.text();
            errorData = { message: errorText || `Request failed with status: ${response.status}` };
          }
        } catch (parseError) {
          errorData = { message: `Request failed with status: ${response.status}. Could not parse error response.` };
        }
        console.error("Failed to submit contact form:", response.status, response.statusText, errorData);
        setSubmitError(errorData?.message || `Failed to submit: ${response.statusText}`);
        setContactSuccess(false);
      }
    } catch (error) {
      console.error("Network or client-side error during submission:", error);
      if (!submitError) {
        setSubmitError("An unexpected network or client-side error occurred. Please try again.");
      }
      setContactSuccess(false);
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (contactSuccess || submitError) {
      timer = setTimeout(() => {
        setContactSuccess(false);
        setSubmitError(null);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [contactSuccess, submitError]);

  return (
    <form onSubmit={contactForm.handleSubmit(onContactSubmit)} className="grid gap-5 py-4">
      <div className="space-y-2">
        <Label htmlFor="name">Your Name</Label>
        <Input
          id="name"
          placeholder="John Doe"
          {...contactForm.register("name")}
          className={`${contactForm.formState.errors.name ? "border-destructive focus-visible:ring-destructive" : ""}`}
          aria-invalid={contactForm.formState.errors.name ? "true" : "false"}
        />
        {contactForm.formState.errors.name && (
          <p className="text-sm text-destructive" role="alert">{contactForm.formState.errors.name.message}</p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          placeholder="<EMAIL>"
          type="email"
          {...contactForm.register("email")}
          className={`${contactForm.formState.errors.email ? "border-destructive focus-visible:ring-destructive" : ""}`}
          aria-invalid={contactForm.formState.errors.email ? "true" : "false"}
        />
        {contactForm.formState.errors.email && (
          <p className="text-sm text-destructive" role="alert">{contactForm.formState.errors.email.message}</p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="message">Message</Label>
        <Textarea
          id="message"
          placeholder="How can we help you?"
          className={`min-h-[120px] resize-none ${contactForm.formState.errors.message ? "border-destructive focus-visible:ring-destructive" : ""}`}
          {...contactForm.register("message")}
          aria-invalid={contactForm.formState.errors.message ? "true" : "false"}
        />
        {contactForm.formState.errors.message && (
          <p className="text-sm text-destructive" role="alert">{contactForm.formState.errors.message.message}</p>
        )}
      </div>
      {contactSuccess && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700" role="status">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Success!</AlertTitle>
          <AlertDescription>Your message has been sent. We&apos;ll be in touch soon.</AlertDescription>
        </Alert>
      )}
      {submitError && (
        <Alert variant="error" className="bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700" role="alert">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Submission Failed</AlertTitle>
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}
      <DialogFooter className="mt-2 sm:mt-4">
        <Button type="submit" className="w-full" disabled={contactForm.formState.isSubmitting} aria-busy={contactForm.formState.isSubmitting}>
          {contactForm.formState.isSubmitting ? (
            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sending...</>
          ) : (
            "Send Message"
          )}
        </Button>
      </DialogFooter>
    </form>
  );
}

// --- Reusable Contact Dialog Content Component ---
const ContactDialogContentComponent = () => (
  <DialogContent className="max-w-md sm:max-w-lg">
    <DialogHeader>
      <DialogTitle className="text-2xl font-bold">Get in Touch</DialogTitle>
      <DialogDescription className="text-muted-foreground">
        Fill out the form below and we&apos;ll get back to you as soon as possible.
      </DialogDescription>
    </DialogHeader>
    <ContactFormLogic />
  </DialogContent>
);

// --- Dashboard Mockup Component ---
const DashboardMockup = () => (
  <div className="relative w-full max-w-4xl mx-auto">
    {/* Main Dashboard Container */}
    <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Dashboard Header */}
      <div className="bg-gradient-to-r from-green-600 to-orange-500 p-4 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <Store className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">Storeyako Dashboard</h3>
              <p className="text-green-100 text-sm">Main Store - Nairobi</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm">Live</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="p-6 bg-gray-50 dark:bg-gray-800/50">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Today's Sales</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">KSh 45,230</p>
              </div>
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600 dark:text-green-400">+12.5%</span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Orders</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">127</p>
              </div>
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <ShoppingBag className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <Activity className="w-4 h-4 text-blue-500 mr-1" />
              <span className="text-sm text-blue-600 dark:text-blue-400">+8.2%</span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Products</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">1,234</p>
              </div>
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">In stock</span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">M-Pesa</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">KSh 32,100</p>
              </div>
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <span className="text-sm text-orange-600 dark:text-orange-400">71% of sales</span>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Area */}
      <div className="p-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-900 dark:text-white">Sales Overview</h4>
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-500">Last 7 days</span>
            </div>
          </div>
          {/* Simple chart representation */}
          <div className="flex items-end space-x-2 h-32">
            {[40, 65, 45, 80, 55, 70, 85].map((height, index) => (
              <div key={index} className="flex-1 bg-gradient-to-t from-green-500 to-orange-500 rounded-t-sm opacity-80" style={{ height: `${height}%` }}></div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-500">
            <span>Mon</span>
            <span>Tue</span>
            <span>Wed</span>
            <span>Thu</span>
            <span>Fri</span>
            <span>Sat</span>
            <span>Sun</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="px-6 pb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Recent Transactions</h4>
          <div className="space-y-3">
            {[
              { item: "Coca Cola 500ml", amount: "KSh 80", time: "2 min ago", status: "completed" },
              { item: "Bread - White", amount: "KSh 55", time: "5 min ago", status: "completed" },
              { item: "Milk 1L", amount: "KSh 120", time: "8 min ago", status: "pending" },
            ].map((transaction, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${transaction.status === 'completed' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{transaction.item}</p>
                    <p className="text-xs text-gray-500">{transaction.time}</p>
                  </div>
                </div>
                <span className="text-sm font-semibold text-gray-900 dark:text-white">{transaction.amount}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>

    {/* Floating elements for visual appeal */}
    <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-green-400 to-orange-500 rounded-full opacity-20 blur-xl"></div>
    <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-orange-400 to-green-500 rounded-full opacity-20 blur-xl"></div>
  </div>
);


// --- Main Landing Page Component ---
export default function LandingPage() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features = [
    { icon: Cloud, title: "Cloud-Based", description: "Access your business data from anywhere, anytime with real-time syncing across all devices." },
    { icon: CreditCard, title: "M-Pesa Integration", description: "Native M-Pesa integration with instant payment processing and automatic reconciliation." },
    { icon: BarChart3, title: "Real-time Analytics", description: "Get instant insights into your sales, inventory, and customer behavior with live dashboards." },
    { icon: Package, title: "Smart Inventory", description: "Automated stock tracking, low-stock alerts, and intelligent reorder suggestions." },
    { icon: Users, title: "Multi-User Support", description: "Role-based access control for your team with detailed activity tracking." },
    { icon: Store, title: "Multi-Store Ready", description: "Manage multiple locations from a single dashboard with centralized reporting." },
  ];

  const socialLinks = [
    { icon: Facebook, url: FACEBOOK_URL, name: "Facebook" },
    { icon: Twitter, url: TWITTER_URL, name: "Twitter" },
    // { icon: LinkedIn, url: LINKEDIN_URL, name: "LinkedIn" },
    // { icon: YouTube, url: YOUTUBE_URL, name: "YouTube" },
  ];

  const benefitsList = [
    "Free access during beta period (6+ months)",
    "Priority support and direct line to developers",
    "Exclusive discounts when we launch",
    "Influence product development with your feedback"
  ];

  const requirementsList = [
    { icon: Store, text: "Retail businesses with 1-5 locations in Kenya" },
    { icon: Users, text: "Businesses with 2+ employees who will use the system" },
    { icon: CreditCard, text: "Businesses currently using or planning to use M-Pesa for payments" },
    { icon: Mail, text: "Willingness to provide regular feedback and report issues" }
  ];

  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  if (!siteKey) {
    console.error("CRITICAL: Missing NEXT_PUBLIC_RECAPTCHA_SITE_KEY environment variable. reCAPTCHA will not function.");
  }

  return (
    <GoogleReCaptchaProvider reCaptchaKey={siteKey || ""}>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-slate-100 dark:from-gray-900 dark:to-slate-950 text-foreground dark:text-primary-foreground overflow-x-hidden">
        <header
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out
                      ${isScrolled ? 'bg-background/95 dark:bg-gray-900/95 backdrop-blur-lg shadow-lg border-b border-border/50' : 'bg-transparent'}`}
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-16 flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <div className="relative w-28 h-8 sm:w-32 sm:h-10">
                <Image src="/logo-out.png" alt="Storeyako Logo" fill className="object-contain" priority />
              </div>
            </Link>
            <div className="flex gap-2 sm:gap-3 items-center">
              <Button variant="ghost" asChild className="text-sm sm:text-base">
                <Link href="/auth/login">Login</Link>
              </Button>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" className="gap-1 sm:gap-2 font-medium text-sm sm:text-base">
                    <Mail className="h-4 w-4" />
                    <span className="hidden sm:inline">Contact Us</span>
                  </Button>
                </DialogTrigger>
                <ContactDialogContentComponent />
              </Dialog>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground text-sm sm:text-base" asChild>
                <Link href="/auth/register" className="flex items-center gap-1 sm:gap-2">
                  Get Started
                  <ArrowRight className="h-4 w-4 hidden sm:inline" />
                </Link>
              </Button>
            </div>
          </div>
        </header>

        <main>
          <section className="relative pt-32 pb-20 md:pt-48 md:pb-32 overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 -z-10 overflow-hidden bg-gradient-to-br from-slate-100 via-white to-slate-200 dark:from-slate-900 dark:via-gray-950 dark:to-slate-800">
              <svg className="absolute left-[max(50%,25rem)] top-0 h-[64rem] w-[128rem] -translate-x-1/2 stroke-gray-200/60 dark:stroke-gray-700/50 [mask-image:radial-gradient(64rem_64rem_at_top,white,transparent)]" aria-hidden="true">
                <defs>
                  <pattern id="e813992c-7d03-4cc4-a2bd-151760b470a0" width="200" height="200" x="50%" y="-1" patternUnits="userSpaceOnUse">
                    <path d="M100 200V.5M.5 .5H200" fill="none" />
                  </pattern>
                </defs>
                <svg x="50%" y="-1" className="overflow-visible fill-gray-50/50 dark:fill-slate-800/30">
                  <path d="M-100.5 0h201v201h-201Z M699.5 0h201v201h-201Z M499.5 400h201v201h-201Z M299.5 800h201v201h-201Z" strokeWidth="0" />
                </svg>
                <rect width="100%" height="100%" strokeWidth="0" fill="url(#e813992c-7d03-4cc4-a2bd-151760b470a0)" />
              </svg>
            </div>

            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="text-center lg:text-left">
                  <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-700 border border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700 mb-6">
                    <Zap className="w-4 h-4 mr-2" />
                    Now in Beta - Free Access Available
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                    Modern Cloud POS for <span className="bg-gradient-to-r from-green-600 to-orange-500 bg-clip-text text-transparent">Kenyan Businesses</span>
                  </h1>
                  <p className="text-lg sm:text-xl text-muted-foreground dark:text-slate-300 mb-10 max-w-xl mx-auto lg:mx-0 leading-relaxed">
                    Transform your retail operations with our comprehensive point-of-sale system featuring seamless M-Pesa integration, real-time analytics, and cloud-based inventory management built for Kenya.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                    <Button size="lg" className="py-3 px-6 sm:py-4 sm:px-8 text-base sm:text-lg bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg transition-shadow" asChild>
                      <Link href="/auth/register" className="flex items-center gap-2">
                        Get Started Free <Zap className="h-5 w-5" />
                      </Link>
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="lg" className="py-3 px-6 sm:py-4 sm:px-8 text-base sm:text-lg gap-2 border-2 hover:border-primary">
                          <Mail className="h-5 w-5" />
                          Contact Sales
                        </Button>
                      </DialogTrigger>
                      <ContactDialogContentComponent />
                    </Dialog>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-center lg:text-left">
                      <div className="text-2xl sm:text-3xl font-bold text-green-600">500+</div>
                      <div className="text-sm text-muted-foreground">Businesses Served</div>
                    </div>
                    <div className="text-center lg:text-left">
                      <div className="text-2xl sm:text-3xl font-bold text-green-600">99.9%</div>
                      <div className="text-sm text-muted-foreground">Uptime</div>
                    </div>
                    <div className="text-center lg:text-left">
                      <div className="text-2xl sm:text-3xl font-bold text-green-600">24/7</div>
                      <div className="text-sm text-muted-foreground">Support</div>
                    </div>
                  </div>
                </div>
                
                {/* STUNNING DASHBOARD MOCKUP */}
                <div className="hidden lg:flex justify-center items-center">
                  <DashboardMockup />
                </div>
              </div>
            </div>
          </section>

          <section id="features" className="py-20 md:py-28 bg-slate-50 dark:bg-gray-800/30">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="max-w-4xl mx-auto text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-4">Key Features to Power Your Business</h2>
                <p className="text-lg text-muted-foreground dark:text-slate-300">
                  Everything you need to manage and grow your retail operations efficiently.
                </p>
              </div>
              <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => (
                  <Card key={index} className="h-full transform transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-[1.02] bg-card text-card-foreground dark:border-gray-700">
                    <CardHeader className="space-y-3">
                      <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30 mb-2">
                        {feature.icon && <feature.icon className="h-6 w-6 text-primary" aria-hidden="true" />}
                      </div>
                      <CardTitle className="text-xl font-semibold">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm text-muted-foreground dark:text-slate-400 leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* Social Proof Section */}
          <section className="py-16 md:py-20 bg-white dark:bg-slate-900">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-2xl sm:text-3xl font-bold tracking-tight mb-4">Trusted by Growing Businesses</h2>
                <p className="text-lg text-muted-foreground dark:text-slate-300 max-w-2xl mx-auto">
                  Join hundreds of Kenyan businesses already using Storeyako to streamline their operations.
                </p>
              </div>
              <div className="grid md:grid-cols-3 gap-8">
                <Card className="bg-card text-card-foreground dark:bg-slate-800/50 dark:border-gray-700 transform transition-all duration-300 ease-in-out hover:shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className="text-lg">★</span>
                        ))}
                      </div>
                    </div>
                    <p className="text-muted-foreground dark:text-slate-300 mb-4 italic">
                      "Storeyako has completely transformed how we manage our inventory. The M-Pesa integration is seamless and saves us hours every day."
                    </p>
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        JK
                      </div>
                      <div className="ml-3">
                        <p className="font-semibold text-sm">Jane Kamau</p>
                        <p className="text-xs text-muted-foreground">Kamau Electronics, Nairobi</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-card text-card-foreground dark:bg-slate-800/50 dark:border-gray-700 transform transition-all duration-300 ease-in-out hover:shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className="text-lg">★</span>
                        ))}
                      </div>
                    </div>
                    <p className="text-muted-foreground dark:text-slate-300 mb-4 italic">
                      "The real-time analytics help us make better business decisions. We can see exactly what's selling and when to restock."
                    </p>
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                        PM
                      </div>
                      <div className="ml-3">
                        <p className="font-semibold text-sm">Peter Mwangi</p>
                        <p className="text-xs text-muted-foreground">Mwangi Supermarket, Mombasa</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-card text-card-foreground dark:bg-slate-800/50 dark:border-gray-700 transform transition-all duration-300 ease-in-out hover:shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className="text-lg">★</span>
                        ))}
                      </div>
                    </div>
                    <p className="text-muted-foreground dark:text-slate-300 mb-4 italic">
                      "Being part of the beta program has been amazing. The team listens to our feedback and constantly improves the platform."
                    </p>
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-orange-400 rounded-full flex items-center justify-center text-white font-semibold">
                        AO
                      </div>
                      <div className="ml-3">
                        <p className="font-semibold text-sm">Alice Ochieng</p>
                        <p className="text-xs text-muted-foreground">Ochieng Fashion, Kisumu</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          <section id="beta-program" className="py-20 md:py-28 bg-slate-50 dark:bg-gray-800/30">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="max-w-4xl mx-auto text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-4">Join Our Exclusive Beta Program</h2>
                <p className="text-lg text-muted-foreground dark:text-slate-300">
                  Help shape Storeyako and enjoy exclusive benefits by becoming an early adopter.
                </p>
              </div>
              <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
                <Card className="bg-card text-card-foreground dark:bg-slate-800/50 dark:border-gray-700 transform transition-all duration-300 ease-in-out hover:shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-xl font-semibold text-primary">
                      What You Get As a Beta Tester
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-4">
                      {benefitsList.map((benefit, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <Check className="h-5 w-5 text-green-500 dark:text-green-400 shrink-0 mt-0.5" aria-hidden="true" />
                          <span className="text-sm text-muted-foreground dark:text-slate-300">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
                <Card className="bg-card text-card-foreground dark:bg-slate-800/50 dark:border-gray-700 transform transition-all duration-300 ease-in-out hover:shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-xl font-semibold">
                      Who We&apos;re Looking For
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-4">
                      {requirementsList.map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          {item.icon && <item.icon className="h-5 w-5 text-primary shrink-0 mt-0.5" aria-hidden="true" />}
                          <span className="text-sm text-muted-foreground dark:text-slate-300">{item.text}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          <section className="bg-gradient-to-r from-green-600 via-orange-500 to-green-700 text-white py-20 md:py-28 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
            </div>

            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
              <div className="max-w-4xl mx-auto">
                <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Business?</h2>
                <p className="text-lg sm:text-xl mb-10 max-w-2xl mx-auto opacity-90 leading-relaxed">
                  Join hundreds of Kenyan businesses already using Storeyako. Start your free beta access today and experience the future of retail management.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Button size="lg" className="py-3 px-6 sm:py-4 sm:px-8 text-base sm:text-lg gap-2 bg-white text-green-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300" asChild>
                    <Link href="/auth/register" className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Start Free Beta
                    </Link>
                  </Button>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="lg" className="py-3 px-6 sm:py-4 sm:px-8 text-base sm:text-lg gap-2 border-2 border-white text-white hover:bg-white hover:text-green-600 transition-all duration-300">
                        <Mail className="h-5 w-5" />
                        Talk to Sales
                      </Button>
                    </DialogTrigger>
                    <ContactDialogContentComponent />
                  </Dialog>
                </div>
                <p className="text-sm mt-6 opacity-75">
                  ✓ No credit card required  ✓ 6+ months free access  ✓ Setup in minutes
                </p>
              </div>
            </div>
          </section>
        </main>

        <footer className="border-t border-border dark:border-gray-700/50 mt-24 md:mt-32 bg-slate-50 dark:bg-slate-950">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
            <div className="flex flex-col md:flex-row justify-between items-center gap-8">
              <Link href="/" className="flex items-center">
                <div className="relative w-28 h-8 sm:w-32 sm:h-10">
                  <Image src="/logo-out.png" alt="Storeyako Logo" fill className="object-contain" />
                </div>
              </Link>
              <nav className="flex flex-wrap justify-center gap-x-6 gap-y-2 md:gap-8">
                <Link href="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors dark:hover:text-primary-light">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors dark:hover:text-primary-light">
                  Terms of Service
                </Link>
                <a href={`mailto:${BETA_TESTING_EMAIL}`} className="text-sm text-muted-foreground hover:text-primary transition-colors dark:hover:text-primary-light">
                  Support
                </a>
              </nav>
              {socialLinks && socialLinks.length > 0 && (
                <div className="flex gap-3">
                  {socialLinks.map((social) => (
                    <Button key={social.name} variant="ghost" size="icon" asChild>
                      <a href={social.url} target="_blank" rel="noopener noreferrer" aria-label={`Visit our ${social.name}`}>
                        {social.icon && <social.icon className="h-5 w-5 text-muted-foreground hover:text-primary dark:hover:text-primary-light" aria-hidden="true" />}
                      </a>
                    </Button>
                  ))}
                </div>
              )}
            </div>
            <div className="mt-10 pt-10 border-t border-border dark:border-gray-700/50 text-center text-sm text-muted-foreground dark:text-slate-400">
              <p>© {new Date().getFullYear()} Storeyako. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </GoogleReCaptchaProvider>
  );
}