"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Plus, Trash2, Loader2 } from "lucide-react";
import CategoryModal from "./CategoryModal";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column header for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

import { getColumns } from "./columns";
import Cookies from "js-cookie";
import { fetchCategories } from "@/lib/api/categories/service";
import { Category, CategorySchema } from "@/lib/api/categories/models";
import { useRouter } from "next/navigation";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function Page() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const router = useRouter();
  const [data, setCategories] = useState<Category[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    async function getSuppliers() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const fetchedCategories = await fetchCategories(token, store_id);
          setCategories(fetchedCategories);
        } catch (error) {
          console.error("Error fetching suppliers:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }
    getSuppliers();
  }, [store_id, token]);

  const handleCreateCategory = async (categoryData: CategorySchema) => {
    if (token && store_id) {
      try {
        console.log("Creating category with data:", categoryData);
        // Add your API call here to create the category
        // const newCategory = await createCategory(token, store_id, categoryData);

        // Refresh the categories list
        const fetchedCategories = await fetchCategories(token, store_id);
        setCategories(fetchedCategories);
      } catch (error) {
        console.error("Error creating category:", error);
        throw error; // This will be caught by the modal's error handling
      }
    }
  };

  const handleDeleteSelected = async () => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedCategoryIds = selectedRows.map((row) => row.original.id);

    try {
      for (const customerId of selectedCategoryIds) {
        // await deleteCategory(token, store_id, customerId);
        router.refresh();
      }
      setCategories((prev) =>
        prev.filter((customer) => !selectedCategoryIds.includes(customer.id)),
      );
      setRowSelection({});
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting customers:", error);
    }
  };

  const columns = React.useMemo(() =>  getColumns(router), [router]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  const showSelectedItems = (
    <ul>
      {table
        .getSelectedRowModel()
        .rows.map((row) => (
          <li key={row.id}>- {row.original.name}</li>
        ))}
    </ul>
  );

  return (
    <div className="w-full">
      {/* Top Section: Input, New Category Button, and Columns Dropdown */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
        <Input
          placeholder="Filter categories..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm"
          disabled={isInitialLoading}
        />

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0">
          <Button
            onClick={() => setIsModalOpen(true)}
            className="w-full"
            disabled={isInitialLoading}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Category
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                Columns <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Responsive Table/Card Layout */}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-auto grid grid-cols-1">
          <Table className="w-full caption-bottom text-sm">
            <TableHeader>
              {table
                .getHeaderGroups()
                .map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    <LoadingSpinner />
                  </TableCell>
                </TableRow>
              ) : paginatedRows.length ? (
                paginatedRows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedRows.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No results.</div>
          )}
          {paginatedRows.map(row => {
            const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
            const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
            const dataCells = row.getVisibleCells().filter(
              cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'name'
            );
            return (
              <div
                key={row.id}
                className={`bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative ${row.getIsSelected() ? "ring-2 ring-primary" : ""}`}
                data-state={row.getIsSelected() ? "selected" : undefined}
              >
                <div className="flex justify-between items-start mb-2">
                  <label className="flex items-center space-x-3">
                    {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                    <span className="font-bold text-lg cursor-pointer hover:text-primary break-words">
                      {row.getValue("name")}
                    </span>
                  </label>
                  <div className="absolute top-2 right-2">
                    {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                  </div>
                </div>
                <hr className="border-border" />
                <div className="space-y-3 pt-2">
                  {dataCells.map(cell => (
                    <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(cell.column.id)}
                      </div>
                      <div className="text-right break-words">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Delete Button and Pagination at the Bottom */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          {Object.keys(rowSelection).length > 0 && (
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={isInitialLoading}>
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Categories</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete the selected categories(s)?
                    {showSelectedItems}
                    <b>This action cannot be undone. </b>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteSelected}>
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>
      <CategoryModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onSubmit={handleCreateCategory}
      />
    </div>
  );
}