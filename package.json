{"name": "storeyako-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev-https": "next dev --experimental-https", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@google-recaptcha/react": "^2.3.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-table": "^8.21.3", "@types/react-google-recaptcha": "^2.1.9", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "country-data-list": "^1.4.1", "country-list": "^2.3.0", "currency-codes": "^2.2.0", "currency-list": "^1.0.8", "date-fns": "^4.1.0", "firebase": "^11.10.0", "framer-motion": "^12.23.0", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "motion": "^12.23.0", "next": "14.2.16", "next-themes": "^0.4.6", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-pdf-tailwind": "^2.3.0", "react-qr-barcode-scanner": "^2.1.8", "react-redux": "^9.2.0", "react-thermal-printer": "^0.19.3", "react-to-print": "^3.1.1", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.72"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.5", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.4", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^8.57.1", "eslint-config-next": "14.2.16", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}