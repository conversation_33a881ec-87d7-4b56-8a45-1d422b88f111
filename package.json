{"name": "storeyako-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@faker-js/faker": "^9.5.1", "@google-recaptcha/react": "^2.1.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-pdf/renderer": "^4.1.6", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-table": "^8.20.6", "@types/react-google-recaptcha": "^2.1.9", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "country-data-list": "^1.4.0", "country-list": "^2.3.0", "currency-codes": "^2.2.0", "currency-list": "^1.0.8", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "motion": "^12.5.0", "next": "14.2.16", "next-themes": "^0.4.4", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-pdf-tailwind": "^2.3.0", "react-redux": "^9.2.0", "react-thermal-printer": "^0.19.3", "react-to-print": "^3.0.5", "sonner": "^2.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "zod": "^3.24.1"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.12", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^8.57.1", "eslint-config-next": "14.2.16", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}}