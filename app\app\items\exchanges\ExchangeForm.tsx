"use client";

import { useState, ChangeEvent, FormEvent } from "react";
import {
    <PERSON>,
    Card<PERSON>eader,
    CardTitle,
    CardDescription,
    CardContent,
    CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { Item } from "@/lib/api/items/models";
import { Receipt, Item as ReceiptItem } from "@/lib/api/receipts/models";
import { Sale } from "@/lib/api/sales/models";

interface ExchangeSchema {
    original_sale_id: string;
    original_item_id: string;
    quantity_exchanged: number;
    exchange_reason?: string;
    exchanged_with_item_id?: string;
    exchange_value?: number;
    receipt_number: string;
    comments?: string;
}

interface ExchangeFormProps {
    initialData?: ExchangeSchema;
    onSubmit(data: ExchangeSchema): Promise<void>;
    isEditing?: boolean;
    items: Item[];
    receipts: Receipt[];
    sales: Sale[];
}

export function ExchangeForm({
    initialData,
    onSubmit,
    isEditing = false,
    items,
    receipts,
    sales,
}: ExchangeFormProps) {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState<ExchangeSchema>(
        initialData || {
            original_sale_id: "",
            original_item_id: "",
            quantity_exchanged: 1,
            exchange_reason: "",
            exchanged_with_item_id: "",
            exchange_value: 0,
            receipt_number: "",
            comments: "",
        },
    );
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<boolean>(false);

    const handleInputChange = (
        e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    ) => {
        const { name, value } = e.target;
        const parsedValue = name === "quantity_exchanged" ? parseFloat(value) || 0 : value;

        setFormData((prev) => {
            const updatedFormData = {
                ...prev,
                [name]: parsedValue,
            };

            // Recalculate exchange value when quantity changes
            if (name === "quantity_exchanged") {
                return updateExchangeValueWithQuantity(
                    updatedFormData,
                    items,
                    formData.original_item_id,
                    formData.exchanged_with_item_id,
                    typeof parsedValue === "number" ? parsedValue : Number(parsedValue) || 0
                );
            }

            return updatedFormData;
        });
    };

    const handleSelectChange = (value: string, name: string) => {
        setFormData((prev) => {
            const updatedFormData = {
                ...prev,
                [name]: value,
            };

            if (name === "receipt_number") {
                return {
                    ...updatedFormData,
                    original_item_id: "",
                    original_sale_id: sales.find(sale => sale.receipt_id === value)?.id || "",
                    quantity_exchanged: 1, // Reset quantity on receipt change
                    exchange_value: 0, // Reset exchange value on receipt change
                };
            }

            return updatedFormData;
        });

        if (name === "exchanged_with_item_id" && formData.original_item_id) {
            updateExchangeValue(formData.original_item_id, value);
        } else if (name === "original_item_id" && formData.exchanged_with_item_id) {
            updateExchangeValue(value, formData.exchanged_with_item_id);
        }
    };

    const updateExchangeValue = (originalId: string, newId: string) => {
        const originalItem = items.find((item) => item.id === originalId);
        const newItem = items.find((item) => item.id === newId);

        if (originalItem && newItem) {
            const difference = newItem.default_price - originalItem.default_price;
            setFormData((prev) => ({
                ...prev,
                exchange_value: Number(difference.toFixed(2)),
            }));
        }
    };

    const updateExchangeValueWithQuantity = (
        formData: ExchangeSchema,
        items: Item[],
        originalId: string,
        newId: string | undefined,
        quantity: number
    ): ExchangeSchema => {
        const originalItem = items.find((item) => item.id === originalId);
        const newItem = newId ? items.find((item) => item.id === newId) : null;

        let difference = 0;

        if (originalItem) {
            if (newItem) {
                difference = (newItem.default_price - originalItem.default_price) * quantity;
            } else {
                difference = -originalItem.default_price * quantity; // Adjust based on logic - refunding?
            }

            return {
                ...formData,
                exchange_value: Number(difference.toFixed(2)),
            };
        }

        return formData;
    };


    const handleFormSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        setSuccess(false);

        try {
            await onSubmit(formData);
            setSuccess(true);

            if (!isEditing) {
                setFormData({
                    original_sale_id: "",
                    original_item_id: "",
                    quantity_exchanged: 1,
                    exchange_reason: "",
                    exchanged_with_item_id: "",
                    exchange_value: 0,
                    receipt_number: "",
                    comments: "",
                });
            }
        } catch (error) {
            setError((error as Error).message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-3xl mx-auto">
            <CardHeader>
                {error && <AlertError message={error} />}
                {success && <AlertSuccess message={`Exchange ${isEditing ? 'updated' : 'processed'} successfully!`} />}
                <CardTitle>{isEditing ? 'Edit Exchange' : 'Product Exchange'}</CardTitle>
                <CardDescription>
                    {isEditing ? 'Edit the product exchange details' : 'Process a product exchange for returned items'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleFormSubmit} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="receipt_number">Receipt Number *</Label>
                            <Select
                                value={formData.receipt_number}
                                onValueChange={(value) =>
                                    handleSelectChange(value, "receipt_number")
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select original receipt" />
                                </SelectTrigger>
                                <SelectContent>
                                    {receipts.map((receipt) => (
                                        <SelectItem key={receipt.id} value={receipt.id}>
                                            {receipt.receipt_number} - {receipt.salesperson.split(' ')[0]} {new Date(receipt.created_at).toLocaleString('en-US')}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>


                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="original_sale_id">Original Sale ID *</Label>
                            <Input
                                required
                                id="original_sale_id"
                                name="original_sale_id"
                                value={sales.find((sale) => sale.receipt_id === formData.receipt_number)?.id}
                                disabled
                                onChange={handleInputChange}
                                placeholder="Enter original sale ID"
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="original_item_id">Original Item *</Label>
                            <Select
                                value={formData.original_item_id}
                                onValueChange={(value) => handleSelectChange(value, "original_item_id")}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select original item" />
                                </SelectTrigger>
                                <SelectContent>


                                    {receipts
                                        .find((receipt) => receipt.id === formData.receipt_number)
                                        ?.items.sort((a, b) => a.name.localeCompare(b.name))
                                        .map((receiptItem: ReceiptItem) => {
                                            const product = items.find((product) => product.name === receiptItem.name);

                                            if (!product) return null;

                                            return (
                                                <SelectItem key={product.id} value={product.id}>
                                                    {receiptItem.name} - {receiptItem.amount}
                                                </SelectItem>
                                            );
                                        })}


                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="exchanged_with_item_id">Exchange With Item</Label>
                            <Select
                                value={formData.exchanged_with_item_id}
                                onValueChange={(value) =>
                                    handleSelectChange(value, "exchanged_with_item_id")
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select new item" />
                                </SelectTrigger>
                                <SelectContent>
                                    {items.map((item) => (
                                        <SelectItem key={item.id} value={item.id}>
                                            {item.name} - ${item.default_price}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="quantity_exchanged">Quantity *</Label>
                            <Input
                                required
                                type="number"
                                min="1"
                                id="quantity_exchanged"
                                name="quantity_exchanged"
                                value={formData.quantity_exchanged}
                                onChange={handleInputChange}
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="exchange_value">Price Difference</Label>
                            <Input
                                type="number"
                                step="0.01"
                                id="exchange_value"
                                name="exchange_value"
                                value={formData.exchange_value}
                                onChange={handleInputChange}
                                placeholder="0.00"
                                disabled
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="exchange_reason">Exchange Reason</Label>
                        <Textarea
                            id="exchange_reason"
                            name="exchange_reason"
                            value={formData.exchange_reason}
                            onChange={handleInputChange}
                            placeholder="Enter reason for exchange"
                            className="min-h-[80px]"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="comments">Additional Comments</Label>
                        <Textarea
                            id="comments"
                            name="comments"
                            value={formData.comments}
                            onChange={handleInputChange}
                            placeholder="Add any additional comments"
                            className="min-h-[80px]"
                        />
                    </div>
                </form>
            </CardContent>
            <CardFooter className="flex justify-end space-x-4">
                <Button
                    variant="outline"
                    onClick={() =>
                        setFormData({
                            original_sale_id: "",
                            original_item_id: "",
                            quantity_exchanged: 1,
                            exchange_reason: "",
                            exchanged_with_item_id: "",
                            exchange_value: 0,
                            receipt_number: "",
                            comments: "",
                        })
                    }
                >
                    Clear
                </Button>
                <Button onClick={handleFormSubmit} disabled={loading}>
                    {loading ? "Processing..." : isEditing ? "Update Exchange" : "Process Exchange"}
                </Button>
            </CardFooter>
        </Card>
    );
}
