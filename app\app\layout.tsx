// This is from shadcn sidebar 7

"use client";
import { Skeleton } from "@/components/ui/skeleton";

import { AppSidebar } from "@/components/app-sidebar";

import * as React from "react";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import Cookies from "js-cookie";
import { StoreSwitcher } from "@/components/store-switcher";
import { getStores } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";

//const stores = ["Store UGX - Uganda", "Store 123 Kampala", "Store 456 Entebbe"];

function getActiveStoreImageUrl(stores: Store[], activeStoreId: string): string | undefined {
  const activeStore = stores.find((store) => store.id === activeStoreId);
  return activeStore ? activeStore.image_url : undefined;
}

export default function AppLayOut({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [stores, setStores] = React.useState<Store[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  const store_id = Cookies.get("active_store");

  React.useEffect(() => {
    const fetchStores = async () => {
      const token = Cookies.get("auth_token");
      if (token) {
        try {
          const storeData: Store[] = await getStores(token);
          setStores(storeData);
        } catch (error) {
          console.error("Failed to fetch stores:", error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    fetchStores();
  }, []);

  if (isLoading) {
    return (
      <SidebarProvider>
        <div className="flex h-screen w-screen items-center justify-center bg-gray-100">
          <div className="space-y-6 w-5/6 max-w-lg">
            <Skeleton className="h-8 w-2/3" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-4/5" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-6 w-full" />
          </div>
        </div>
      </SidebarProvider>
    );
  }

  const image_url = getActiveStoreImageUrl(stores, store_id!);

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex sticky top-0 bg-background h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="flex-1" />
          <div className="w-60">
            <StoreSwitcher versions={stores.map(store => store.name)} defaultVersion={stores[0].name} />
          </div>
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={image_url}
              alt="Store avatar"
            />
            <AvatarFallback>CK</AvatarFallback>
          </Avatar>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
