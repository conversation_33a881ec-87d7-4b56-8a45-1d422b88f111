// This is from shadcn sidebar 7

"use client";
import { Skeleton } from "@/components/ui/skeleton";

import { AppSidebar } from "@/components/app-sidebar";

import * as React from "react";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import Cookies from "js-cookie";
import { StoreSwitcher } from "@/components/store-switcher";
import { getStores } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import { useRouter } from "next/navigation";

//const stores = ["Store UGX - Uganda", "Store 123 Kampala", "Store 456 Entebbe"];

function getActiveStoreImageUrl(stores: Store[], activeStoreId: string): string | undefined {
  const activeStore = stores.find((store) => store.id === activeStoreId);
  return activeStore ? activeStore.image_url : undefined;
}

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [stores, setStores] = React.useState<Store[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const store_id = Cookies.get("active_store");

  React.useEffect(() => {
    const authToken = Cookies.get("auth_token");
    //if (!authToken || !store_id) {
    if (!authToken) {

      window.location.href = '/auth/login';
      return;
    }
    setIsAuthenticated(true);

    const fetchStores = async () => {
      try {
        const storeData: Store[] = await getStores(authToken);
        setStores(storeData);
      } catch (error) {
        console.error("Failed to fetch stores:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStores();
  }, []);

  if (!isAuthenticated) {
    return null; // Return null while redirecting
  }

  if (isLoading) {
    return (
      <SidebarProvider>
        <div className="flex h-screen w-screen items-center justify-center bg-gray-100">
          <div className="space-y-6 w-5/6 max-w-lg">
            <Skeleton className="h-8 w-2/3" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-4/5" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-6 w-full" />
          </div>
        </div>
      </SidebarProvider>
    );
  }

  const image_url = getActiveStoreImageUrl(stores, store_id!);


  return (

    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex sticky top-0 z-10 bg-white h-14 sm:h-16 shrink-0 items-center gap-2 sm:gap-4 border-b px-3 sm:px-4">
          <SidebarTrigger className="-ml-1 h-9 w-9 sm:h-10 sm:w-10" />
          <div className="flex-1 min-w-0" />
          <div className="hidden sm:block sm:w-60">
            <StoreSwitcher versions={stores.map(store => store.name)} defaultVersion={stores[0].name} />
          </div>
          <div className="sm:hidden max-w-[140px]">
            <StoreSwitcher
              versions={stores.map(store => store.name)}
              defaultVersion={stores[0].name}
            />
          </div>
          <Avatar className="h-8 w-8 sm:h-9 sm:w-9 ml-2 flex-shrink-0">
            <AvatarImage
              src={image_url}
              alt="Store avatar"
            />
            <AvatarFallback className="text-xs sm:text-sm">CK</AvatarFallback>
          </Avatar>
        </header>
        {/* Mobile-optimized content area */}
        <div className="flex flex-col h-[calc(100vh-3.5rem)] sm:h-[calc(100vh-4rem)]"> {/* Full height minus header */}
          <div className="flex-1 overflow-auto p-3 sm:p-4 md:p-6"> {/* Mobile-first padding with responsive scaling */}
            <div className="max-w-full"> {/* Prevent horizontal overflow */}
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
