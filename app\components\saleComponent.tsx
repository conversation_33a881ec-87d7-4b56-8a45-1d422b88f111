// Example usage in a React component (adapt for your framework)
import React, { useState, useEffect, useCallback } from 'react';
// --- Import the updated service function and its result type ---
import { sellItems, SellItemsResult } from '@/lib/api/sales/service'; // Adjust path
import { SellItemsSchema } from '@/lib/api/sales/models'; // Keep this for the payload type
// import { useAuth } from '@/hooks/useAuth'; // Your auth hook
// --- Import your WebSocket hook and types ---
import { useWebSocket, PaymentStatusMessage } from '@/lib/hooks/useWebSocket'; // Adjust path
import Cookies from 'js-cookie';

const SaleComponent: React.FC = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [saleStatusMessage, setSaleStatusMessage] = useState<string | null>(null);
    const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);
    const authToken = Cookies.get('auth_token');

    // --- Use the WebSocket Hook ---
    const { connectionStatus, lastMessage, sendMessage } = useWebSocket(!!authToken);

    // --- Effect to process WebSocket messages (remains the same) ---
    useEffect(() => {
        if (lastMessage && lastMessage.receiptId === pendingReceiptId) {
            console.log(`Processing relevant WebSocket message for receipt ${pendingReceiptId}:`, lastMessage);
            if (lastMessage.status === 'completed') {
                setSaleStatusMessage(`Payment Completed! Receipt: ${lastMessage.receiptId}. M-Pesa Ref: ${lastMessage.mpesaReceiptNumber || 'N/A'}`);
            } else {
                setSaleStatusMessage(`Payment Failed for ${lastMessage.receiptId}: ${lastMessage.message}`);
            }
            setPendingReceiptId(null); // Clear pending state
        }
    }, [lastMessage, pendingReceiptId]);


    // --- Updated Sale Handling Function ---
    const handleSell = async (payload: SellItemsSchema) => {
        if (!authToken) {
            setSaleStatusMessage("Authentication required.");
            return;
        }
        setIsLoading(true);
        setSaleStatusMessage("Processing sale...");
        setPendingReceiptId(null); // Reset pending state

        try {
            // Call the updated service function
            const result: SellItemsResult = await sellItems(authToken, payload);

            // Check the result using the 'ok' flag and 'status'
            if (result.ok) {
                // Successful API call (status 200 or 202)
                if (result.status === 200) {
                    // Synchronous completion (e.g., Cash)
                    console.log("Sale Completed Synchronously:", result.data); // data is Receipt
                    setSaleStatusMessage(`Sale successful! Receipt: ${result.data.receipt_number}`);
                } else if (result.status === 202) {
                    // Asynchronous process started (e.g., M-Pesa)
                    console.log("Sale Pending Asynchronously:", result.data); // data is SalePendingResponse
                    setSaleStatusMessage(`Sale initiated (Pending M-Pesa): ${result.data.message}. Waiting for confirmation... Receipt ID: ${result.data.receiptId}`);
                    // Store the receiptId to match WebSocket updates
                    setPendingReceiptId(result.data.receiptId);
                }
                // Note: The 'else' case for status here shouldn't happen if 'ok' is true
                // based on the SellItemsResult definition, but checking status is safer.

            } else {
                // API call failed (ok: false)
                console.error("Sale failed:", result.error, `(Status: ${result.status})`);
                setSaleStatusMessage(`Sale failed: ${result.error || 'Unknown error'} (Status: ${result.status})`);
            }
        } catch (error: any) {
            // Catch unexpected errors *outside* the fetch promise itself
            // (e.g., issues in component logic before/after the call)
            // Note: The service function now catches network errors and returns { ok: false, ... }
            // So this catch block might be less likely to hit for network issues,
            // but good to keep for other potential component-level errors.
            console.error("Unexpected error during sale process:", error);
            setSaleStatusMessage(`An unexpected error occurred: ${error.message || 'Unknown issue'}`);
        } finally {
            setIsLoading(false);
        }
    };

    // --- Render (remains the same) ---
    return (
        <div>
            <p>
                Real-time Status: <span style={{ fontWeight: 'bold' }}>{connectionStatus.toUpperCase()}</span>
                {connectionStatus === 'error' && <span style={{ color: 'red' }}> (Check console/connection)</span>}
            </p>

            <button
                onClick={() => handleSell({ /* your SellItemsSchema payload */
                    items: [/* ... item data ... */],
                    payment_method: 'mpesa', // or 'cash'
                    mpesa_payload: { amount: 10.00, phone_number: '254...' } // Only if 'mpesa'
                })}
                disabled={isLoading || connectionStatus !== 'open'}
                title={connectionStatus !== 'open' ? "Real-time updates may be delayed" : ""}
            >
                {isLoading ? 'Processing...' : 'Sell Items'}
            </button>

            {saleStatusMessage && <p>Status: {saleStatusMessage}</p>}

            {/* {lastMessage && <pre>Last WS Message: {JSON.stringify(lastMessage, null, 2)}</pre>} */}
        </div>
    );
};

export default SaleComponent;