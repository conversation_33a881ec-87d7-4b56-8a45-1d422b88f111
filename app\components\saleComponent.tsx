import React, { useState, useEffect } from 'react';
import { sellItems, SellItemsResult } from '@/lib/api/sales/service';
import { SellItemsSchema } from '@/lib/api/sales/models';
import { useWebSocket, PaymentStatusMessage } from '@/lib/hooks/useWebSocket';
import Cookies from 'js-cookie';

const SaleComponent: React.FC = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [saleStatusMessage, setSaleStatusMessage] = useState<string | null>(null);
    const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const authToken = Cookies.get('auth_token');

    const { connectionStatus, lastMessage, sendMessage } = useWebSocket(!!authToken);

    useEffect(() => {
        if (lastMessage && lastMessage.receiptId === pendingReceiptId) {
            console.log(`Processing relevant WebSocket message for receipt ${pendingReceiptId}:`, lastMessage);
            if (lastMessage.status === 'completed') {
                setSaleStatusMessage(`Payment Completed! Receipt: ${lastMessage.receiptId}. M-Pesa Ref: ${lastMessage.mpesaReceiptNumber || 'N/A'}`);
                setIsLoading(false);
                setIsDialogOpen(false);
            } else {
                setSaleStatusMessage(`Payment Failed for ${lastMessage.receiptId}: ${lastMessage.message}`);
                setIsLoading(false);
                // Keep dialog open to show error
            }
            setPendingReceiptId(null);
        }
    }, [lastMessage, pendingReceiptId]);

    const handleSell = async (payload: SellItemsSchema) => {
        if (!authToken) {
            setSaleStatusMessage("Authentication required.");
            return;
        }
        setIsLoading(true);
        setSaleStatusMessage("Processing sale...");
        setPendingReceiptId(null);
        setIsDialogOpen(true);

        try {
            const result: SellItemsResult = await sellItems(authToken, payload);

            if (result.ok) {
                if (result.status === 200) {
                    console.log("Sale Completed Synchronously:", result.data);
                    setSaleStatusMessage(`Sale successful! Receipt: ${result.data.receipt_number}`);
                    setIsLoading(false);
                    setIsDialogOpen(false);
                } else if (result.status === 202) {
                    console.log("Sale Pending Asynchronously:", result.data);
                    setSaleStatusMessage(`Sale initiated (Pending M-Pesa): ${result.data.message}. Waiting for confirmation... Receipt ID: ${result.data.receiptId}`);
                    setPendingReceiptId(result.data.receiptId);
                    // Keep loading state true while waiting for WebSocket response
                }
            } else {
                console.error("Sale failed:", result.error, `(Status: ${result.status})`);
                setSaleStatusMessage(`Sale failed: ${result.error || 'Unknown error'} (Status: ${result.status})`);
                setIsLoading(false);
                // Keep dialog open to show error
            }
        } catch (error: any) {
            console.error("Unexpected error during sale process:", error);
            setSaleStatusMessage(`An unexpected error occurred: ${error.message || 'Unknown issue'}`);
            setIsLoading(false);
            // Keep dialog open to show error
        }
    };

    return (
        <div>
            <p>
                Real-time Status: <span style={{ fontWeight: 'bold' }}>{connectionStatus.toUpperCase()}</span>
                {connectionStatus === 'error' && <span style={{ color: 'red' }}> (Check console/connection)</span>}
            </p>

            <button
                onClick={() => handleSell({
                    items: [],
                    payment_method: 'mpesa',
                    mpesa_payload: { amount: 10.00, phone_number: '254...' }
                })}
                disabled={isLoading || connectionStatus !== 'open'}
                title={connectionStatus !== 'open' ? "Real-time updates may be delayed" : ""}
            >
                {isLoading ? 'Processing...' : 'Sell Items'}
            </button>

            {saleStatusMessage && <p>Status: {saleStatusMessage}</p>}
        </div>
    );
};

export default SaleComponent;