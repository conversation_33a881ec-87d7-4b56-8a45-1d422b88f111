"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";


import { useRouter } from "next/navigation";

import { columns as Columns } from "./columns";
import Cookies from "js-cookie";
import { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";

function LoadingSpinner() {
    return (
        <div className="flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin" />
        </div>
    );
}


export default function Page() {
    const router = useRouter();
    const [data, setReceipts] = useState<Receipt[]>([]);

    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [rowSelection, setRowSelection] = useState({});
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(10);

    const token = Cookies.get("auth_token");
    const store_id = Cookies.get("active_store");
    const isMobile = useMediaQuery("(max-width: 768px)");

    useEffect(() => {
        async function getReceipts() {
            if (token && store_id) {
                setIsInitialLoading(true);
                try {
                    const fetchedReceipts = await fetchReceipts(token, store_id);
                    setReceipts(fetchedReceipts);
                } catch (error) {
                    console.error("Error fetching suppliers:", error);
                } finally {
                    setIsInitialLoading(false);
                }
            }
        }
        getReceipts();
    }, [store_id, token]);

    const handleDeleteSelected = async () => {
        if (!token || !store_id) {
            console.error("Authentication token or store ID is missing.");
            return;
        }
        const selectedRows = table.getSelectedRowModel().rows;
        const selectedReceiptIds = selectedRows.map((row) => row.original.id);

        try {
            for (const customerId of selectedReceiptIds) {
                // await deleteReceipt(token, store_id, customerId);
                router.refresh();
            }
            setReceipts((prev) =>
                prev.filter((customer) => !selectedReceiptIds.includes(customer.id)),
            );
            setRowSelection({});
            setIsDeleteDialogOpen(false);
        } catch (error) {
            console.error("Error deleting customers:", error);
        }
    };

    const columns = useMemo(() => Columns, []);

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    const filteredRows = table.getFilteredRowModel().rows;
    const sortedRows = table.getSortedRowModel().rows;
    const pageCount = Math.ceil(sortedRows.length / pageSize);
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    const paginatedRows = sortedRows.slice(start, end);

    const showSelectedItems = (
        <ul>
            {table
                .getSelectedRowModel()
                .rows.map((row) => (
                    <li key={row.id}>- {row.original.receipt_number}</li>
                ))}
        </ul>
    );

    return (
        <div className="w-full">
            {/* Top Section: Input and Columns Dropdown */}
            <div className="flex flex-col items-start sm:flex-row sm:items-center gap-4 py-4">
                <Input
                    placeholder="Filter receipts..."
                    value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
                    onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
                    className="w-full sm:max-w-sm"
                    disabled={isInitialLoading}
                />
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-2 sm:mt-0">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" disabled={isInitialLoading} className="w-full sm:w-auto">
                                Columns <ChevronDown />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {table
                                .getAllColumns()
                                .filter((column) => column.getCanHide())
                                .map((column) => (
                                    <DropdownMenuCheckboxItem
                                        key={column.id}
                                        className="capitalize"
                                        checked={column.getIsVisible()}
                                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                                    >
                                        {column.id}
                                    </DropdownMenuCheckboxItem>
                                ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* --- START OF CONDITIONAL RENDERING --- */}

            {/*-- TABLE VIEW FOR DESKTOP --*/}
            {!isMobile && (
                <div className="rounded-md border relative w-full overflow-auto">
                    <Table className="w-full caption-bottom text-sm">
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {isInitialLoading ? (
                                <TableRow>
                                    <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                                        <LoadingSpinner />
                                    </TableCell>
                                </TableRow>
                            ) : paginatedRows.length ? (
                                paginatedRows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            )}

            {/*-- CARD VIEW FOR MOBILE (RECEIPTS) --*/}
            {isMobile && (
                <div className="grid grid-cols-1 gap-4">
                    {isInitialLoading && <LoadingSpinner />}
                    {!isInitialLoading && paginatedRows.length === 0 && (
                        <div className="text-center text-muted-foreground p-4">No results.</div>
                    )}
                    {paginatedRows.map(row => {
                        // Find the special cells for custom placement
                        const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
                        const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
                        // Filter out special cells AND the receipt_number to render the rest as key-value pairs
                        const dataCells = row.getVisibleCells().filter(
                            cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'receipt_number'
                        );
                        return (
                            <div
                                key={row.id}
                                className="bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative"
                                data-state={row.getIsSelected() ? "selected" : undefined}
                            >
                                {/* Part 1: Header with Receipt Number, Checkbox, and Actions Menu */}
                                <div className="flex justify-between items-start mb-2">
                                    <label className="flex items-center space-x-3">
                                        {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                                        <span className="font-bold text-lg break-words">
                                            {row.getValue("receipt_number")}
                                        </span>
                                    </label>
                                    <div className="absolute top-2 right-2">
                                        {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                                    </div>
                                </div>
                                <hr className="border-border" />
                                {/* Part 2: Details with proper alignment */}
                                <div className="space-y-3 pt-2">
                                    {dataCells.map(cell => (
                                        <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                                            <div className="font-medium text-muted-foreground">
                                                {formatHeader(cell.column.id)}
                                            </div>
                                            <div className="text-right break-words">
                                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}
            {/* --- END OF CONDITIONAL RENDERING --- */}

            <div className="flex items-center justify-end space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
                </div>
                <div className="space-x-2">
                    <TablePagination
                        pageIndex={pageIndex}
                        pageSize={pageSize}
                        pageCount={pageCount}
                        totalItems={filteredRows.length}
                        onPageChange={setPageIndex}
                        onPageSizeChange={setPageSize}
                    />
                </div>
            </div>
        </div>
    );

}