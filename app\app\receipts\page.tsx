"use client";

import { useEffect, useState } from "react";
import {
    ColumnFiltersState,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";

import { columns as Columns } from "./columns";
import Cookies from "js-cookie";
import { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";

function LoadingSpinner() {
    return (
        <div className="flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin" />
        </div>
    );
}

export default function Page() {
    const router = useRouter();
    const [data, setReceipts] = useState<Receipt[]>([]);

    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [rowSelection, setRowSelection] = useState({});
    const [isInitialLoading, setIsInitialLoading] = useState(true);

    const token = Cookies.get("auth_token");
    const store_id = Cookies.get("active_store");

    useEffect(() => {
        async function getReceipts() {
            if (token && store_id) {
                setIsInitialLoading(true);
                try {
                    const fetchedReceipts = await fetchReceipts(token, store_id);
                    setReceipts(fetchedReceipts);
                } catch (error) {
                    console.error("Error fetching suppliers:", error);
                } finally {
                    setIsInitialLoading(false);
                }
            }
        }
        getReceipts();
    }, [store_id, token]);

    const handleDeleteSelected = async () => {
        if (!token || !store_id) {
            console.error("Authentication token or store ID is missing.");
            return;
        }
        const selectedRows = table.getSelectedRowModel().rows;
        const selectedReceiptIds = selectedRows.map((row) => row.original.id);

        try {
            for (const customerId of selectedReceiptIds) {
                // await deleteReceipt(token, store_id, customerId);
                router.refresh();
            }
            setReceipts((prev) =>
                prev.filter((customer) => !selectedReceiptIds.includes(customer.id)),
            );
            setRowSelection({});
            setIsDeleteDialogOpen(false);
        } catch (error) {
            console.error("Error deleting customers:", error);
        }
    };

    const columns = Columns;

    const table = useReactTable({
        data,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    const showSelectedItems = (
        <ul>
            {table
                .getSelectedRowModel()
                .rows.map((row) => (
                    <li key={row.id}>- {row.original.receipt_number}</li>
                ))}
        </ul>
    );

    return (
        <div className="w-full">
            {/* Top Section: Input and Columns Dropdown */}
            <div className="flex items-center justify-between py-4">
                <Input
                    placeholder="Filter receipts..."
                    value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
                    onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
                    className="max-w-sm"
                    disabled={isInitialLoading}
                />
                {/* Columns Dropdown moved to the far right */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="outline" disabled={isInitialLoading}>
                            Columns <ChevronDown />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        {table
                            .getAllColumns()
                            .filter((column) => column.getCanHide())
                            .map((column) => (
                                <DropdownMenuCheckboxItem
                                    key={column.id}
                                    className="capitalize"
                                    checked={column.getIsVisible()}
                                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                                >
                                    {column.id}
                                </DropdownMenuCheckboxItem>
                            ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table
                            .getHeaderGroups()
                            .map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead key={header.id}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext(),
                                                )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                    </TableHeader>
                    <TableBody>
                        {isInitialLoading ? (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    <LoadingSpinner />
                                </TableCell>
                            </TableRow>
                        ) : table.getRowModel().rows?.length ? (
                            table
                                .getRowModel()
                                .rows.map((row) => (
                                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext(),
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>

            </div>
            <div className="flex items-center justify-end space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {table.getFilteredSelectedRowModel().rows.length} of{" "}
                    {table.getFilteredRowModel().rows.length} row(s) selected.
                </div>
                <div className="space-x-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.previousPage()}
                        disabled={!table.getCanPreviousPage() || isInitialLoading}
                    >
                        Previous
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => table.nextPage()}
                        disabled={!table.getCanNextPage() || isInitialLoading}
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    );

}