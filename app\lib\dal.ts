import { cookies } from 'next/headers';

export async function verifySession() {
  const cookieStore = cookies();
  const sessionCookie = cookieStore.get('session');
  
  if (!sessionCookie) {
    return null;
  }

  try {
    // Here you would typically verify the session token with your backend
    // For now, we'll just check if the cookie exists
    return {
      id: sessionCookie.value,
      // Add any other session data you need
    };
  } catch (error) {
    console.error('Session verification failed:', error);
    return null;
  }
} 