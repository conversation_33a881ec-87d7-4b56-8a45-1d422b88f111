import React from "react";

export default function TermsPage() {
  return (
    <main className="container mx-auto px-4 py-16 max-w-3xl">
      <h1 className="text-3xl font-bold mb-8 text-center">Terms & Conditions</h1>
      <p className="mb-6 text-sm text-muted-foreground text-center">Last updated: May 6, 2025</p>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">1. Beta Disclaimer</h2>
        <p>
          StoreYako POS is currently in beta testing. The features and performance of this web app are subject to change. We appreciate your feedback as we work to improve the service. Please be aware that data loss or unexpected issues may occur during this period.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">2. Acceptance of Terms</h2>
        <p>
          By accessing or using StoreYako POS, you agree to be bound by these Terms & Conditions. If you do not agree, please do not use the app.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">3. Use of the Service</h2>
        <ul className="list-disc pl-6">
          <li>You may use StoreYako POS only for lawful purposes.</li>
          <li>You are responsible for maintaining the confidentiality of your account and data.</li>
          <li>We reserve the right to suspend or terminate your access at any time without notice, especially during beta testing.</li>
        </ul>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">4. Intellectual Property</h2>
        <p>
          All content, trademarks, and data on StoreYako POS are the property of StoreYako or its licensors. You may not reproduce, distribute, or create derivative works without permission.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">5. Limitation of Liability</h2>
        <p>
          StoreYako POS is provided “as is” without warranties of any kind. We are not liable for any damages or loss arising from your use or inability to use the app, including during beta testing.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">6. Changes to Terms</h2>
        <p>
          We may update these Terms & Conditions at any time. Continued use of the app constitutes acceptance of the new terms.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">7. Contact</h2>
        <p>
          For questions or feedback, please contact us at <a href="mailto:<EMAIL>" className="text-primary underline"><EMAIL></a>.
        </p>
      </section>
    </main>
  );
}
