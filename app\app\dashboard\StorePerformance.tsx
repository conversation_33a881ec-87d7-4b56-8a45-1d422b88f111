import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import { fetchStoreMetrics } from '@/lib/api/reports/service';
import { getMe } from '@/lib/api/users/service';
import { getStores } from '@/lib/api/retailStores/service';
import { Store } from '@/lib/api/retailStores/models';
import Cookies from 'js-cookie';
import { EXCHANGE_RATE_API_KEY } from "@/app/configs/constants";

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface StorePerformanceData {
    id: string;
    name: string;
    sales: number;
    transactions: number;
    averageTransactionValue: number;
    currency?: string;
}

const CURRENCIES = [
    { code: 'KES', name: 'Kenyan Shilling' },
    { code: 'UGX', name: 'Ugandan <PERSON>lling' },
    { code: 'TZS', name: 'Tanzanian <PERSON>' },
    { code: 'USD', name: 'US Dollar' },
    { code: 'EUR', name: 'Euro' },
    { code: 'GBP', name: 'British Pound' },
    { code: 'JPY', name: 'Japanese Yen' },
    { code: 'INR', name: 'Indian Rupee' },
];

async function fetchExchangeRates(baseCurrency: string) {
    const url = `https://v6.exchangerate-api.com/v6/${EXCHANGE_RATE_API_KEY}/latest/${baseCurrency}`;
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`Failed to fetch exchange rates: ${response.status}`);
    }
    const data = await response.json();
    if (data.result === 'error') {
        throw new Error(data['error-type']);
    }
    return data.conversion_rates;
}

interface StorePerformanceProps {
    stores: Store[] | null;
}

const StorePerformance = ({ }: StorePerformanceProps) => {
    const [storeData, setStoreData] = useState<StorePerformanceData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [neutralCurrency, setNeutralCurrency] = useState('USD');
    const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
    //const [barChartDataSales, setBarChartDataSales] = useState<any>(null);
    //const [barChartDataTransactions, setBarChartDataTransactions] = useState<any>(null);

    const authToken = Cookies.get('auth_token');

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);

            try {
                if (!authToken) {
                    throw new Error('Authentication token is missing');
                }

                const user = await getMe(authToken);
                const adminId = user.id;

                const metrics = await fetchStoreMetrics(authToken, adminId);
                const stores: Store[] = await getStores(authToken);

                const combinedData = metrics.map((metric) => {
                    const store = stores.find((store) => store.id === metric.store_id);
                    return {
                        id: metric.store_id,
                        name: store ? store.name : 'Unknown Store',
                        sales: metric.sales,
                        transactions: metric.transactions,
                        averageTransactionValue: metric.average_transaction_value,
                        currency: store?.currency || 'USD',
                    };
                });

                setStoreData(combinedData);
            } catch (err) {
                setError('Failed to load store performance data.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        const fetchRates = async () => {
            try {
                const rates = await fetchExchangeRates(neutralCurrency);
                setExchangeRates(rates);
            } catch (err) {
                console.error('Error fetching exchange rates:', err);
                setError('Failed to fetch exchange rates.');
            }
        };


        fetchData();
        fetchRates();
    }, [authToken]);

    const convertToNeutralCurrency = (value: number, fromCurrency: string): number => {
        const rate = exchangeRates[fromCurrency];
        if (!rate) {
            console.warn(`Exchange rate not found for currency: ${fromCurrency}`);
            return value;
        }
        return value / rate;
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: neutralCurrency,
        }).format(value);
    };

    const barChartDataSales = {
        labels: storeData.map((store) => store.name),
        datasets: [
            {
                label: 'Sales',
                data: storeData.map((store) =>
                    convertToNeutralCurrency(store.sales, store.currency || 'USD')
                ),
                backgroundColor: 'rgba(53, 162, 235, 0.5)',
                borderColor: 'rgba(53, 162, 235, 1)',
                borderWidth: 1,
            },
        ],
    };

    const barChartDataTransactions = {
        labels: storeData.map((store) => store.name),
        datasets: [
            {
                label: 'Transactions',
                data: storeData.map((store) => store.transactions),
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1,
            },
        ],
    };

    const barChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: `Store Sales Comparison (in ${neutralCurrency})`,
            },
        },
    };

    const barChartOptionsTransactions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: 'Store Transaction Comparison',
            },
        },
    };

    if (loading) {
        return (
            <div className="h-full flex justify-center items-center">
                <div className="animate-pulse flex flex-col items-center">
                    <div className="h-8 w-8 rounded-full border-3 border-t-blue-500 border-gray-200 animate-spin"></div>
                    <p className="mt-2 text-sm text-gray-500">Loading store data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="h-full flex justify-center items-center">
                <div className="text-center">
                    <div className="text-red-500 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <p className="text-sm text-gray-600">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 h-full">
                <div className="flex flex-col space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Store Performance</h2>
                        <div className="flex items-center space-x-2">
                            <label htmlFor="currency" className="text-sm text-gray-600 dark:text-gray-300">
                                Currency:
                            </label>
                            <select
                                id="currency"
                                value={neutralCurrency}
                                onChange={(e) => setNeutralCurrency(e.target.value)}
                                className="text-sm border border-gray-200 dark:border-gray-600 rounded-md px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                {CURRENCIES.map((currency) => (
                                    <option key={currency.code} value={currency.code}>
                                        {currency.code}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Table with shadow and better spacing */}
                    <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead className="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Store Name</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sales</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Transactions</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. Value</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    {storeData.map((store) => (
                                        <tr key={store.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                            <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">{store.name}</td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                                                {formatCurrency(convertToNeutralCurrency(store.sales, store.currency || 'USD'))}
                                            </td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">{store.transactions}</td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                                                {formatCurrency(convertToNeutralCurrency(store.averageTransactionValue, store.currency || 'USD'))}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Charts Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[400px]">
                            {barChartDataSales && <Bar options={{
                                ...barChartOptions,
                                plugins: {
                                    ...barChartOptions.plugins,
                                    title: {
                                        ...barChartOptions.plugins.title,
                                        text: `Sales (${neutralCurrency})`,
                                        font: { size: 14 },
                                        color: 'rgb(107 114 128)'
                                    },
                                    legend: {
                                        display: false
                                    }
                                }
                            }} data={barChartDataSales} />}
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[400px]">
                            {barChartDataTransactions && <Bar options={{
                                ...barChartOptionsTransactions,
                                plugins: {
                                    ...barChartOptionsTransactions.plugins,
                                    title: {
                                        ...barChartOptionsTransactions.plugins.title,
                                        text: 'Transactions',
                                        font: { size: 14 },
                                        color: 'rgb(107 114 128)'
                                    },
                                    legend: {
                                        display: false
                                    }
                                }
                            }} data={barChartDataTransactions} />}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StorePerformance;