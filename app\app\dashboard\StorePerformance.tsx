import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import { fetchStoreMetrics } from '@/lib/api/reports/service';
import { getMe } from '@/lib/api/users/service';
import { getStores } from '@/lib/api/retailStores/service';
import { Store } from '@/lib/api/retailStores/models';
import Cookies from 'js-cookie';
import { EXCHANGE_RATE_API_KEY } from "@/app/configs/constants";

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface StorePerformanceData {
    id: string;
    name: string;
    sales: number;
    transactions: number;
    averageTransactionValue: number;
    currency?: string;
}

const CURRENCIES = [
    { code: 'KES', name: 'Kenyan Shilling' },
    { code: 'UGX', name: 'Ugandan <PERSON>lling' },
    { code: 'TZS', name: 'Tanzanian <PERSON>' },
    { code: 'USD', name: 'US Dollar' },
    { code: 'EUR', name: 'Euro' },
    { code: 'GBP', name: 'British Pound' },
    { code: 'JPY', name: 'Japanese Yen' },
    { code: 'INR', name: 'Indian Rupee' },
];

async function fetchExchangeRates(baseCurrency: string) {
    const url = `https://v6.exchangerate-api.com/v6/${EXCHANGE_RATE_API_KEY}/latest/${baseCurrency}`;
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`Failed to fetch exchange rates: ${response.status}`);
    }
    const data = await response.json();
    if (data.result === 'error') {
        throw new Error(data['error-type']);
    }
    return data.conversion_rates;
}

interface StorePerformanceProps {
    stores: Store[] | null;
}

const StorePerformance = ({ stores }: StorePerformanceProps) => {
    const [storeData, setStoreData] = useState<StorePerformanceData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [neutralCurrency, setNeutralCurrency] = useState('USD');
    const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
    //const [barChartDataSales, setBarChartDataSales] = useState<any>(null);
    //const [barChartDataTransactions, setBarChartDataTransactions] = useState<any>(null);

    const authToken = Cookies.get('auth_token');

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);

            try {
                if (!authToken) {
                    throw new Error('Authentication token is missing');
                }

                const user = await getMe(authToken);
                const adminId = user.id;

                const metrics = await fetchStoreMetrics(authToken, adminId);
                const stores: Store[] = await getStores(authToken);

                const combinedData = metrics.map((metric) => {
                    const store = stores.find((store) => store.id === metric.store_id);
                    return {
                        id: metric.store_id,
                        name: store ? store.name : 'Unknown Store',
                        sales: metric.sales,
                        transactions: metric.transactions,
                        averageTransactionValue: metric.average_transaction_value,
                        currency: store?.currency || 'USD',
                    };
                });

                setStoreData(combinedData);
            } catch (err) {
                setError('Failed to load store performance data.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        const fetchRates = async () => {
            try {
                const rates = await fetchExchangeRates(neutralCurrency);
                setExchangeRates(rates);
            } catch (err) {
                console.error('Error fetching exchange rates:', err);
                setError('Failed to fetch exchange rates.');
            }
        };


        fetchData();
        fetchRates();
    }, [authToken]);

    const convertToNeutralCurrency = (value: number, fromCurrency: string): number => {
        const rate = exchangeRates[fromCurrency];
        if (!rate) {
            console.warn(`Exchange rate not found for currency: ${fromCurrency}`);
            return value;
        }
        return value / rate;
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: neutralCurrency,
        }).format(value);
    };

    const barChartDataSales = {
        labels: storeData.map((store) => store.name),
        datasets: [
            {
                label: 'Sales',
                data: storeData.map((store) =>
                    convertToNeutralCurrency(store.sales, store.currency || 'USD')
                ),
                backgroundColor: 'rgba(53, 162, 235, 0.5)',
                borderColor: 'rgba(53, 162, 235, 1)',
                borderWidth: 1,
            },
        ],
    };

    const barChartDataTransactions = {
        labels: storeData.map((store) => store.name),
        datasets: [
            {
                label: 'Transactions',
                data: storeData.map((store) => store.transactions),
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1,
            },
        ],
    };

    const barChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: `Store Sales Comparison (in ${neutralCurrency})`,
            },
        },
    };

    const barChartOptionsTransactions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: 'Store Transaction Comparison',
            },
        },
    };

    if (loading) {
        return <div className="p-4">Loading...</div>;
    }

    if (error) {
        return <div className="p-4 text-red-500">{error}</div>;
    }

    return (
        <div className="h-full flex flex-col">
            <h2 className="text-lg font-semibold mb-4">Store Performance</h2>

            {/* Neutral Currency Dropdown */}
            <div className="mb-4">
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
                    Select Neutral Currency
                </label>
                <select
                    id="currency"
                    value={neutralCurrency}
                    onChange={(e) => setNeutralCurrency(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                    {CURRENCIES.map((currency) => (
                        <option key={currency.code} value={currency.code}>
                            {currency.name} ({currency.code})
                        </option>
                    ))}
                </select>
            </div>

            {/* Table */}
            <div className="overflow-x-auto mb-4 flex-grow">
                <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Store Name</th>
                            <th scope="col" className="px-3 py-3.5 text-right text-sm font-semibold text-gray-900 dark:text-white">Sales</th>
                            <th scope="col" className="px-3 py-3.5 text-right text-sm font-semibold text-gray-900 dark:text-white">Transactions</th>
                            <th scope="col" className="px-3 py-3.5 text-right text-sm font-semibold text-gray-900 dark:text-white">Avg. Transaction Value</th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white dark:bg-gray-800">
                        {storeData.map((store) => (
                            <tr key={store.id}>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{store.name}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300 text-right">
                                    {formatCurrency(convertToNeutralCurrency(store.sales, store.currency || 'USD'))}
                                </td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300 text-right">{store.transactions}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300 text-right">
                                    {formatCurrency(convertToNeutralCurrency(store.averageTransactionValue, store.currency || 'USD'))}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Bar Charts */}
            <div className="mb-4 flex-grow">
                {barChartDataSales && <Bar options={barChartOptions} data={barChartDataSales} />}
            </div>
            <div className="flex-grow">
                {barChartDataTransactions && <Bar options={barChartOptionsTransactions} data={barChartDataTransactions} />}
            </div>
        </div>
    );
};

export default StorePerformance;