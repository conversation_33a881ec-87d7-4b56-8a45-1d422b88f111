import { BASE_URL } from "@/app/configs/constants";
import { LowStockItem, EmployeePerformance, MonthlyFinancialData, DailyAverageSales, ProductPerformance, DailyTotalSales, DailyStoreAggregatedAverageSales, StoreMetrics, DashboardStats } from "./models";

export async function fetchLowStockItems(authToken: string, storeId: string): Promise<LowStockItem[]> {
  const url = `${BASE_URL}/sales/statistics/${storeId}/low-stock-items`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: LowStockItem[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching low stock items:", error);
    return [];
  }

}

export async function fetchEmployeePerformaces(authToken: string, storeId: string): Promise<EmployeePerformance[]> {
  const url = `${BASE_URL}/sales/statistics/${storeId}/employee-performance`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: EmployeePerformance[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching employee performance data:", error);
    return [];
  }
}

export async function fetchFinancialOverview(authToken: string, storeId: string): Promise<MonthlyFinancialData[]> {
  const url = `${BASE_URL}/sales/statistics/${storeId}/financial-overview`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: MonthlyFinancialData[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching financial overview data:", error);
    return [];
  }
}

export async function fetchDailyAverageSales(authToken: string, storeId: string): Promise<DailyAverageSales[]> {
  const url = `${BASE_URL}/sales/statistics/${storeId}/daily-average-sales`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: DailyAverageSales[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching daily average sales data:", error);
    return [];
  }
}

export async function productPerformance(authToken: string, storeId: string): Promise<ProductPerformance[]> {
  // used to get sales by category
  //{ "category": "string", "quantity": 0 }

  const url = `${BASE_URL}/sales/statistics/${storeId}/product-performance`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: ProductPerformance[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching product performance data:", error);
    return [];
  }
}

export async function fetchDailyTotalSales(authToken: string, storeId: string): Promise<DailyTotalSales[]> {
  // this give sales without targets. I will add targets later
  const url = `${BASE_URL}/sales/statistics/${storeId}/sales`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: DailyTotalSales[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching daily total sales data:", error);
    return [{ date: "", total: 0 }];
  }

}

export async function fetchDailyStoreAggregatedAverageSales(authToken: string, storeId: string): Promise<DailyStoreAggregatedAverageSales> {
  const url = `${BASE_URL}/sales/statistics/${storeId}/daily-aggregate-sales`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: DailyStoreAggregatedAverageSales = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching daily average sales data:", error);
    return {
      monday: 0,
      tuesday: 0,
      wednesday: 0,
      thursday: 0,
      friday: 0,
      saturday: 0,
      sunday: 0
    };
  }
}

export async function fetchStoreMetrics(authToken: string, adminId: string): Promise<StoreMetrics[]> {
  const url = `${BASE_URL}/sales/statistics/${adminId}/store-performance-summary`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: StoreMetrics[] = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching store metrics data:", error);
    return [];
  }
}

export async function fetchDashboardStats(authToken: string, adminId: string): Promise<DashboardStats> {
  const url = `${BASE_URL}/sales/statistics/${adminId}/dashboard-stats`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  };
  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data: DashboardStats = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching dashboard stats data:", error);
    throw error;
  }
}
