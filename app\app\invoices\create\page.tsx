"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { InvoiceForm } from "@/app/app/invoices/InvoiceForm";
import { Customer } from "@/lib/api/customers/models";
import { fetchCustomers } from "@/lib/api/customers/service";
import { createInvoice, createInvoiceItems } from "@/lib/api/invoices/services";
import { AddInvoicePayload } from "@/lib/api/invoices/models";
import { useRouter } from "next/navigation";

export default function CreateInvoicePage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const router = useRouter();

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    async function fetchDropDownData() {
      if (token && store_id) {
        try {
          const customers = await fetchCustomers(token, store_id);
          setCustomers(customers);
        } catch (error) {
          console.error("Failed to fetch customers:", error);
        }
      }
    }
    fetchDropDownData();
  }, [token, store_id]);

  const handleSubmit = async (data: AddInvoicePayload) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    const payload = {
      ...data,
      storeId: store_id,
      items: data.items.map(item => ({
        ...item,
        invoiceId: '',
      })),
    };

    const invoiceResponse = await createInvoice(token, store_id, payload);

    if (!invoiceResponse?.id) {
      throw new Error("Failed to create invoice.");
    }

    const invoiceId = invoiceResponse.id;

    if (data.items.length > 0) {
      const itemsPayload = data.items.map(item => ({
        ...item,
        invoiceId: invoiceId,
      }));
      await createInvoiceItems(token, store_id, invoiceId, itemsPayload);
      router.push("/app/invoices")
    }
  };

  return (
    <InvoiceForm
      onSubmit={handleSubmit}
      customers={customers}
    />
  );
}
