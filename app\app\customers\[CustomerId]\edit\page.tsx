"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { getRetailStoreClient, updateRetailStoreClient } from "@/lib/api/clients/service";
import { CustomerForm } from "@/app/app/customers/CustomerForm";
import { NewCustomerSchema, Customer } from "@/lib/api/customers/models";


export default function EditCustomerPage({ params }: { params: { CustomerId: string } }) {
  const router = useRouter();
  const [initialData, setInitialData] = useState<Customer | null>(null);


  useEffect(() => {
    const fetchCustomerData = async () => {
      const token = Cookies.get('auth_token');
      const store_id = Cookies.get("active_store");

      if (!token || !store_id) {
        throw new Error('Authentication token or store ID is missing.');
      }

      const data = await getRetailStoreClient(token, store_id, params.CustomerId) as Customer;

      setInitialData(data);
    };

    fetchCustomerData();
  }, [params.CustomerId]);

  const handleSubmit = async (data: NewCustomerSchema) => {
    const token = Cookies.get('auth_token');
    const store_id = Cookies.get("active_store");

    if (!token || !store_id) {
      throw new Error('Authentication token or store ID is missing.');
    }

    await updateRetailStoreClient(token, store_id, params.CustomerId, data);
    router.push('/app/customers');
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <CustomerForm
      initialData={initialData}
      onSubmit={handleSubmit}
      isEditing={true}
    />
  );
}
