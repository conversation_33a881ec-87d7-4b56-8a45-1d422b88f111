// lib/utils/country-currency.ts
import { countries, currencies, lookup } from 'country-data-list';

interface CountryCurrency {
  country: string;
  code: string;
  currency: string;
  currencyName: string;
}

// Function to search for countries (for autocomplete)
export const searchCountries = (searchTerm: string): CountryCurrency[] => {
  if (!searchTerm) {
    return getAllCountries();
  }

  const searchTermLower = searchTerm.toLowerCase();
  const results = lookup.countries({ name: searchTermLower });
  const foundCountries = lookup.countries({ name: searchTermLower })
  if (!foundCountries || foundCountries.length === 0) return [] as CountryCurrency[]

  return foundCountries.map((country: { name: string; alpha2: string; currencies: string[] }) => {
    const currencyCode: string = country.currencies[0] || '';
    const currencyObj = Array.isArray(currencies) ? currencies.find((c) => c.code === currencyCode) : undefined;
    const currencyName: string = currencyObj?.name || '';
    return {
      country: country.name,
      code: country.alpha2,
      currency: currencyCode,
      currencyName
    } as CountryCurrency;
  })
};

export const getAllCountries = (): CountryCurrency[] => {
  return countries.all.map((country) => {
    const currencyCode = country.currencies[0] || '';
    const currencyObj = Array.isArray(currencies) ? currencies.find((c) => c.code === currencyCode) : undefined;
    const currencyName = currencyObj?.name || '';
    return {
      country: country.name,
      code: country.alpha2,
      currency: currencyCode,
      currencyName,
    };
  });
}
