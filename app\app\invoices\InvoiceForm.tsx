"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2, Plus, Receipt, DollarSign, FileText } from "lucide-react";
import { AddInvoicePayload, PaymentStatus } from "@/lib/api/invoices/models";
import { Customer } from "@/lib/api/customers/models";
import { Switch } from "@/components/ui/switch";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import Cookies from "js-cookie";

interface InvoiceFormProps {
  initialData?: AddInvoicePayload;
  onSubmit(data: AddInvoicePayload): Promise<void>;
  isEditing?: boolean;
  customers: Customer[];
}

export function InvoiceForm({ initialData, onSubmit, isEditing = false, customers }: InvoiceFormProps) {
  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<AddInvoicePayload>({
    defaultValues: initialData || {
      invoice_number: `INV-${new Date().getFullYear()}-${String("1").padStart(3, "0")}`,
      invoice_date: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      customer_id: '',
      store_id: '',
      items: [{ invoiceId: '', item_name: '', quantity: 1, price: 0 }],
      subtotal: '0',
      tax_amount: '0',
      total_amount: '0',
      notes: '',
      terms: 'Net 30',
      payment_status: PaymentStatus.Unpaid,
      has_tax: false,
    },
  });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "items",
  });

  const items = watch('items');
  const hasTax = watch('has_tax');
  const taxAmount = watch('tax_amount');

  useEffect(() => {
    const subtotal = items.reduce((sum, item) => sum + (Number(item.quantity) * Number(item.price)), 0);
    const tax = hasTax ? subtotal * ((Number(taxAmount) || 0) / 100) : 0;
    const totalAmount = subtotal + tax;

    setValue('subtotal', subtotal.toFixed(2));
    setValue('total_amount', totalAmount.toFixed(2));
  }, [items, hasTax, taxAmount, setValue]);

  const addNewItem = () => {
    append({
      invoiceId: '',
      item_name: '',
      quantity: 1,
      price: 0,
    });
  };

  const handleFormSubmit = async (data: AddInvoicePayload) => {
    setError(null);
    setSuccess(false);

    try {
      data.store_id = Cookies.get('active_store') || '';
      await onSubmit(data);
      setSuccess(true);
      if (!isEditing) {
        reset();
      }
    } catch (error) {
      setError((error as Error).message);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (event.key === "Enter") {
      event.preventDefault();
      if (index === fields.length - 1) {
        addNewItem();
      }
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
        <Card>
          {error && <AlertError message={error} />}
          {success && <AlertSuccess message={`Invoice ${isEditing ? 'updated' : 'created'} successfully!`} />}
          <CardHeader>
            <CardTitle className="text-2xl font-bold">{isEditing ? 'Edit Invoice' : 'New Invoice'}</CardTitle>
            <CardDescription>
              {isEditing ? 'Update the invoice details below.' : 'Fill in the invoice details below.'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* Basic Information Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Receipt className="h-5 w-5 text-muted-foreground" />
                <h3 className="text-lg font-semibold">Basic Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="invoiceNumber" className="text-sm font-medium">Invoice Number</Label>
                    <Input
                      id="invoiceNumber"
                      className="mt-1"
                      {...register('invoice_number', { required: true })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="invoiceDate" className="text-sm font-medium">Invoice Date</Label>
                    <Input
                      id="invoiceDate"
                      type="date"
                      className="mt-1"
                      {...register('invoice_date', { required: true })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="customerId" className="text-sm font-medium">Customer</Label>
                    <Select onValueChange={(value) => setValue('customer_id', value)}>
                      <SelectTrigger id="customerId" className="mt-1">
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="dueDate" className="text-sm font-medium">Due Date</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      className="mt-1"
                      {...register('dueDate')}
                    />
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Invoice Items Section */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-semibold">Invoice Items</h3>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addNewItem}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </div>

              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-12 gap-4 items-center bg-muted/50 p-4 rounded-lg">
                    <div className="col-span-5">
                      <Input
                        placeholder="Item name"
                        className="bg-background"
                        {...register(`items.${index}.item_name`, { required: true })}
                        onKeyDown={(e) => handleKeyDown(e, index)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Input
                        type="number"
                        placeholder="Quantity"
                        min="1"
                        className="bg-background"
                        {...register(`items.${index}.quantity`, {
                          required: true,
                          min: 1,
                          valueAsNumber: true,
                        })}
                        onKeyDown={(e) => handleKeyDown(e, index)}
                      />
                    </div>
                    <div className="col-span-3">
                      <Input
                        type="number"
                        placeholder="Price"
                        step="0.01"
                        min="0"
                        className="bg-background"
                        {...register(`items.${index}.price`, {
                          required: true,
                          min: 0,
                          valueAsNumber: true,
                        })}
                        onKeyDown={(e) => handleKeyDown(e, index)}
                      />
                    </div>
                    <div className="col-span-2 flex justify-end">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => remove(index)}
                        disabled={fields.length === 1}
                        className="hover:bg-destructive/10 hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Totals Section */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-muted-foreground" />
                <h3 className="text-lg font-semibold">Totals</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Subtotal:</Label>
                    <Input
                      className="w-48 text-right bg-muted"
                      {...register('subtotal')}
                      readOnly
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="has_tax"
                        onCheckedChange={(checked) => setValue("has_tax", checked)}
                      />
                      <Label htmlFor="has_tax" className="text-sm font-medium">Include Tax</Label>
                    </div>
                    {hasTax && (
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          className="w-24 text-right bg-muted"
                          {...register('tax_amount', { valueAsNumber: true })}
                          placeholder="Tax %"
                        />
                        <span className="text-sm text-muted-foreground">%</span>
                      </div>
                    )}
                  </div>
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-bold">Total Amount:</Label>
                    <Input
                      className="w-48 text-right bg-muted font-bold"
                      {...register('total_amount')}
                      readOnly
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="notes" className="text-sm font-medium">Notes</Label>
                    <Textarea
                      id="notes"
                      className="mt-1"
                      {...register('notes')}
                      placeholder="Add any additional notes here..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="terms" className="text-sm font-medium">Terms</Label>
                    <Textarea
                      id="terms"
                      className="mt-1"
                      {...register('terms')}
                      placeholder="e.g., Net 30"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => window.history.back()}
              >
                Cancel
              </Button>
              <Button type="submit">
                {isEditing ? 'Update Invoice' : 'Create Invoice'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
