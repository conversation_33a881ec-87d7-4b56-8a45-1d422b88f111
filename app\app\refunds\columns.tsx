import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Refund } from "@/lib/api/sales/refunds/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { UserInfoResponse } from "@/lib/api/users/models";

export const getColumns = (router: AppRouterInstance, users: UserInfoResponse[]): ColumnDef<Refund>[] => [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "return_id",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                Return ID
                <ArrowUpDown />
            </Button>
        ),
        cell: ({ row }) => <div>#{row.getValue("return_id")}</div>,
        enableSorting: true,
    },
    {
        accessorKey: "quantity_refunded",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                Quantity Refunded
                <ArrowUpDown />
            </Button>
        ),
        cell: ({ row }) => <div>{row.getValue("quantity_refunded")}</div>,
        enableSorting: true,
    },
    {
        accessorKey: "amount_refunded",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                Amount Refunded
                <ArrowUpDown />
            </Button>
        ),
        cell: ({ row }) => <div>{row.getValue("amount_refunded")}</div>,
        enableSorting: true,
    },
    {
        accessorKey: "timestamp",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                Date
                <ArrowUpDown />
            </Button>
        ),
        cell: ({ row }) => {
            const date = new Date(row.getValue("timestamp"));
            return <div>{date.toLocaleDateString()}</div>;
        },
        enableSorting: true,
    },
    {
        accessorKey: "staff_id",
        header: ({ column }) => (
            <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            >
                Staff ID
                <ArrowUpDown />
            </Button>
        ),
        cell: ({ row }) => {
            const staff = users.find((user) => user.id === row.getValue("staff_id"));
            return staff ? <div>{staff.name}</div> : null;
        },
        enableSorting: true,
    },
    {
        id: "actions",
        enableHiding: false,
        cell: ({ row }) => {
            const refund = row.original;

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                            onClick={() => navigator.clipboard.writeText(refund.id)}
                        >
                            Copy Refund ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/app/refunds/${refund.id}`)}>View Details</DropdownMenuItem>
                        {/* Add Edit/Delete actions here if needed */}
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        },
    },
];