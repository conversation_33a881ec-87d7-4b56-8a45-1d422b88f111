"use client";
import React from 'react';
import { submitOnboardingData } from '@/lib/api/users/onboarding/services';
import { OnboardingData, StoreOnboardingData, UserOnboardingData } from '@/lib/api/users/onboarding/models';
import StoreForm from './components/StoreForm';
import UserForm from './components/UserForm';
import { useRouter } from 'next/navigation';
import FormStepIndicator from './components/FormStepIndicator';
import { v4 as uuidv4 } from 'uuid';
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

// Define validation schemas
const userSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  plan_id: z.number().default(1),
  profile: z.string().default(""),
  role_id: z.number().default(1),
  store_id: z.string().default(""),
  user_defined_role_id: z.number().default(1),
});

const storeSchema = z.object({
  name: z.string().min(1, "Store name is required"),
  category: z.string().min(1, "Category is required"),
  phone: z.string().optional().or(z.literal("")), // Make phone optional, no validation
  country: z.string().min(1, "Country is required"),
  email: z.string().email().optional(),
  currency: z.string().optional().default('USD'),
  opening_hours: z.string().optional().default('09:00-17:00'),
  city: z.string().optional().default(''),
  postal_code: z.string().optional().default(''),
  state: z.string().optional().default(''),
  street_address: z.string().optional().default(''),
  payment_methods: z.string().optional().default('cash'),
  notes: z.string().optional().default(''),
  tax_rate: z.number().default(0),
  status: z.string().default("active"),
});

const onboardingSchema = z.object({
  user: userSchema,
  store: storeSchema,
});

type OnboardingFormValues = z.infer<typeof onboardingSchema>;

const initialValues: OnboardingFormValues = {
  user: {
    name: '',
    email: '',
    password: '',
    plan_id: 1,
    profile: '',
    role_id: 1,
    store_id: '',
    user_defined_role_id: 1,
  },
  store: {
    name: '',
    email: '',
    category: '',
    phone: '',
    country: '',
    //address: '',
    city: '',
    state: '',
    street_address: '',
    tax_rate: 0,
    currency: '',
    payment_methods: '',
    notes: '',
    status: 'active',
    opening_hours: '09:00-17:00',
    postal_code: '',
  },
};

export default function RegistrationPage() {
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  return (
    <GoogleReCaptchaProvider reCaptchaKey={siteKey || ""}>
      <RegistrationPageContent />
    </GoogleReCaptchaProvider>
  );
}

function RegistrationPageContent() {
  const [step, setStep] = React.useState(1);
  const [loading, setLoading] = React.useState(false);
  const [submitError, setSubmitError] = React.useState<string | null>(null);
  const router = useRouter();

  const { executeRecaptcha } = useGoogleReCaptcha();

  const methods = useForm<OnboardingFormValues>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: initialValues,
    mode: "onChange",
  });

  const totalSteps = 2;

  const pageVariants = {
    initial: { opacity: 0, x: 100 },
    in: { opacity: 1, x: 0 },
    out: { opacity: 0, x: -100 }
  };

  const pageTransition: import("framer-motion").Transition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.5
  };

  const handleUserSubmit = async () => {
    const isValid = await methods.trigger(['user.name', 'user.email', 'user.password']);
    if (isValid) {
      setStep(2);
    }
  };

  const handleStoreSubmit = async (data: OnboardingFormValues) => {
    let captcha_token: string | undefined;
    try {
      setLoading(true);
      setSubmitError(null);
      // Execute reCAPTCHA for this action
      if (!executeRecaptcha) {
        setSubmitError("reCAPTCHA not yet available. Please try again.");
        setLoading(false);
        return;
      }
      try {
        captcha_token = await executeRecaptcha('storeOnboarding');
      } catch (error) {
        console.error("Error getting reCAPTCHA token:", error);
        setSubmitError("Failed to get reCAPTCHA token. Please try again.");
        setLoading(false);
        return;
      }
      if (!captcha_token) {
        console.error("reCAPTCHA token not received.");
        setSubmitError("reCAPTCHA verification failed. Please try again.");
        setLoading(false);
        return;
      }

      console.log('Submitting data:', data);
      console.log('With reCAPTCHA Token:', captcha_token);

      const storeId = uuidv4();
      const finalData: OnboardingData = {
        store: {
          ...data.store,
          email: data.store.email || data.user.email,
          currency: data.store.currency || 'USD',
          opening_hours: data.store.opening_hours || '09:00-17:00',
          city: data.store.city || '',
          state: data.store.state || '',
          street_address: data.store.street_address || '',
          payment_methods: data.store.payment_methods || 'cash',
          notes: data.store.notes || '',
          phone_number: data.store.phone || '',
          address: data.store.street_address || '',
        },
        user: {
          ...data.user,
          store_id: storeId,
        },
        captcha_token,
      };

      console.log('Final data:', finalData);

      const response = await submitOnboardingData(finalData);
      if (response.success) {
        toast.success("Registration Successful! 🎉", {
          description: "Welcome to our platform. Redirecting to login...",
        });
        setTimeout(() => router.push('/auth/login'), 1500);
      } else {
        toast.error("Registration Failed", {
          description: response.message || "An error occurred during registration.",
        });
      }
    } catch (error: any) {
      console.error('Submission error:', error);
      toast.error("Error", {
        description: error.message || "An unexpected error occurred",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  return (
    <div className="min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-3xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Create Your Account
          </h1>
          <p className="mt-2 text-sm text-muted-foreground">
            Get started with your store in just a few steps
          </p>
        </div>

        <FormStepIndicator 
          currentStep={step} 
          totalSteps={totalSteps} 
          steps={["Account Details", "Store Setup"]} 
        />

        <FormProvider {...methods}>
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="user-form"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <UserForm onSubmit={handleUserSubmit} />
              </motion.div>
            )}
            
            {step === 2 && (
              <motion.div
                key="store-form"
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <StoreForm 
                  onSubmit={handleStoreSubmit}
                  onBack={handleBack}
                  loading={loading}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </FormProvider>
      </div>
    </div>
  );
}