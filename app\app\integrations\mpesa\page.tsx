// app/app/integrations/mpesa/page.tsx
"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, Loader2, Settings, ArrowRight } from "lucide-react";
import { getMpesaIntegration, deleteMpesaIntegration } from "@/lib/api/integrations/mpesa/service";
import { MpesaIntegration } from "@/lib/api/integrations/mpesa/models";

export default function MpesaIntegrationPage() {
  const [integration, setIntegration] = useState<MpesaIntegration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    async function loadIntegration() {
      setError(null);
      setIsLoading(true);
      try {
        const data = await getMpesaIntegration();
        setIntegration(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to load M-Pesa integration"));
      } finally {
        setIsLoading(false);
      }
    }
    loadIntegration();
  }, []);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setDeleteError(null);
      if (integration) await deleteMpesaIntegration(integration.id);
      setIntegration(null);
    } catch (err) {
      setDeleteError(err instanceof Error ? err.message : "Failed to delete M-Pesa integration");
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading M-Pesa integration...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Integration</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/integrations">
              <Button variant="link">Return to Integrations</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/integrations" className="hover:text-blue-600">Integrations</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Mpesa</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        <Card className="shadow-md rounded-xl">
          <CardHeader>
            <CardTitle>M-Pesa Integration</CardTitle>
            <CardDescription>
              Manage your M-Pesa payment gateway settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            {deleteError && (
              <Alert variant="error" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Deletion Error</AlertTitle>
                <AlertDescription>{deleteError}</AlertDescription>
              </Alert>
            )}

            {integration ? (
              <div className="space-y-6">
                <Alert variant="success" className="border-green-200 bg-green-50 text-green-800">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertTitle className="font-semibold">M-Pesa is configured</AlertTitle>
                  <AlertDescription>
                    Your store is connected to M-Pesa for payments.
                  </AlertDescription>
                </Alert>

                <div className="border rounded-lg p-4 grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Business Short Code</p>
                    <p className="text-sm text-gray-800">
                      {integration.business_short_code}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Consumer Key</p>
                    <p className="text-sm text-gray-800 break-all">
                      {integration.consumer_key ? '••••••••••••••••••••' : <span className="text-gray-400 italic">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Consumer Secret</p>
                    <p className="text-sm text-gray-800 break-all">
                      {integration.consumer_secret ? '••••••••••••••••••••' : <span className="text-gray-400 italic">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Passkey</p>
                    <p className="text-sm text-gray-800 break-all">
                      {integration.pass_key ? '••••••••••••••••••••' : <span className="text-gray-400 italic">Not Set</span>}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">Callback URL</p>
                    <p className="text-sm text-gray-800 break-all">
                      {integration.callback_url || <span className="text-gray-400 italic">Not specified</span>}
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-center pt-4 gap-4 border-t border-gray-100 mt-6">
                  <Button variant="outline" asChild>
                    <Link href="/app/integrations/mpesa/create">
                      <Settings className="h-4 w-4 mr-2" />
                      Edit Configuration
                    </Link>
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="w-full sm:w-auto"
                  >
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : null}
                    {isDeleting ? "Removing..." : "Remove Integration"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center text-center space-y-6 py-8">
                <Alert variant="default" className="w-full max-w-md border-blue-200 bg-blue-50 text-blue-800">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="font-semibold">Not Configured</AlertTitle>
                  <AlertDescription>
                    M-Pesa integration is not yet set up for your store.
                  </AlertDescription>
                </Alert>
                <Button asChild size="lg">
                  <Link href="/app/integrations/mpesa/create">
                    Set Up M-Pesa Integration
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
          {integration && (
            <CardFooter className="bg-gray-50 border-t py-3">
              <p className="text-xs text-gray-500">
                Last updated: {new Date(integration.updated_at || integration.created_at || "").toLocaleString()}
              </p>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}