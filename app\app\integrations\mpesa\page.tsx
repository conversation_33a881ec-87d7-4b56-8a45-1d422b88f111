// app/app/integrations/mpesa/page.tsx
"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, Loader2, Settings, ArrowRight } from "lucide-react";
import { getMpesaIntegration, deleteMpesaIntegration } from "@/lib/api/integrations/mpesa/service";
import { MpesaIntegration } from "@/lib/api/integrations/mpesa/models";

export default function MpesaIntegrationPage() {
  const [integration, setIntegration] = useState<MpesaIntegration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    async function loadIntegration() {
      try {
        const data = await getMpesaIntegration();
        setIntegration(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load M-Pesa integration");
      } finally {
        setIsLoading(false);
      }
    }
    loadIntegration();
  }, []);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);
      await deleteMpesaIntegration();
      setIntegration(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete M-Pesa integration");
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>M-Pesa Integration</CardTitle>
          <CardDescription>
            Manage your M-Pesa payment gateway settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="error" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {integration ? (
            <div className="space-y-6">
              <Alert>
                <CheckCircle2 className="h-4 w-4" />
                <AlertTitle>M-Pesa is configured</AlertTitle>
                <AlertDescription>
                  Your store is connected to M-Pesa for payments.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Business Short Code</p>
                  <p className="text-sm text-muted-foreground">
                    {integration.business_short_code}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Callback URL</p>
                  <p className="text-sm text-muted-foreground">
                    {integration.callback_url || "Not specified"}
                  </p>
                </div>
              </div>

              <div className="flex justify-between pt-4">
                <Button variant="outline" asChild>
                  <Link href="/app/integrations/mpesa/create">
                    <Settings className="h-4 w-4 mr-2" />
                    Edit Configuration
                  </Link>
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    "Remove Integration"
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-6">
              <Alert variant="default">
                <AlertDescription>
                  M-Pesa integration is not configured for your store.
                </AlertDescription>
              </Alert>
              <Button asChild>
                <Link href="/app/integrations/mpesa/create">
                  Set Up M-Pesa Integration
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            {integration
              ? "Last updated: " + new Date(integration.updated_at || integration.created_at || "").toLocaleString()
              : "Configure M-Pesa to enable mobile payments"}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}