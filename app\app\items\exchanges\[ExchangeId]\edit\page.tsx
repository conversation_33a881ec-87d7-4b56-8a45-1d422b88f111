// "use client";
//
// import * as React from "react";
// import {
//   Card,
//   CardHeader,
//   CardTitle,
//   CardDescription,
//   CardContent,
//   CardFooter,
// } from "@/components/ui/card";
// import { Label } from "@/components/ui/label";
// import { Input } from "@/components/ui/input";
// import { Button } from "@/components/ui/button";
// import { Textarea } from "@/components/ui/textarea";
// import { toast } from "@/hooks/use-toast";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
//
// const dummyItems = [
//   { id: "ITEM001", name: "T-Shirt - Black", price: 19.99 },
//   { id: "ITEM002", name: "Jeans - Blue", price: 49.99 },
//   { id: "ITEM003", name: "<PERSON><PERSON><PERSON> <PERSON> <PERSON>", price: 79.99 },
//   { id: "ITEM004", name: "<PERSON><PERSON> <PERSON> <PERSON>", price: 39.99 },
//   { id: "ITEM005", name: "Cap - Navy", price: 24.99 },
// ];
//
// interface ExchangeSchema {
//   original_sale_id: string;
//   original_item_id: string;
//   quantity_exchanged: number;
//   exchange_reason?: string;
//   exchanged_with_item_id?: string;
//   exchange_value?: number;
//   receipt_number: string;
//   comments?: string;
// }
//
// export default function ExchangeForm() {
//   const [loading, setLoading] = React.useState(false);
//   const [formData, setFormData] = React.useState<ExchangeSchema>({
//     original_sale_id: "",
//     original_item_id: "",
//     quantity_exchanged: 1,
//     exchange_reason: "",
//     exchanged_with_item_id: "",
//     exchange_value: 0,
//     receipt_number: "",
//     comments: "",
//   });
//
//   const handleInputChange = (
//     e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
//   ) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]:
//         name === "quantity_exchanged" || name === "exchange_value"
//           ? parseFloat(value) || 0
//           : value,
//     }));
//   };
//
//   const handleSelectChange = (value: string, name: string) => {
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }));
//
//     if (name === "exchanged_with_item_id" && formData.original_item_id) {
//       updateExchangeValue(formData.original_item_id, value);
//     } else if (name === "original_item_id" && formData.exchanged_with_item_id) {
//       updateExchangeValue(value, formData.exchanged_with_item_id);
//     }
//   };
//
//   const updateExchangeValue = (originalId: string, newId: string) => {
//     const originalItem = dummyItems.find((item) => item.id === originalId);
//     const newItem = dummyItems.find((item) => item.id === newId);
//
//     if (originalItem && newItem) {
//       const difference = newItem.price - originalItem.price;
//       setFormData((prev) => ({
//         ...prev,
//         exchange_value: Number(difference.toFixed(2)),
//       }));
//     }
//   };
//
//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setLoading(true);
//
//     try {
//       console.log("Submitting exchange:", formData);
//       toast({
//         title: "Exchange processed",
//         description: `Exchange receipt: ${formData.receipt_number}`,
//       });
//     } catch (error) {
//       toast({
//         title: "Error",
//         description: `Failed to process exchange. Please try again. ${error}`,
//         variant: "destructive",
//       });
//     } finally {
//       setLoading(false);
//     }
//   };
//
//   return (
//     <Card className="w-full max-w-2xl mx-auto">
//       <CardHeader>
//         <CardTitle>Product Exchange</CardTitle>
//         <CardDescription>
//           Process a product exchange for returned items
//         </CardDescription>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit} className="space-y-4">
//           <div className="grid grid-cols-2 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="receipt_number">Receipt Number *</Label>
//               <Input
//                 required
//                 id="receipt_number"
//                 name="receipt_number"
//                 value={formData.receipt_number}
//                 onChange={handleInputChange}
//                 placeholder="Enter receipt number"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="original_sale_id">Original Sale ID *</Label>
//               <Input
//                 required
//                 id="original_sale_id"
//                 name="original_sale_id"
//                 value={formData.original_sale_id}
//                 onChange={handleInputChange}
//                 placeholder="Enter original sale ID"
//               />
//             </div>
//           </div>
//
//           <div className="grid grid-cols-2 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="original_item_id">Original Item *</Label>
//               <Select
//                 value={formData.original_item_id}
//                 onValueChange={(value) =>
//                   handleSelectChange(value, "original_item_id")
//                 }
//               >
//                 <SelectTrigger>
//                   <SelectValue placeholder="Select original item" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   {dummyItems.map((item) => (
//                     <SelectItem key={item.id} value={item.id}>
//                       {item.name} - ${item.price}
//                     </SelectItem>
//                   ))}
//                 </SelectContent>
//               </Select>
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="exchanged_with_item_id">Exchange With Item</Label>
//               <Select
//                 value={formData.exchanged_with_item_id}
//                 onValueChange={(value) =>
//                   handleSelectChange(value, "exchanged_with_item_id")
//                 }
//               >
//                 <SelectTrigger>
//                   <SelectValue placeholder="Select new item" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   {dummyItems.map((item) => (
//                     <SelectItem key={item.id} value={item.id}>
//                       {item.name} - ${item.price}
//                     </SelectItem>
//                   ))}
//                 </SelectContent>
//               </Select>
//             </div>
//           </div>
//
//           <div className="grid grid-cols-2 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="quantity_exchanged">Quantity *</Label>
//               <Input
//                 required
//                 type="number"
//                 min="1"
//                 id="quantity_exchanged"
//                 name="quantity_exchanged"
//                 value={formData.quantity_exchanged}
//                 onChange={handleInputChange}
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="exchange_value">Price Difference</Label>
//               <Input
//                 type="number"
//                 step="0.01"
//                 id="exchange_value"
//                 name="exchange_value"
//                 value={formData.exchange_value}
//                 onChange={handleInputChange}
//                 placeholder="0.00"
//                 disabled
//               />
//             </div>
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="exchange_reason">Exchange Reason</Label>
//             <Textarea
//               id="exchange_reason"
//               name="exchange_reason"
//               value={formData.exchange_reason}
//               onChange={handleInputChange}
//               placeholder="Enter reason for exchange"
//               className="min-h-[80px]"
//             />
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="comments">Additional Comments</Label>
//             <Textarea
//               id="comments"
//               name="comments"
//               value={formData.comments}
//               onChange={handleInputChange}
//               placeholder="Add any additional comments"
//               className="min-h-[80px]"
//             />
//           </div>
//         </form>
//       </CardContent>
//       <CardFooter className="flex justify-end space-x-4">
//         <Button
//           variant="outline"
//           onClick={() =>
//             setFormData({
//               original_sale_id: "",
//               original_item_id: "",
//               quantity_exchanged: 1,
//               exchange_reason: "",
//               exchanged_with_item_id: "",
//               exchange_value: 0,
//               receipt_number: "",
//               comments: "",
//             })
//           }
//         >
//           Clear
//         </Button>
//         <Button onClick={handleSubmit} disabled={loading}>
//           {loading ? "Processing..." : "Process Exchange"}
//         </Button>
//       </CardFooter>
//     </Card>
//   );
// }

"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ExchangeForm } from "@/app/app/items/exchanges/ExchangeForm";
import { Item } from "@/lib/api/items/models";
import { fetchItems } from "@/lib/api/items/service";
import { getExchange, updateExchange } from "@/lib/api/items/exchanges/service";
import { ExchangeSchema } from "@/lib/api/items/exchanges/models";

export default function EditExchangePage({ params }: { params: { ExchangeId: string } }) {
  const [items, setItems] = useState<Item[]>([]);
  const [initialData, setInitialData] = useState<ExchangeSchema | null>(null);


  const token = Cookies.get("token");
  const store_id = Cookies.get("store_id");

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const [items, exchange] = await Promise.all([
            fetchItems(token, store_id),
            getExchange(token, store_id, params.ExchangeId),
          ]);
          setItems(items);
          setInitialData(exchange);
        } catch (error) {
          console.error("Failed to fetch data:", error);
        }
      }
    };
    fetchData();
  }, [token, store_id, params.ExchangeId]);

  const handleSubmit = async (data: ExchangeSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await updateExchange(token, store_id, params.ExchangeId, data);
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <ExchangeForm
      initialData={initialData}
      onSubmit={handleSubmit}
      items={items}
      isEditing={true}
    />
  );
}
