"use client";

import { useRouter } from "next/navigation";
import { StaffForm } from "@/app/app/users/StaffForm";
import { NewStaff } from "@/lib/api/users/models";
import { create_user  } from "@/lib/api/users/service";
import Cookies from "js-cookie";

export default function CreateStaffPage() {
  const router = useRouter();
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  const handleSubmit = async (data: NewStaff) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    data.profile = "https://api.dicebear.com/5.x/initials/svg?seed=${data.name}";

    await create_user(token, store_id, data);
    router.push("/app/users");
  };

  const handleCancel = () => {
    router.back();
  };

  return <StaffForm onSubmit={handleSubmit} onCancel={handleCancel} />;
}
