"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectContent,
} from "@/components/ui/select";
import { NewStaff } from "@/lib/api/users/models";
import { UserDefinedRoles } from "@/lib/types/user_roles";
import { useState } from "react";

interface StaffFormProps {
  initialData?: NewStaff;
  onSubmit(data: NewStaff): Promise<void>;
  isEditing?: boolean;
  onCancel?: () => void;
}

export function StaffForm({
  initialData,
  onSubmit,
  isEditing = false,
  onCancel,
}: StaffFormProps) {
  const [loading, setLoading] = useState(false);
  // const [errors, setErrors] = useState<Partial<Record<keyof NewStaff, string>>>({});
  const [errors, setErrors] = useState<Partial<Record<keyof NewStaff | "submit", string>>>({});


  const [formData, setFormData] = useState<NewStaff>(
    initialData || {
      name: "",
      email: "",
      user_defined_role_id: UserDefinedRoles.Staff,
      profile: "",
    }
  );

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof NewStaff, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Invalid email format";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Failed to submit staff form:", error);
      setErrors({ submit: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  const getRoleName = (roleId: number): string => {
    return (
      Object.keys(UserDefinedRoles).find(
        (key) => UserDefinedRoles[key as keyof typeof UserDefinedRoles] === roleId
      ) || ""
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Staff" : "Create Staff"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Update staff member information"
            : "Add a new staff member to your team"}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter staff name"
              aria-invalid={!!errors.name}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter staff email"
              aria-invalid={!!errors.email}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">User Defined Role</Label>
            <Select
              value={formData.user_defined_role_id ? formData.user_defined_role_id.toString() : ""}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  user_defined_role_id: Number(value),
                }))
              }
            >
              <SelectTrigger id="role">
                <SelectValue placeholder="Select Role">
                  {getRoleName(formData.user_defined_role_id  || UserDefinedRoles.Staff)}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(UserDefinedRoles)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([role, value]) => (
                    <SelectItem key={role} value={value.toString()}>
                      {role}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {errors.submit && (
            <p className="text-sm text-red-500">{errors.submit}</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} type="button">
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading
              ? isEditing
                ? "Updating..."
                : "Creating..."
              : isEditing
              ? "Update"
              : "Create"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
