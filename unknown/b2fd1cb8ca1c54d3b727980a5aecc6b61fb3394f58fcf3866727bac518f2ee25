"use client";

import * as React from "react";
import { Check, ChevronsUpDown, GalleryVerticalEnd } from "lucide-react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { getStores } from "@/lib/api/retailStores/service";

export function StoreSwitcher({
  versions,
  defaultVersion,
}: {
  versions: string[];
  defaultVersion: string;
}) {
  const [selectedStore, setSelectedStore] = React.useState(
    () => Cookies.get("active_store") || defaultVersion,
  );
  const router = useRouter();
  React.useEffect(() => {
    const initializeStore = async () => {
      const activeStoreId = Cookies.get("active_store");
      if (activeStoreId) {
        const token = Cookies.get("auth_token")!;
        const stores = await getStores(token);

        const storeName = stores.find((s) => s.id === activeStoreId)?.name;
        if (storeName) {
          setSelectedStore(storeName);
        }
      }
    };
    initializeStore();
  }, []);

  const handleStoreSelect = async (store: string) => {
    setSelectedStore(store);
    const token = Cookies.get("auth_token")!.toString();
    const stores = await getStores(token);

    const selectedStoreId = stores.find((s) => s.name === store)?.id;
    if (selectedStoreId) {
      Cookies.set("active_store", selectedStoreId);
      router.refresh();
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-semibold">StoreYako</span>
                <span className="">{selectedStore}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width]"
            align="start"
          >
            {versions.map((version) => (
              <DropdownMenuItem
                key={version}
                onSelect={() => handleStoreSelect(version)}
              >
                {version}{" "}
                {version === selectedStore && <Check className="ml-auto" />}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
