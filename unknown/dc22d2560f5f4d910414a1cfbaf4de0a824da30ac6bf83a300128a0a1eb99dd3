import React from 'react';
import { Line } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import { EmployeePerformance } from '@/lib/api/reports/models';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface EmployeeSalesPerformanceProps {
    employeeData: EmployeePerformance[] | null;
}

const EmployeeSalesPerformance = ({ employeeData }: EmployeeSalesPerformanceProps) => {

    if (!employeeData) {
        return <div>Loading... or Error</div>;
    }

    const labels = employeeData.map(employee => employee.employee_name);
    const salesData = employeeData.map(employee => employee.total_sales || 0);

    const chartData = {
        labels: labels,
        datasets: [
            {
                label: 'Total Sales',
                data: salesData,
                borderColor: 'rgb(53, 162, 235)',
                backgroundColor: 'rgba(53, 162, 235, 0.5)',
            },
        ],
    };

    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: 'Employee Sales Performance',
            },
        },
    };

    return (
        <div>
            <h2 className={"text-lg font-semibold mb-4"}>Employee Sales Performance</h2>
            <Line options={options} data={chartData} />
        </div>
    );
};

export default EmployeeSalesPerformance;