import { BASE_URL } from "@/app/configs/constants";
import { Customer, NewCustomerSchema } from "./models";

export async function fetchCustomers(
  authToken: string,
  store_id: string,
): Promise<Customer[]> {
  const url = `${BASE_URL}/customers/${store_id}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch customers: ${errorMessage.error}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching customers:", error);
    throw error;
  }
}

export async function createCustomer(
  authToken: string,
  store_id: string,
  customer: NewCustomerSchema,
): Promise<Customer> {
  const url = `${BASE_URL}/customers/${store_id}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(customer),
    });

    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create customer: ${errorMessage.error}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating customer:", error);
    throw error;
  }
}

export async function deleteCustomer(
  authToken: string,
  store_id: string,
  customer_id: string,
): Promise<void> {
  const url = `${BASE_URL}/customers/${store_id}/${customer_id}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (response.status != 204) {
      const errorMessage = await response.json();
      throw new Error(`Failed to delete customer: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error deleting customer:", error);
    throw error;
  }
}
