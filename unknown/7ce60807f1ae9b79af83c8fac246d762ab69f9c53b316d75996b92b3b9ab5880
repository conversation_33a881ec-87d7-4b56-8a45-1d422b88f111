import { BASE_URL } from "@/app/configs/constants";
import { Returns, ReturnSchema } from "./models";

export async function fetchReturns(
  token: string,
  store_id: string,
): Promise<Returns[]> {
  const url = `${BASE_URL}/items/returns/${store_id}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    if (!response.ok) {
      throw new Error("Error fetching returns");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function createReturn(
  authToken: string,
  store_id: string,
  payload: ReturnSchema,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${store_id}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (response.status != 201) {
      throw new Error("Error creating return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function getReturn(
  authToken: string,
  store_id: string,
  return_id: string,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${store_id}/${return_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      throw new Error("Error fetching return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function updateReturn(
  authToken: string,
  store_id: string,
  return_id: string,
  payload: ReturnSchema,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${store_id}/${return_id}`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (response.status != 200) {
      throw new Error("Error updating return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function deleteReturn(
  authToken: string,
  store_id: string,
  return_id: string,
): Promise<void> {
  const url = `${BASE_URL}/items/returns/${store_id}/${return_id}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (response.status != 204) {
      throw new Error("Error deleting return");
    }
  } catch (error) {
    throw error;
  }
}
