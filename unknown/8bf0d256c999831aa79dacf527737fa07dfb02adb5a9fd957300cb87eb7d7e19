"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "motion/react"

export default function Loading() {
    return (
        <div className="flex flex-col space-y-6 p-6">
            <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex justify-between items-center"
            >
                <Skeleton className="w-[120px] h-[36px] rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200" />
            </motion.div>

            <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="space-y-4"
            >
                <Skeleton className="w-full h-[20px] rounded-md bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200" />

                <div className="space-y-3">
                    {[...Array(5)].map((_, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                        >
                            <Skeleton className="w-full h-[40px] rounded-lg bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200" />
                        </motion.div>
                    ))}
                </div>
            </motion.div>

            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1 }}
                className="absolute inset-0 bg-gradient-to-br from-transparent via-gray-50 to-transparent opacity-30"
            />
        </div>
    );
}