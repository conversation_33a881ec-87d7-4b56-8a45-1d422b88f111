export interface Attributes {
  id: string,
  name: string,
  inherited: boolean,
  store_id?: string,
  system_defined: boolean,
  date_created: string,
  date_updated?: string,
}

export interface AttributeValues {
  id: string,
  attribute_id: string,
  value: string,
  store_id?: string,
  date_created: string,
  date_updated?: string,
}

export interface ItemAttributesValues {
  id: string,
  item_id: string,
  attribute_value_id: string,
  position: number,
  store_id: string,
  date_created: string,
  date_updated?: string,
}

export interface MatrixAttributesValues {
  id: string,
  item_matrix_id: string,
  attribute_value_id: string,
  position: number,
  store_id: string,
  date_created: string,
  date_updated?: string,
}


export interface AttributePayload {
  name: string,
}


export interface AttributeValuesSchema {
  values: string[]
}
