//app/app/items/matrices/create/AttributesCard.tsx
import React, { useState } from "react";
import { X, GripVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";

interface AttributeCardProps {
  attr: {
    attribute: Attributes;
    values: AttributeValues[];
    selectedValues: string[];
  };
  onRemoveAttribute: (attributeId: string) => void;
  onToggleValue: (attributeId: string, valueId: string) => void;
  onAddValue?: (attributeId: string, value: string) => void;
  onRemoveValue?: (attributeId: string, valueId: string) => void;
  onAddBulkValues?: (
    attributeId: string,
    values: AttributeValuesSchema,
  ) => Promise<void>;
}

const AttributeCard: React.FC<AttributeCardProps> = ({
  attr,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onRemoveValue,
  onAddBulkValues,
}) => {
  const [isValuesDialogOpen, setIsValuesDialogOpen] = useState(false);
  const [bulkValues, setBulkValues] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleAddValueClick = () => {
    setIsValuesDialogOpen(true);
  };

  const handleAddBulkValues = async () => {
    if (!bulkValues.trim() || !onAddBulkValues) return;

    try {
      setIsLoading(true);
      const values = bulkValues
        .split(",")
        .map((v) => v.trim())
        .filter((v) => v.length > 0);

      const payload: AttributeValuesSchema = {
        values: values,
      };

      await onAddBulkValues(attr.attribute.id, payload);
      setBulkValues("");
      setIsValuesDialogOpen(false);
    } catch (error) {
      console.error("Error adding bulk values:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Dialog
        open={isValuesDialogOpen}
        onOpenChange={(open) => {
          setIsValuesDialogOpen(open);
          if (!open) setBulkValues("");
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Values for {attr.attribute.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="bulkValues">Bulk Values (comma separated)</Label>
              <Textarea
                id="bulkValues"
                value={bulkValues}
                onChange={(e) => setBulkValues(e.target.value)}
                placeholder="e.g. S, M, L, XL, XXL"
                rows={4}
              />
              <p className="text-sm text-gray-500">
                Enter multiple values separated by commas
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsValuesDialogOpen(false);
                setBulkValues("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddBulkValues}
              disabled={!bulkValues.trim() || isLoading}
            >
              {isLoading ? "Adding..." : "Add Bulk Values"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <GripVertical
                className="w-4 h-4 text-gray-400"
                aria-hidden="true"
              />
              <h4 className="font-medium">{attr.attribute.name}</h4>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => onRemoveAttribute(attr.attribute.id)}
              aria-label={`Remove ${attr.attribute.name}`}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center justify-between gap-2 mt-2">
            <div className="flex flex-wrap gap-2">
              {attr.values.map((value) => (
                <Badge
                  key={value.id}
                  variant={
                    attr.selectedValues.includes(value.id)
                      ? "default"
                      : "secondary"
                  }
                  className="cursor-pointer flex items-center gap-1"
                  onClick={() => onToggleValue(attr.attribute.id, value.id)}
                  aria-label={`Toggle value ${value.value}`}
                >
                  {value.value}
                  {onRemoveValue && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={(e) => {
                        e.stopPropagation();
                        onRemoveValue(attr.attribute.id, value.id);
                      }}
                      aria-label={`Remove value ${value.value}`}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </Badge>
              ))}
            </div>

            {onAddValue && (
              <Button
                type="button"
                size="sm"
                onClick={handleAddValueClick}
                aria-label="Add value"
              >
                Add Value
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default AttributeCard;
