import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { LowStockItem } from '@/lib/api/reports/models';


ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface InventoryOverviewProps {
    inventoryData: LowStockItem[] | null;
}

const InventoryOverview = ({ inventoryData }: InventoryOverviewProps) => {
  if (!inventoryData) {
    return <div>Loading... or Error</div>;
  }

  const labels = inventoryData.map(item => item.item_name);
  const quantityData = inventoryData.map(item => item.quantity);

  const barChartData = {
    labels: labels,
    datasets: [
      {
        label: 'Quantity',
        data: quantityData,
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Inventory Levels',
      },
    },
  };

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-lg font-semibold mb-4">Inventory Management</h2>
      <div className="mb-6 flex-grow">
        <h3 className="text-md font-medium mb-2">Low Stock Items</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  ID
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  Name
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  Quantity
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:bg-gray-800">
              {inventoryData.map((item) => (
                <tr key={item.item_id}>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.item_id}</td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.item_name}</td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.quantity}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="flex-grow">
        {barChartData && <Bar options={barChartOptions} data={barChartData} />}
      </div>
    </div>
  );
};

export default InventoryOverview;