"use client";

import React from 'react';
import { Invoice, PaymentStatus } from '@/lib/api/invoices/models';
import { Store } from '@/lib/api/retailStores/models';
import { PDFDownloadLink } from '@react-pdf/renderer';
import InvoicePdf from './InvoiceComponent';
import { Button } from '@/components/ui/button';


interface InvoiceTemplateProps {
  invoice: Invoice;
  store: Store;
  customerName?: string;
  email?: string;
  onPrint?: () => void;
}

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

const InvoiceTemplate: React.FC<InvoiceTemplateProps> = ({
  invoice,
  store,
  customerName = 'Customer',
  email,
  onPrint
}: {
  invoice: Invoice;
  store: Store;
  customerName?: string;
  email?: string;
  onPrint?: () => void;
}) => {
  // Calculate tax amount if applicable
  const taxAmount = invoice.has_tax ? invoice.subtotal * invoice.tax_amount / 100 : 0;

  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded shadow-sm my-6 print:shadow-none" id="invoice">
      <div className="grid grid-cols-2 items-center">
        <div>
          <div className="h-20 w-20 bg-gray-200 flex items-center justify-center rounded">
            <span className="text-gray-500">{store.name.charAt(0)}</span>
          </div>
        </div>

        <div className="text-right">
          <p className="font-bold text-lg">{store.name}</p>
          <p className="text-gray-500 text-sm">{store.email || ''}</p>
          <p className="text-gray-500 text-sm mt-1">{store.phone_number || ''}</p>
          <p className="text-gray-500 text-sm mt-1">{store.address || ''}</p>
          {store.city && store.postal_code && (
            <p className="text-gray-500 text-sm">
              {store.city}, {store.state || ''} {store.postal_code}
            </p>
          )}
        </div>
      </div>

      <div className="mt-4">
        <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold ${invoice.payment_status === PaymentStatus.Paid
          ? 'bg-green-100 text-green-800'
          : 'bg-yellow-100 text-yellow-800'
          }`}>
          {invoice.payment_status}
        </span>
      </div>

      <div className="grid grid-cols-2 items-center mt-8">
        <div>
          <p className="text-sm">
            Bill to: {customerName}
          </p>
          {email && (
            <p className="text-gray-500">
              {email}
            </p>
          )}
          <p className="text-gray-500 text-sm">
            Customer ID: {invoice.customer_id}
          </p>
        </div>

        <div className="text-right">
          <p className="font-bold text-2xl text-gray-800 mb-4">INVOICE</p>
          <p className="text-sm">
            Invoice number:
            <span className="text-gray-500 ml-2 text-sm">{invoice.invoice_number}</span>
          </p>
          <p className="text-sm">
            Invoice date: <span className="text-gray-500 text-sm">{invoice.invoice_date}</span>
          </p>
          {invoice.due_date && (
            <p className="text-sm">
              Due date: <span className="text-gray-500 text-sm">{invoice.due_date}</span>
            </p>
          )}
          {invoice.payment_date && invoice.payment_status === PaymentStatus.Paid && (
            <p>
              Paid on: <span className="text-gray-500">{invoice.payment_date}</span>
            </p>
          )}
        </div>
      </div>

      <div className="-mx-4 mt-8 flow-root sm:mx-0">
        <table className="min-w-full">
          <colgroup>
            <col className="w-full sm:w-1/2" />
            <col className="sm:w-1/6" />
            <col className="sm:w-1/6" />
            <col className="sm:w-1/6" />
          </colgroup>
          <thead className="border-b border-gray-300 text-gray-900">
            <tr>
              <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Items</th>
              <th scope="col" className="hidden px-3 py-3.5 text-right text-sm font-semibold text-gray-900 sm:table-cell">Quantity</th>
              <th scope="col" className="hidden px-3 py-3.5 text-right text-sm font-semibold text-gray-900 sm:table-cell">Price</th>
              <th scope="col" className="py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-0">Amount</th>
            </tr>
          </thead>
          <tbody>
            {invoice.items.map((item, index) => (
              <tr key={item.id || index} className="border-b border-gray-200">
                <td className="max-w-0 py-5 pl-4 pr-3 text-sm sm:pl-0">
                  <div className="font-medium text-gray-900">{item.item_name}</div>
                </td>
                <td className="hidden px-3 py-5 text-right text-sm text-gray-500 sm:table-cell">{item.quantity}</td>
                <td className="hidden px-3 py-5 text-right text-sm text-gray-500 sm:table-cell">
                  {formatCurrency(item.price, store.currency)}
                </td>
                <td className="py-5 pl-3 pr-4 text-right text-sm text-gray-500 sm:pr-0">
                  {formatCurrency(item.price * item.quantity, store.currency)}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr>
              <th scope="row" colSpan={3} className="hidden pl-4 pr-3 pt-6 text-right text-sm font-normal text-gray-500 sm:table-cell sm:pl-0">Subtotal</th>
              <th scope="row" className="pl-6 pr-3 pt-6 text-left text-sm font-normal text-gray-500 sm:hidden">Subtotal</th>
              <td className="pl-3 pr-6 pt-6 text-right text-sm text-gray-500 sm:pr-0">
                {formatCurrency(invoice.subtotal, store.currency)}
              </td>
            </tr>
            {invoice.has_tax && (
              <tr>
                <th scope="row" colSpan={3} className="hidden pl-4 pr-3 pt-4 text-right text-sm font-normal text-gray-500 sm:table-cell sm:pl-0">
                  Tax ({invoice.tax_amount}%)
                </th>
                <th scope="row" className="pl-6 pr-3 pt-4 text-left text-sm font-normal text-gray-500 sm:hidden">
                  Tax ({invoice.tax_amount}%)
                </th>
                <td className="pl-3 pr-6 pt-4 text-right text-sm text-gray-500 sm:pr-0">
                  {formatCurrency(taxAmount, store.currency)}
                </td>
              </tr>
            )}
            <tr>
              <th scope="row" colSpan={3} className="hidden pl-4 pr-3 pt-4 text-right text-sm font-semibold text-gray-900 sm:table-cell sm:pl-0">Total</th>
              <th scope="row" className="pl-6 pr-3 pt-4 text-left text-sm font-semibold text-gray-900 sm:hidden">Total</th>
              <td className="pl-3 pr-4 pt-4 text-right text-sm font-semibold text-gray-900 sm:pr-0">
                {formatCurrency(invoice.total_amount, store.currency)}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      {(invoice.notes || invoice.terms) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
          {invoice.notes && (
            <div>
              <h3 className="font-bold text-gray-700 mb-2">Notes</h3>
              <p className="text-sm text-gray-600">{invoice.notes}</p>
            </div>
          )}
          {invoice.terms && (
            <div>
              <h3 className="font-bold text-gray-700 mb-2">Terms</h3>
              <p className="text-sm text-gray-600">{invoice.terms}</p>
            </div>
          )}
        </div>
      )}

      <div className="border-t-2 pt-4 text-xs text-gray-500 text-center mt-16">
        <p>Thank you for your business!</p>
        {invoice.payment_status === PaymentStatus.Unpaid && (
          <p className="mt-2">Please pay this invoice by the due date.</p>
        )}
      </div>

      <div className="mt-8 flex justify-end print:hidden">
        <PDFDownloadLink document={<InvoicePdf invoice={invoice} store={store} customerName={customerName} />} fileName="invoice.pdf">
          {/*({ loading }) => (loading ? 'Loading document...' : 'Download Invoice')} */}
          <Button onClick={() => { }}>Download Invoice</Button>
        </PDFDownloadLink>
      </div>

    </div >
  );
};

export default InvoiceTemplate;
