// "use client";
//
// import { useState } from 'react';
// import { useRouter } from 'next/router';
// import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import { Button } from '@/components/ui/button';
// import { Label } from '@/components/ui/label';
// import { Textarea } from '@/components/ui/textarea';
// import { type SupplierSchema } from '@/lib/api/suppliers/models';
// import { createSupplier } from '@/lib/api/suppliers/service';
// import Cookies from 'js-cookie';
// import { AlertError } from '@/components/errors';
// import { AlertSuccess } from '@/components/success';
//
// export default function SupplierForm() {
//   const router = useRouter();
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState<string | null>(null);
//   const [success, setSuccess] = useState(false);
//
//   const token = Cookies.get('token');
//   const store_id = Cookies.get('store_id');
//
//   const [formData, setFormData] = useState<SupplierSchema>({
//     name: '',
//     phone_number: '',
//     address: '',
//     city: '',
//     state: '',
//     country: '',
//     postal_code: '',
//     contact_person: '',
//     notes: '',
//   });
//
//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setLoading(true);
//     setError(null);
//     setSuccess(false);
//
//     try {
//       if (!formData.name || !formData.phone_number) {
//         throw new Error('Name and phone number are required');
//       }
//
//       if (!token || !store_id) {
//         throw new Error("Authentication token or store ID is missing.");
//       }
//
//       await createSupplier(token, store_id, formData);
//
//       setSuccess(true);
//
//       setFormData({
//         name: '',
//         phone_number: '',
//         address: '',
//         city: '',
//         state: '',
//         country: '',
//         postal_code: '',
//         contact_person: '',
//         notes: '',
//       });
//
//       router.push('/app/suppliers');
//     } catch (error) {
//       setError((error as Error).message);
//     } finally {
//       setLoading(false);
//     }
//   };
//
//   return (
//     <Card className="w-full max-w-2xl">
//       {error && <AlertError message={error} />}
//       {success && <AlertSuccess message="Supplier created successfully" />}
//       <CardHeader>
//         <CardTitle>Add New Supplier</CardTitle>
//       </CardHeader>
//       <form onSubmit={handleSubmit}>
//         <CardContent className="space-y-4">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="name">Company Name *</Label>
//               <Input
//                 id="name"
//                 required
//                 value={formData.name}
//                 onChange={(e) => setFormData({ ...formData, name: e.target.value })}
//                 placeholder="Enter supplier name"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="phone_number">Phone Number *</Label>
//               <Input
//                 id="phone_number"
//                 required
//                 value={formData.phone_number}
//                 onChange={(e) => setFormData({ ...formData, phone_number: e.target.value })}
//                 placeholder="Enter phone number"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="contact_person">Contact Person</Label>
//               <Input
//                 id="contact_person"
//                 value={formData.contact_person}
//                 onChange={(e) => setFormData({ ...formData, contact_person: e.target.value })}
//                 placeholder="Enter contact person name"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="postal_code">Postal Code</Label>
//               <Input
//                 id="postal_code"
//                 value={formData.postal_code}
//                 onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
//                 placeholder="Enter postal code"
//               />
//             </div>
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="address">Address</Label>
//             <Input
//               id="address"
//               value={formData.address}
//               onChange={(e) => setFormData({ ...formData, address: e.target.value })}
//               placeholder="Enter street address"
//             />
//           </div>
//
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//             <div className="space-y-2">
//               <Label htmlFor="city">City</Label>
//               <Input
//                 id="city"
//                 value={formData.city}
//                 onChange={(e) => setFormData({ ...formData, city: e.target.value })}
//                 placeholder="Enter city"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="state">State</Label>
//               <Input
//                 id="state"
//                 value={formData.state}
//                 onChange={(e) => setFormData({ ...formData, state: e.target.value })}
//                 placeholder="Enter state"
//               />
//             </div>
//
//             <div className="space-y-2">
//               <Label htmlFor="country">Country</Label>
//               <Input
//                 id="country"
//                 value={formData.country}
//                 onChange={(e) => setFormData({ ...formData, country: e.target.value })}
//                 placeholder="Enter country"
//               />
//             </div>
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="notes">Notes</Label>
//             <Textarea
//               id="notes"
//               value={formData.notes}
//               onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
//               placeholder="Enter any additional notes"
//               className="h-24"
//             />
//           </div>
//         </CardContent>
//
//         <CardFooter className="flex justify-end space-x-2">
//           <Button
//             variant="outline"
//             onClick={() => router.back()}
//             type="button"
//           >
//             Cancel
//           </Button>
//           <Button type="submit" disabled={loading}>
//             {loading ? 'Creating...' : 'Create Supplier'}
//           </Button>
//         </CardFooter>
//       </form>
//     </Card>
//   );
// }


"use client";

import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { SupplierForm } from '@/app/app/suppliers/SupplierForm';
import { type SupplierSchema } from '@/lib/api/suppliers/models';
import { createSupplier } from '@/lib/api/suppliers/service';

export default function CreateSupplierPage() {
  const router = useRouter();
  const token = Cookies.get('token');
  const store_id = Cookies.get('store_id');

  const handleSubmit = async (data: SupplierSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await createSupplier(token, store_id, data);
    router.push('/app/suppliers');
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <SupplierForm
      onSubmit={handleSubmit}
      onCancel={handleCancel}
    />
  );
}
