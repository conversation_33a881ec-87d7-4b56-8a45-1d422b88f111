import { BASE_URL } from "@/app/configs/constants";
import { MpesaIntegration, MpesaIntegrationFormValues } from "./models";
import Cookies from "js-cookie";

export async function getMpesaIntegration(): Promise<MpesaIntegration | null> {
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  if (!token || !store_id) {
    throw new Error("Authentication token or store ID is missing.");
  }

  const url = `${BASE_URL}/transactions/mpesa/secrets/${store_id}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch M-Pesa integration: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching M-Pesa integration:", error);
    throw error;
  }
}

export async function saveMpesaIntegration(
  data: MpesaIntegrationFormValues
): Promise<MpesaIntegration> {
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  if (!token || !store_id) {
    throw new Error("Authentication token or store ID is missing.");
  }

  const url = `${BASE_URL}/transactions/mpesa/secrets/${store_id}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to save M-Pesa integration: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error saving M-Pesa integration:", error);
    throw error;
  }
}

export async function updateMpesaIntegration(
  data: MpesaIntegrationFormValues
): Promise<MpesaIntegration> {
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  if (!token || !store_id) {
    throw new Error("Authentication token or store ID is missing.");
  }

  const url = `${BASE_URL}/transactions/mpesa/secrets/${store_id}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to update M-Pesa integration: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating M-Pesa integration:", error);
    throw error;
  }
}

export async function deleteMpesaIntegration(id: string): Promise<void> {
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  if (!token || !store_id) {
    throw new Error("Authentication token or store ID is missing.");
  }

  const url = `${BASE_URL}/transactions/mpesa/secrets/${store_id}/${id}`;
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to delete M-Pesa integration: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error deleting M-Pesa integration:", error);
    throw error;
  }
}