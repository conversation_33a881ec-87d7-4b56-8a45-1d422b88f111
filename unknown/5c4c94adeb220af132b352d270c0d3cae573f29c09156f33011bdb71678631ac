import React from 'react';
import { Line } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';
import Link from 'next/link';
import { DailyStoreAggregatedAverageSales } from '@/lib/api/reports/models';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
);

const labels: string[] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

interface DailyAverageSalesProps {
    dailySales: DailyStoreAggregatedAverageSales | null;
}

const DailyAverageSales = ({ dailySales }: DailyAverageSalesProps) => {
    if (!dailySales) {
        return <div className="p-4">No data available.</div>;
    }

    const salesData: number[] = [
        dailySales.monday || 0,
        dailySales.tuesday || 0,
        dailySales.wednesday || 0,
        dailySales.thursday || 0,
        dailySales.friday || 0,
        dailySales.saturday || 0,
        dailySales.sunday || 0,
    ];

    const totalSales = salesData.reduce((sum, val) => sum + val, 0);
    const daysWithData = salesData.filter(val => val > 0).length;
    const averageSales = daysWithData > 0 ? totalSales / daysWithData : 0;

    const today = new Date().getDay();
    const adjustedDayIndex = today === 0 ? 6 : today - 1;
    const todaySales = salesData[adjustedDayIndex] || 0;
    const percentChange = averageSales ? ((todaySales - averageSales) / averageSales) * 100 : 0;

    const chartData = {
        labels,
        datasets: [
            {
                label: 'Daily Sales',
                data: salesData,
                borderColor: 'rgb(53, 162, 235)',
                backgroundColor: 'rgba(53, 162, 235, 0.5)',
            },
            {
                label: 'Average',
                data: Array(7).fill(averageSales),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderDash: [5, 5],
            },
        ],
    };

    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
        },
        scales: {
            y: { beginAtZero: true },
            x: { display: false },
        },
    };

    return (
        <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="text-lg font-semibold">Daily Average Sales</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
                ${averageSales.toFixed(2)}
            </div>
            <div className={`text-sm ${percentChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {percentChange >= 0 ? '+' : ''}{percentChange.toFixed(2)}% {percentChange >= 0 ? 'above' : 'below'} Average
            </div>
            <div className="h-16 mt-2">
                <Line data={chartData} options={chartOptions} />
            </div>
            <Link href="/reports/sales" className="text-blue-500 hover:underline mt-2 block text-sm">
                View Detailed Report
            </Link>
        </div>
    );
};

export default DailyAverageSales;