import { BASE_URL } from "@/app/configs/constants";
import { Exchange, ExchangeSchema } from "./models";

export async function fetchExchanges(
  authToken: string,
  storeId: string,
): Promise<Exchange[]> {
  const url = `${BASE_URL}/items/exchanges/${storeId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function createExchange(
  authToken: string,
  storeId: string,
  payload: ExchangeSchema,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getExchange(
  authToken: string,
  storeId: string,
  exchangeId: string,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateExchange(
  authToken: string,
  storeId: string,
  exchangeId: string,
  payload: ExchangeSchema,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteExchange(
  authToken: string,
  storeId: string,
  exchangeId: string,
): Promise<void> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Deleting exchanged item failed");
    }
  } catch (error) {
    throw error;
  }
}
