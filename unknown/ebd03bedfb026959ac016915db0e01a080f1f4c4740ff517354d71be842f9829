"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ReturnForm } from "@/app/app/items/returns/ReturnForm";
import { Item } from "@/lib/api/items/models";
import { ReturnSchema } from "@/lib/api/items/returns/models";
import { fetchItems } from "@/lib/api/items/service";
import { getReturn, updateReturn } from "@/lib/api/items/returns/service";
import { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";
import { fetchReceipts } from "@/lib/api/receipts/service";
import { Receipt } from "@/lib/api/receipts/models";
import { getMe } from "@/lib/api/users/service";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function EditReturnPage({ params }: { params: { ReturnId: string } }) {
  const [items, setItems] = useState<Item[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [initialData, setInitialData] = useState<ReturnSchema | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false);

  const router = useRouter();
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      setIsNotFound(false);

      try {
        const [fetchedItems, returnData, fetchedSales, fetchedReceipts, fetchedUser] = await Promise.all([
          fetchItems(token, store_id),
          getReturn(token, store_id, params.ReturnId),
          fetchSales(token, store_id),
          fetchReceipts(token, store_id),
          getMe(token),
        ]);
        setItems(fetchedItems);
        setInitialData(returnData);
        setSales(fetchedSales);
        setReceipts(fetchedReceipts);
        setUserId(fetchedUser.id);
      } catch (err: any) {
        if (err.message === "Return not found") {
          setIsNotFound(true);
        } else {
          setError(err instanceof Error ? err : new Error("Failed to fetch data."))
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [token, store_id, params.ReturnId]);

  const handleSubmit = async (data: ReturnSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    try {
      await updateReturn(token, store_id, params.ReturnId, data);
      router.push("/app/items/returns");
    } catch (error) {
      setError(error instanceof Error ? error : new Error("Error updating return."));
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading return details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Return Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items/returns">
              <Button variant="link">Return to Returns List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Return Not Found</h2>
          <p className="text-gray-600 text-center">The return with ID ({params.ReturnId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items/returns">
              <Button variant="link">Return to Returns List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!initialData || !userId) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Return data could not be prepared for editing.</p>
        <Link href="/app/items/returns" className="ml-4">
          <Button variant="link">Return to Returns List</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/items/returns" className="hover:text-blue-600">Returns</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit Return: {params.ReturnId}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6 flex justify-center">
        <div className="w-full max-w-2xl">
          <ReturnForm
            initialData={initialData}
            onSubmit={handleSubmit}
            items={items}
            sales={sales}
            receipts={receipts}
            isEditing={true}
            userId={userId}
          />
        </div>
      </div>
    </div>
  );
}