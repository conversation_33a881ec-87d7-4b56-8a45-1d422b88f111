"use client";

import { StoreForm } from "@/app/app/stores/StoreForm";
import { StoreSchema } from "@/lib/api/retailStores/models";
import { createStore } from "@/lib/api/retailStores/service";
import Cookies from "js-cookie";

export default function CreateStorePage() {

  const token = Cookies.get("auth_token");

  const handleSubmit = async (data: StoreSchema) => {
    if (!token) {
      throw new Error("Authentication token is missing.");
    }
    await createStore(data, token);
  };

  return <StoreForm onSubmit={handleSubmit} />;
}