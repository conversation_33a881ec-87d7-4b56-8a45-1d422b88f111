"use client";

import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { ItemForm } from "@/app/app/items/ItemForm";
import { Category } from "@/lib/api/categories/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { Brand } from "@/lib/api/brands/models";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { createItems } from "@/lib/api/items/service";
import { ItemSchema } from "@/lib/api/items/models";
import { useRouter } from "next/navigation";

export default function CreateItemPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);

  const router = useRouter();

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    if (!token || !store_id) {
      return;
    }

    const fetchData = async () => {
      try {
        const [categories, suppliers, brands] = await Promise.all([
          fetchCategories(token, store_id),
          fetchSuppliers(token, store_id),
          fetchBrands(token, store_id),
        ]);
        setCategories(categories);
        setSuppliers(suppliers);
        setBrands(brands);

      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    };

    fetchData();
  }, [token, store_id]);

  const handleSubmit = async (data: ItemSchema[]) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await createItems(token, store_id, data);
  };

  return (
    <ItemForm
      onSubmit={handleSubmit}
      categories={categories}
      suppliers={suppliers}
      brands={brands}
    />
  );
}
