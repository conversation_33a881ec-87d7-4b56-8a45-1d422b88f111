import { BASE_URL } from "@/app/configs/constants";
import { InvoiceItem, AddInvoiceItemPayload } from "./models";

export async function fetchInvoiceItems(token: string, store_id: string, invoice_id: string): Promise<InvoiceItem[]> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${store_id}/invoices/${invoice_id}/items`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice items");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function createInvoiceItem(
    token: string, 
    store_id: string, 
    invoice_id: string, 
    payload: AddInvoiceItemPayload
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${store_id}/invoices/${invoice_id}/items`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                credentials: "include",
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to create invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function updateInvoiceItem(
    token: string, 
    store_id: string, 
    invoice_id: string,
    item_id: string,
    payload: Partial<AddInvoiceItemPayload>
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${store_id}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                credentials: "include",
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function deleteInvoiceItem(
    token: string, 
    store_id: string, 
    invoice_id: string,
    item_id: string
): Promise<void> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${store_id}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete invoice item");
        }
    } catch (error) {
        throw error;
    }
}

export async function getInvoiceItemById(
    token: string, 
    store_id: string, 
    invoice_id: string,
    item_id: string
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${store_id}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}