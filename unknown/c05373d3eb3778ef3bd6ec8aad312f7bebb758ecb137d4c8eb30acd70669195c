import { BASE_URL } from "@/app/configs/constants";
import { Receipt } from "./models";

export async function fetchReceipts(authToken: string, storeId: string)
: Promise<Receipt[]>{
  const url = `${BASE_URL}/sales/receipts/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch receipts: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching receipts:", error);
    throw error;
  }
}
