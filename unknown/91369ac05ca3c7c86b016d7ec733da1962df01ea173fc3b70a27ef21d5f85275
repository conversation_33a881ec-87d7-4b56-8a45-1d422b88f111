
import { getStores } from "../retailStores/service";
import { LoginPayload, LoginResponse } from "./models";
import { BASE_URL } from "@/app/configs/constants";

export async function login(payload: LoginPayload): Promise<LoginResponse> {
    try {
        const response = await fetch(`${BASE_URL}/auth/login`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Login failed");
        }
        const token = await response.json();
        await getStores(token);
        return token;
    } catch (error) {
        throw error;
    }
}