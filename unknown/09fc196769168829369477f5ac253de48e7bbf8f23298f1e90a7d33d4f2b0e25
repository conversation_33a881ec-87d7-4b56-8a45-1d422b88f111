// app/error.tsx
'use client';

import Link from 'next/link';
import { cn } from "@/lib/utils";
import { motion } from 'framer-motion';
import { Home, RefreshCw, AlertTriangle, Send, Clock } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function ErrorPage({ error, reset }: { error: Error; reset: () => void }) {
  const [countdown, setCountdown] = useState(10);
  
  // Auto-retry countdown
  useEffect(() => {
    if (countdown <= 0) {
      reset();
      return;
    }
    
    const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    return () => clearTimeout(timer);
  }, [countdown, reset]);

  return (
    <div className="relative min-h-screen w-full overflow-hidden bg-slate-50">
      {/* Simple subtle background */}
      <div className="absolute inset-0 bg-grid-slate-100 opacity-50 pointer-events-none"></div>
      
      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <div className="rounded-lg bg-white border border-slate-100 shadow-xl p-8 text-center">
            {/* Error icon */}
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 rounded-full bg-amber-50 flex items-center justify-center">
                <AlertTriangle className="h-8 w-8 text-amber-500" />
              </div>
            </div>
            
            {/* Error message */}
            <h1 className="text-2xl font-semibold text-slate-800 mb-2">Server Error</h1>
            <p className="text-slate-600 mb-6">
              We're experiencing technical difficulties. Our team has been notified and is working on the issue.
            </p>
            
            {/* Primary actions */}
            <div className="space-y-3 mb-6">
              <button
                onClick={() => reset()}
                className={cn(
                  "w-full py-3 px-4 flex items-center justify-center gap-2 rounded-md",
                  "bg-slate-800 hover:bg-slate-900 text-white",
                  "transition-colors duration-200",
                  "font-medium"
                )}
              >
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
                <span className="ml-1 text-sm opacity-70">({countdown})</span>
              </button>
              
              <Link
                href="/"
                className={cn(
                  "w-full py-3 px-4 flex items-center justify-center gap-2 rounded-md",
                  "bg-white border border-slate-200 hover:border-slate-300 text-slate-700",
                  "transition-colors duration-200",
                  "font-medium"
                )}
              >
                <Home className="h-4 w-4" />
                <span>Return to Home</span>
              </Link>
            </div>
            
            {/* Status information */}
            <div className="border-t border-slate-100 pt-6 text-sm flex justify-between text-slate-500">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-slate-400" />
                <span>Status checked <time dateTime={(new Date()).toISOString()}>just now</time></span>
              </div>
              <div>Error 500</div>
            </div>
          </div>
          
          {/* Optional report section */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="mt-4 p-4 rounded-md bg-white border border-slate-100 shadow-lg"
          >
            <h2 className="text-sm font-medium text-slate-700 mb-2">
              Still having trouble?
            </h2>
            <div className="flex">
              <textarea 
                className="flex-1 p-2 text-sm border border-slate-200 rounded-l-md focus:outline-none focus:ring-1 focus:ring-slate-300" 
                placeholder="Describe what you were trying to do when this error occurred..."
                rows={2}
              ></textarea>
              <button className="bg-slate-100 hover:bg-slate-200 px-3 rounded-r-md border border-l-0 border-slate-200 text-slate-600 transition-colors">
                <Send className="h-4 w-4" />
              </button>
            </div>
            <p className="mt-2 text-xs text-slate-500">
              Your feedback helps us improve our service.
            </p>
          </motion.div>
        </motion.div>
      </div>
      
      {/* Simple styling */}
      <style jsx global>{`
        .bg-grid-slate-100 {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(241 245 249 / 0.25)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
        }
      `}</style>
    </div>
  );
}