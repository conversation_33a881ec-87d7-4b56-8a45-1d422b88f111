export interface Exchange {
  id: string;
  original_sale_id: string;
  original_item_id: string;
  transaction_id?: string;
  store_id: string;
  quantity_exchanged: number;
  exchange_reason?: string;
  exchanged_with_item_id?: string;
  exchange_value?: number;
  timestamp: string;
  receipt_number: string;
  comments?: string;
  staff_id?: string;
}

export interface ExchangeSchema {
  original_sale_id: string;
  original_item_id: string;
  quantity_exchanged: number;
  exchange_reason?: string;
  exchanged_with_item_id?: string;
  exchange_value?: number;
  receipt_number: string;
  comments?: string;
}
