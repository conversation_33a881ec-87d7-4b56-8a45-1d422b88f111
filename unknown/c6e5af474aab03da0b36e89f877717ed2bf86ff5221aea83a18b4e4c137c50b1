// "use client";
//
// import { sampleReceipt } from './dummyData';
// import ReceiptPrinter from './receiptGenerator';
// import { useEffect } from 'react';
//
// const ReceiptPage = () => {
//     useEffect(() => {
//         const handlePrint = async (data: Uint8Array) => {
//             if (typeof navigator !== 'undefined' && 'serial' in navigator) {
//                 try {
//                     // Typescript casting
//                     const serial = (navigator as any).serial;
//                     const port = await serial.requestPort();
//                     await port.open({ baudRate: 9600 });
//
//                     const writer = port.writable.getWriter();
//                     await writer.write(data);
//                     await writer.close();
//                     port.close();
//
//                     console.log("Receipt printed successfully!");
//                 } catch (error) {
//                     console.error("Printing failed:", error);
//                 }
//             } else {
//                 console.warn("Web Serial API is not supported in this browser.");
//             }
//         };
//     }, []);
//
//     return <ReceiptPrinter receipt={sampleReceipt} onPrint={() => { }} />;
// };
//
// export default ReceiptPage;

// app/[ReceiptId]/page.tsx
import { sampleReceipt } from './dummyData';
import Receipt from './receiptGenerator';

const ReceiptPage = () => {
  return (
    <div className="p-4 mx-auto">
      <h1>Receipt</h1>
      <Receipt {...sampleReceipt} />
      </div>
  );
};

export default ReceiptPage;
