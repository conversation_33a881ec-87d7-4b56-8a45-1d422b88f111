import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Exchange } from "@/lib/api/items/exchanges/models";
import { Item } from "@/lib/api/items/models";
import { User } from "@/lib/api/users/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export const getColumns = (
  items: Item[],
  staff: User[],
  router: AppRouterInstance
): ColumnDef<Exchange>[] => [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "original_item_id",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Original Item
          <ArrowUpDown />
        </Button>
      ),
      cell: ({ row }) => {
        const item = items.find(
          (item) => item.id === row.getValue("original_item_id"),
        )?.name;

        return (
          <div>
            <div
              className="font-semibold text-base cursor-pointer hover:text-primary"
              onClick={() => router.push(`/app/items/exchanges/${row.original.id}`)}
            >
              {item}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },

    {
      accessorKey: "exchanged_with_item_id",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Exchanged With
          <ArrowUpDown />
        </Button>
      ),
      cell: ({ row }) => {
        const item = items.find(
          (item) => item.id === row.getValue("exchanged_with_item_id"),
        )?.name;
        return <div>{item}</div>;
      },
      enableSorting: true,
    },

    {
      accessorKey: "receipt_number",
      header: "Receipt",
      cell: ({ row }) => <div>{row.getValue("receipt_number")}</div>,
      enableSorting: true,
    },

    {
      accessorKey: "exchange_value",
      header: "Exchange Value",
      cell: ({ row }) => <div>{row.getValue("exchange_value")}</div>,
      enableSorting: true,
    },

    {
      accessorKey: "staff_id",
      header: "Staff",
      cell: ({ row }) => {
        const staffMember = staff.find(
          (staff) => staff.id === row.getValue("staff_id"),
        )?.name;
        return <div>{staffMember}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "timestamp",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
          <ArrowUpDown />
        </Button>
      ),
      cell: ({ row }) => {
        const rawTime = row.getValue<string>("timestamp");
        // const formattedTime = new Date(rawTime).toDateString();
        return <div>{new Date(rawTime).toDateString()}</div>;
      },
      enableSorting: true,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const receipt = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(receipt.id)}
              >
                Copy Item Exchange ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => router.push(`/app/items/exchanges/${receipt.id}`)}
              >View Details</DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  router.push(`/app/items/exchanges/${receipt.id}/edit`);
                }}
              >
                Edit Customer
              </DropdownMenuItem>

            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
