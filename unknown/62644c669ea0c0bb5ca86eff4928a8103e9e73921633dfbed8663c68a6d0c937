import { RefundStatus } from "@/lib/types/refund_status";

export interface ReturnSchema {
  sale_id: string;
  item_id: string;
  return_reason: string;
  receipt_id: string;
  staff_id: string;
  refund_status: RefundStatus;
  quantity: number;
}

export interface Returns {
  id: string;
  sale_id: string;
  item_id: string;
  return_reason: string;
  timestamp: string;
  receipt_id: string;
  staff_id: string;
  store_id: string;
  refund_status: RefundStatus;
  quantity: number;
}
