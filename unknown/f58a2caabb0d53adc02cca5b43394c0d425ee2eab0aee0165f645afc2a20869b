//app/app/refunds/[RefundId]/page.tsx
// app/app/refunds/[RefundId]/page.tsx
"use client";

import { fetchRefund } from '@/lib/api/sales/refunds/service';
import { Refund } from '@/lib/api/sales/refunds/models';
import Cookies from 'js-cookie';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';



export default function RefundPage({ params }: { params: { RefundId: string; } }) {
  const refundId = params.RefundId;
  const [refund, setRefund] = useState<Refund | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const authToken = Cookies.get("auth_token");
    const storeId = Cookies.get("active_store");

    const fetchData = async () => {
      if (!authToken || !storeId) {
        return;
      }

      try {
        const fetchedRefund = await fetchRefund(authToken, storeId, refundId);
        setRefund(fetchedRefund);
      } catch (err: any) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [refundId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading refund details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/sales/refunds">
              <Button variant="link">Return to Refunds</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!refund) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Refund Not Found</h2>
          <p className="text-gray-600 text-center">The requested refund could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/sales/refunds">
              <Button variant="link">Return to Refunds</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/sales/refunds" className="hover:text-blue-600">Refunds</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Refund {refund.id}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Refund Details</h1>
          <p className="text-gray-500 text-sm">Refund ID: {refund.id}</p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Refund Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 font-medium">Transaction ID</p>
                  <p className="text-gray-800 text-sm">{refund.transaction_id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Return ID</p>
                  <p className="text-gray-800 text-sm">{refund.return_id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Quantity Refunded</p>
                  <p className="text-gray-800 text-sm">{refund.quantity_refunded}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Amount Refunded</p>
                  <p className="text-gray-800 text-sm">${refund.amount_refunded.toFixed(2)}</p>
                </div>
              </div>
            </div>
            <div>
              <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Additional Details</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 font-medium">Timestamp</p>
                  <p className="text-gray-800 text-sm">{formatDate(refund.timestamp)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Staff ID</p>
                  <p className="text-gray-800 text-sm">{refund.staff_id || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Store ID</p>
                  <p className="text-gray-800 text-sm">{refund.store_id}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}