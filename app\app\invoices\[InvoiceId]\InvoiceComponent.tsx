import { Invoice } from "@/lib/api/invoices/models";
import { Store } from "@/lib/api/retailStores/models";
import {
    Document,
    Page,
    Text,
    View,
    StyleSheet,
} from "@react-pdf/renderer";
import { PaymentStatus } from "@/lib/api/invoices/models";

const styles = StyleSheet.create({
    page: {
        padding: 40,
    },
    section: {
        marginBottom: 20,
    },
    text: {
        fontSize: 12,
        marginBottom: 5,
        color: "#333",
    },
    header: {
        fontSize: 20,
        fontWeight: "bold",
        marginBottom: 10,
        color: "#222",
    },
    table: {
        width: "100%",
    },
    tableRow: {
        flexDirection: "row",
    },
    tableCol: {
        width: "25%", // Equal width for all columns
    },
    tableCellHeader: {
        margin: 4,
        padding: 4,
        fontSize: 12,
        fontWeight: "bold",
        backgroundColor: "#eee",
    },
    tableCell: {
        margin: 4,
        padding: 4,
        fontSize: 10,
    },
    statusUnpaid: {
        backgroundColor: "#ffc",
        color: "#880",
        padding: 5,
        borderRadius: 5,
        fontSize: 12,
    },
    statusPaid: {
        backgroundColor: "#afa",
        color: "#080",
        padding: 5,
        borderRadius: 5,
        fontSize: 12,
    },
    logoContainer: {
        height: 60,
        width: 60,
        backgroundColor: "#ddd",
        borderRadius: 5,
        marginBottom: 10,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
    },
    logoText: {
        fontSize: 24,
        fontWeight: "bold",
        color: "#666",
    },
    // Removed notesContainer styles
    notesHeader: {
        fontSize: 14,
        fontWeight: "bold",
        marginBottom: 5,
    },
    notesText: {
        fontSize: 10,
    },
});

const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
};

const InvoicePdf = ({
    invoice,
    store,
    customerName,
    email,
}: {
    invoice: Invoice;
    store: Store;
    customerName?: string;
    email?: string;
}) => {
    const taxAmount = invoice.has_tax
        ? (invoice.subtotal * invoice.tax_amount) / 100
        : 0;

    return (
        <Document>
            <Page size="A4" style={styles.page}>
                {/* Header Section */}
                <View style={styles.section}>
                    <View
                        style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: 20,
                        }}
                    >
                        <View>
                            <View style={styles.logoContainer}>
                                <Text style={styles.logoText}>{store.name.charAt(0)}</Text>
                            </View>
                        </View>

                        <View style={{ textAlign: "right" }}>
                            <Text style={styles.header}>{store.name}</Text>
                            <Text style={styles.text}>{store.email || ""}</Text>
                            <Text style={styles.text}>{store.phone_number || ""}</Text>
                            <Text style={styles.text}>{store.address || ""}</Text>
                            {store.city && store.postal_code && (
                                <Text style={styles.text}>
                                    {store.city}, {store.state || ""} {store.postal_code}
                                </Text>
                            )}
                        </View>
                    </View>

                    <Text
                        style={
                            invoice.payment_status === PaymentStatus.Paid
                                ? styles.statusPaid
                                : styles.statusUnpaid
                        }
                    >
                        {invoice.payment_status}
                    </Text>

                    <View
                        style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            marginTop: 20,
                        }}
                    >
                        <View>
                            <Text style={styles.text}>Bill to: {customerName}</Text>
                            {email && <Text style={styles.text}>{email}</Text>}
                            <Text style={styles.text}>
                                Customer ID: {invoice.customer_id}
                            </Text>
                        </View>

                        <View style={{ textAlign: "right" }}>
                            <Text style={styles.header}>INVOICE</Text>
                            <Text style={styles.text}>
                                Invoice number: {invoice.invoice_number}
                            </Text>
                            <Text style={styles.text}>
                                Invoice date: {invoice.invoice_date}
                            </Text>
                            {invoice.due_date && (
                                <Text style={styles.text}>Due date: {invoice.due_date}</Text>
                            )}
                            {invoice.payment_date &&
                                invoice.payment_status === PaymentStatus.Paid && (
                                    <Text style={styles.text}>Paid on: {invoice.payment_date}</Text>
                                )}
                        </View>
                    </View>
                </View>

                {/* Table Section */}
                <View style={styles.table}>
                    <View style={styles.tableRow}>
                        <View style={styles.tableCol}>
                            <Text style={styles.tableCellHeader}>Item</Text>
                        </View>
                        <View style={styles.tableCol}>
                            <Text style={styles.tableCellHeader}>Quantity</Text>
                        </View>
                        <View style={styles.tableCol}>
                            <Text style={styles.tableCellHeader}>Price</Text>
                        </View>
                        <View style={styles.tableCol}>
                            <Text style={styles.tableCellHeader}>Total</Text>
                        </View>
                    </View>

                    {invoice.items.map((item, index) => (
                        <View style={styles.tableRow} key={index}>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{item.item_name}</Text>
                            </View>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{item.quantity}</Text>
                            </View>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{formatCurrency(item.price, store.currency)}</Text>
                            </View>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{formatCurrency(item.price * item.quantity, store.currency)}</Text>
                            </View>
                        </View>
                    ))}
                </View>

                {/* Summary Section */}
                <View style={{ ...styles.section, flexDirection: "column", alignItems: "flex-end" }}>
                    <Text style={styles.text}>Subtotal: {formatCurrency(invoice.subtotal, store.currency)}</Text>
                    {invoice.has_tax && (
                        <Text style={styles.text}>
                            Tax ({invoice.tax_amount}%): {formatCurrency(taxAmount, store.currency)}
                        </Text>
                    )}
                    <Text style={{ ...styles.text, fontWeight: "bold" }}>
                        Total: {formatCurrency(invoice.total_amount, store.currency)}
                    </Text>
                </View>

                {/* Notes and Terms Section - No Boxes */}
                {(invoice.notes || invoice.terms) && (
                    <View style={{ flexDirection: "column" }}> {/* Use column layout for stacking */}
                        {invoice.notes && (
                            <View style={{ marginBottom: 10 }}>
                                <Text style={styles.notesHeader}>Notes</Text>
                                <Text style={styles.notesText}>{invoice.notes}</Text>
                            </View>
                        )}
                        {invoice.terms && (
                            <View>
                                <Text style={styles.notesHeader}>Terms</Text>
                                <Text style={styles.notesText}>{invoice.terms}</Text>
                            </View>
                        )}
                    </View>
                )}

                {/* Footer Section */}
                <View
                    style={{
                        marginTop: 30,
                        paddingTop: 10,
                        borderTopWidth: 1,
                        borderTopColor: "#ccc",
                        alignItems: "center",
                    }}
                >
                    <Text style={styles.text}>powered by Storeyako</Text>
                    {invoice.payment_status === PaymentStatus.Unpaid && (
                        <Text style={styles.text}>Please make the payment before {invoice.due_date}</Text>
                    )}
                </View>
            </Page>
        </Document>
    );
};

export default InvoicePdf;
