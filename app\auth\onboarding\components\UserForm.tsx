// app/auth/registration/components/UserForm.tsx
import React from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { UserOnboardingData } from '@/lib/api/users/onboarding/models';
import { Label } from '@/components/ui/label';
import { Mail, User, Lock, ArrowRight } from 'lucide-react'; // Replace `At` with `Mail`

interface UserFormProps {
  userData: UserOnboardingData;
  onUserDataChange: (data: Partial<UserOnboardingData>) => void;
  onSubmit: () => void;
}

const UserForm: React.FC<UserFormProps> = ({ userData, onUserDataChange, onSubmit }) => {
  // Form validation
  const isNameValid = userData.name && userData.name.length >= 2;
  const isEmailValid = userData.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email);
  const isPasswordValid = userData.password && userData.password.length >= 8;

  const canProceed = isNameValid && isEmailValid && isPasswordValid;

  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <h2 className="text-2xl font-bold mb-6 text-center">Create Your Account</h2>
      <p className="text-gray-500 mb-6 text-center">Enter your information to get started</p>

      <div className="space-y-5">
        <div className="space-y-2">
          <Label htmlFor="user-name" className="text-sm font-medium">
            Full Name
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <User size={18} />
            </div>
            <Input
              id="user-name"
              placeholder="John Doe"
              value={userData.name}
              onChange={(e) => onUserDataChange({ name: e.target.value })}
              className={`pl-10 ${!userData.name ? '' : isNameValid ? 'border-green-400 focus-visible:ring-green-400' : 'border-red-400 focus-visible:ring-red-400'}`}
            />
          </div>
          {userData.name && !isNameValid && (
            <p className="text-red-500 text-xs mt-1">Name must be at least 2 characters</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="user-email" className="text-sm font-medium">
            Email Address
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <Mail size={18} /> {/* Replace `At` with `Mail` */}
            </div>
            <Input
              id="user-email"
              type="email"
              placeholder="<EMAIL>"
              value={userData.email}
              onChange={(e) => onUserDataChange({ email: e.target.value })}
              className={`pl-10 ${!userData.email ? '' : isEmailValid ? 'border-green-400 focus-visible:ring-green-400' : 'border-red-400 focus-visible:ring-red-400'}`}
            />
          </div>
          {userData.email && !isEmailValid && (
            <p className="text-red-500 text-xs mt-1">Please enter a valid email address</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">
            Password
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <Lock size={18} />
            </div>
            <Input
              id="password"
              type="password"
              placeholder="Create a strong password"
              value={userData.password}
              onChange={(e) => onUserDataChange({ password: e.target.value })}
              className={`pl-10 ${!userData.password ? '' : isPasswordValid ? 'border-green-400 focus-visible:ring-green-400' : 'border-red-400 focus-visible:ring-red-400'}`}
            />
          </div>
          {userData.password && !isPasswordValid && (
            <p className="text-red-500 text-xs mt-1">Password must be at least 8 characters</p>
          )}
        </div>
      </div>

      {/* Hidden fields */}
      <input type="hidden" value={userData.plan_id} />
      <input type="hidden" value={userData.profile} />
      <input type="hidden" value={userData.role_id} />
      <input type="hidden" value={userData.store_id} />
      <input type="hidden" value={userData.user_defined_role_id} />

      <Button
        type="button"
        onClick={onSubmit}
        className="w-full mt-8 font-medium flex items-center justify-center gap-2"
        disabled={!canProceed}
      >
        Continue to Store Setup
        <ArrowRight size={16} />
      </Button>

      <p className="text-center text-xs text-gray-500 mt-4">
        By creating an account, you agree to our Terms of Service and Privacy Policy
      </p>
    </div>
  );
};

export default UserForm;
