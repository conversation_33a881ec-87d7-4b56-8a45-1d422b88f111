"use client";

import { useState } from "react";
import { FormProvider, useForm } from 'react-hook-form';
import LoginForm from './components/LoginForm';
import { Alert, AlertDescription } from "@/components/ui/alert";
import Cookies from "js-cookie";
import { login } from "@/lib/api/auth/loginService";
import { useRouter } from "next/navigation";
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export default function LoginPage() {
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  return (
    <GoogleReCaptchaProvider reCaptchaKey={siteKey || ""}>
      <LoginPageContent />
    </GoogleReCaptchaProvider>
  );
}

function LoginPageContent() {
  const methods = useForm({
    defaultValues: {
      user: {
        email: '',
        password: '',
      },
    },
    mode: 'onChange',
  });
  const { handleSubmit, formState, setError } = methods;
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);

  const onSubmit = async (data: any, captcha_token: string) => {
    setIsLoading(true);
    setGeneralError(null);
    try {
      const payload = {
        email: data.user.email,
        password: data.user.password,
        captcha_token,
      };

      const resp = await login(payload);
      Cookies.set('auth_token', resp.token, {
        expires: 7,
        sameSite: 'strict',
        path: '/',
      });
      router.push('/app/stores');
    } catch (error: any) {
      if (error?.response?.data?.errors) {
        const allowedFields = ['email', 'password'] as const;
        Object.entries(error.response.data.errors).forEach(([field, message]) => {
          if (allowedFields.includes(field as typeof allowedFields[number])) {
            setError(`user.${field}` as 'user.email' | 'user.password', { type: 'manual', message: message as string });
          }
        });
      } else {
        setGeneralError(error instanceof Error ? error.message : 'An error occurred during login. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-2 py-4 sm:px-6 sm:py-8 mx-auto bg-background font-sans">
      <div className="w-full bg-white rounded-lg shadow dark:border max-w-md sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        <div className="p-3 sm:p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white text-center">
            Login to your Account
          </h1>
          {generalError && (
            <Alert variant="error">
              <AlertDescription>{generalError}</AlertDescription>
            </Alert>
          )}
          <FormProvider {...methods}>
            <LoginForm onSubmit={(captcha_token) => handleSubmit((data) => onSubmit(data, captcha_token))()} />
          </FormProvider>
        </div>
      </div>
    </div>
  );
}
