import { NewStaff, User, UserInfoResponse } from "./models";

import { BASE_URL } from "@/app/configs/constants";

export async function list_users(
  authToken: string,
  storeId: string,
): Promise<UserInfoResponse[]> {
  try {
    const response = await fetch(`${BASE_URL}/users/admin/${storeId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function create_user(authToken: string, storeId: string, user: NewStaff): Promise<User> {
  try{
    const response = await fetch(`${BASE_URL}/users/admin/${storeId}/new`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(user),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function revoke_user_access(
  authToken: string,
  storeId: string,
  userId: string,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/deactivate`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function reactivate_user_access(
  authToken: string,
  storeId: string,
  userId: string,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/reactivate`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getUser(authToken: string, storeId: string, userId: string): Promise<UserInfoResponse> {

  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }

}

export async function updateUser(authToken: string, storeId: string, userId: string, user: NewStaff): Promise<User> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(user),
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getMe(authToken: string): Promise<User> {
  try {
    const response = await fetch(`${BASE_URL}/users/me`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function delete_user(
  authToken: string,
  storeId: string,
  userId: string,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/delete`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}
