"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ReturnForm } from "@/app/app/items/returns/ReturnForm";
import { Item } from "@/lib/api/items/models";
import { ReturnSchema } from "@/lib/api/items/returns/models";
import { fetchItems } from "@/lib/api/items/service";
import { getReturn, updateReturn } from "@/lib/api/items/returns/service";
import { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";
import { fetchReceipts } from "@/lib/api/receipts/service";
import { Receipt } from "@/lib/api/receipts/models";
import { getMe } from "@/lib/api/users/service";

export default function EditReturnPage({ params }: { params: { ReturnId: string } }) {
  const [items, setItems] = useState<Item[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [initialData, setInitialData] = useState<ReturnSchema | null>(null);
  const [userId, setUserId] = useState<string | null>(null);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");


  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const [fetchedItems, returnData, fetchedSales, fetchedReceipts, fechedUser] = await Promise.all([
            fetchItems(token, store_id),
            getReturn(token, store_id, params.ReturnId),
            fetchSales(token, store_id),
            fetchReceipts(token, store_id),
            getMe(token)
          ]);
          setItems(fetchedItems);
          setInitialData(returnData);
          setSales(fetchedSales);
          setReceipts(fetchedReceipts);
          setUserId(fechedUser.id);
        } catch (error) {
          console.error("Failed to fetch data:", error);
        }
      }
    };
    fetchData();
  }, [token, store_id, params.ReturnId]);

  const handleSubmit = async (data: ReturnSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    try {
      await updateReturn(token, store_id, params.ReturnId, data);
    } catch (error) {
      console.error("Error updating return:", error);
      throw error;
    }
  };

  if (!initialData || !userId) {
    return <div>Loading...</div>;
  }

  return (
    <ReturnForm
      initialData={initialData}
      onSubmit={handleSubmit}
      items={items}
      sales={sales}
      receipts={receipts}
      isEditing={true}
      userId={userId}
    />
  );
}
