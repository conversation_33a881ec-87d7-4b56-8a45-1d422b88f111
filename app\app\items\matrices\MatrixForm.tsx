//app/app/items/matrices/MatrixForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import { Supplier } from "@/lib/api/suppliers/models";
import MatrixAttributes from "./create/AttributesSection";
import { Category } from "@/lib/api/categories/models";
import { Brand } from "@/lib/api/brands/models";
import { AttributeConfig } from "./create/AttributesSection";
import { ItemMatrixSchema } from "@/lib/api/items/matrices/models";

interface MatrixFormProps {
  initialData?: ItemMatrixSchema;
  attributes: AttributeConfig[];
  availableAttributes: Attributes[];
  availableAttributeValues: AttributeValues[];
  vendors: Supplier[];
  categories: Category[];
  brands: Brand[];
  storeId: string;
  onSubmit: (data: ItemMatrixSchema, attributes: AttributeConfig[]) => Promise<void>;
  onCancel: () => void;
  onAddAttribute: (attributeId: string) => void;
  onRemoveAttribute: (attributeId: string) => void;
  onToggleValue: (attributeId: string, valueId: string) => void;
  onAddValue: (attributeId: string, value: string) => void;
  onRemoveValue: (attributeId: string, valueId: string) => void;
  onAddBulkValues: (attributeId: string, values: AttributeValuesSchema) => Promise<void>;
  loading: boolean;
  saving: boolean;
}

interface FormState extends ItemMatrixSchema {
  attributes: AttributeConfig[];
}

const matrixTypes = [
  { id: "size", name: "Size Based" },
  { id: "color", name: "Color Based" },
  { id: "combo", name: "Size & Color" },
];

export const MatrixForm: React.FC<MatrixFormProps> = ({
  initialData,
  attributes,
  availableAttributes,
  availableAttributeValues,
  vendors,
  categories,
  brands,
  storeId,
  onSubmit,
  onCancel,
  onAddAttribute,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onRemoveValue,
  onAddBulkValues,
  loading,
  saving
}) => {
  const [formData, setFormData] = useState<ItemMatrixSchema>(initialData || {
    store_id: storeId,
    name: "",
    description: "",
    matrix_type: "",
    category: "",
    brand: "",
    vendor_id: "",
    default_price: 0,
    default_cost: 0,
    has_discount: false,
    discount: 0,
    msrp: 0,
    // attributes: [], //Move this at the parent Level
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof ItemMatrixSchema, string>>
  >({});

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof ItemMatrixSchema, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.matrix_type) {
      newErrors.matrix_type = "Matrix type is required";
    }

    if (formData.default_price < 0) {
      newErrors.default_price = "Price cannot be negative";
    }

    if (formData.default_cost < 0) {
      newErrors.default_cost = "Cost cannot be negative";
    }

    if (formData.has_discount && (formData.discount || 0) > 100) {
      newErrors.discount = "Discount cannot exceed 100%";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await onSubmit(formData, attributes);
  };

  useEffect(() => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      store_id: storeId,
    }));
  }, [storeId]);


  return (
    <Card className="w-full mx-auto px-4 sm:px-6 md:px-8">
      <CardHeader>
        <CardTitle>{initialData ? "Edit Item Matrix" : "Create Item Matrix"}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex flex-col md:grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Matrix Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="Enter matrix name"
                />
                 {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="matrix_type">Matrix Type *</Label>
                <Select
                  value={formData.matrix_type}
                  onValueChange={(value) =>
                    setFormData({ ...formData, matrix_type: value })
                  }
                >
                  <SelectTrigger id="matrix_type">
                    <SelectValue placeholder="Select matrix type" />
                  </SelectTrigger>
                  <SelectContent>
                    {matrixTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Enter description"
              />
            </div>
          </div>

          <div className="flex flex-col md:grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value) =>
                  setFormData({ ...formData, category: value })
                }
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Select
                value={formData.brand}
                onValueChange={(value) =>
                  setFormData({ ...formData, brand: value })
                }
              >
                <SelectTrigger id="brand">
                  <SelectValue placeholder="Select brand" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vendor">Vendor</Label>
              <Select
                value={formData.vendor_id}
                onValueChange={(value) =>
                  setFormData({ ...formData, vendor_id: value })
                }
              >
                <SelectTrigger id="vendor">
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col md:grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="default_price">Default Price *</Label>
              <Input
                id="default_price"
                type="number"
                step="0.01"
                min="0"
                value={formData.default_price}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    default_price: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="0.00"
                aria-invalid={!!errors.default_price}
              />
              {errors.default_price && (
                <p className="text-sm text-red-500">{errors.default_price}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="default_cost">Default Cost *</Label>
              <Input
                id="default_cost"
                type="number"
                step="0.01"
                min="0"
                value={formData.default_cost}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    default_cost: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="0.00"
                aria-invalid={!!errors.default_cost}
              />
              {errors.default_cost && (
                <p className="text-sm text-red-500">{errors.default_cost}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="msrp">MSRP</Label>
              <Input
                id="msrp"
                type="number"
                step="0.01"
                min="0"
                value={formData.msrp || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    msrp: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="0.00"
              />
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-2">
              <Switch
                id="has_discount"
                checked={formData.has_discount}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, has_discount: checked })
                }
              />
              <Label htmlFor="has_discount">Enable Discount</Label>
            </div>

            {formData.has_discount && (
              <div className="space-y-2">
                <Label htmlFor="discount">Discount Percentage</Label>
                <Input
                  id="discount"
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={formData.discount || ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      discount: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="0.0"
                  aria-invalid={!!errors.discount}
                />
                {errors.discount && (
                  <p className="text-sm text-red-500">{errors.discount}</p>
                )}
              </div>
            )}
          </div>
          <MatrixAttributes
            attributes={attributes}
            availableAttributes={availableAttributes}
            onAddAttribute={onAddAttribute}
            onRemoveAttribute={onRemoveAttribute}
            onToggleValue={onToggleValue}
            onAddValue={onAddValue}
            onRemoveValue={onRemoveValue}
            storeId={storeId}
            onAddBulkValues={onAddBulkValues}
          />
        </CardContent>

        <CardFooter className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? "Saving..." : initialData ? "Save Changes" : "Create Matrix"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};