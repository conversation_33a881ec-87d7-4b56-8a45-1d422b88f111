//app/app/items/matrices/create/AttributesSection.tsx
import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Attributes,
  AttributeValues,
  AttributePayload,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import AttributeCard from "./AttributesCard";
import {
  createAttribute,
  fetchAttributes,
} from "@/lib/api/attributes/attributeService";
import Cookies from "js-cookie";
import AttributeConfig from "./page";

export interface AttributeConfig {
  attribute: Attributes;
  values: AttributeValues[];
  selectedValues: string[];
}

interface MatrixAttributesProps {
  attributes: AttributeConfig[];
  availableAttributes: Attributes[];
  onAddAttribute: (attributeId: string) => void;
  onRemoveAttribute: (attributeId: string) => void;
  onToggleValue: (attributeId: string, valueId: string) => void;
  onAddValue?: (attributeId: string, value: string) => void;
  onAddBulkValues?: (
    attributeId: string,
    values: AttributeValuesSchema,
  ) => Promise<void>;
  onRemoveValue?: (attributeId: string, valueId: string) => void;
  onCreateAttribute?: (name: string) => Promise<Attributes>;
  storeId: string;
}

const MatrixAttributes: React.FC<MatrixAttributesProps> = ({
  attributes,
  availableAttributes,
  onAddAttribute,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onAddBulkValues,
  onRemoveValue,
}) => {
  const [currentAttributeId, setCurrentAttributeId] = useState<string>("");
  const [isNewAttributeDialogOpen, setIsNewAttributeDialogOpen] =
    useState(false);
  const [isValuesDialogOpen, setIsValuesDialogOpen] = useState(false);
  const [newAttributeName, setNewAttributeName] = useState("");
  const [isCreatingAttribute, setIsCreatingAttribute] = useState(false);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  const filteredAttributes = useMemo(
    () =>
      availableAttributes.filter(
        (attr) =>
          !attributes.some(
            (existingAttr) => existingAttr.attribute.id === attr.id,
          ),
      ),
    [availableAttributes, attributes],
  );

  const totalCombinations = useMemo(
    () => attributes.reduce((acc, attr) => acc * attr.values.length, 1),
    [attributes],
  );

  const handleAttributeSelect = (value: string) => {
    if (value === "new") {
      setIsNewAttributeDialogOpen(true);
    } else {
      setCurrentAttributeId(value);
      onAddAttribute(value);
      setIsValuesDialogOpen(true);
    }
  };

  const handleRemoveAttributeWithReset = (attributeId: string) => {
    onRemoveAttribute(attributeId);
    if (attributes.length <= 1) {
      setCurrentAttributeId("");
    }
  };

  const handleCreateAttribute = async () => {
    try {
      setIsCreatingAttribute(true);

      const trimmedName = newAttributeName.trim();
      const payload: AttributePayload = {
        name: trimmedName,
      };
      await createAttribute(token!, store_id!, payload);
      const _attributes = await fetchAttributes(token!, store_id!);
      const newAttribute = _attributes.find(
        (attr) => attr.name === trimmedName,
      )!;

      setIsNewAttributeDialogOpen(false);
      setNewAttributeName("");
      setCurrentAttributeId(newAttribute.id);
      onAddAttribute(newAttribute.id);
      setIsValuesDialogOpen(true);
    } catch (error) {
      console.error("Failed to create attribute:", error);
    } finally {
      setIsCreatingAttribute(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Matrix Attributes</h3>
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <Select
            value={currentAttributeId}
            onValueChange={handleAttributeSelect}
          >
            <SelectTrigger aria-label="Add Attribute">
              <SelectValue placeholder="Add Attribute" />
            </SelectTrigger>
            <SelectContent>
              {filteredAttributes.map((attr) => (
                <SelectItem key={attr.id} value={attr.id}>
                  {attr.name}
                </SelectItem>
              ))}
              <SelectItem value="new" className="text-primary">
                + Create New Attribute
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        {attributes.map((attr) => (
          <AttributeCard
            key={attr.attribute.id}
            attr={attr}
            onRemoveAttribute={handleRemoveAttributeWithReset}
            onToggleValue={onToggleValue}
            onAddValue={onAddValue}
            onRemoveValue={onRemoveValue}
            onAddBulkValues={onAddBulkValues}
          />
        ))}
      </div>

      {attributes.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Matrix Preview</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">
              This matrix will create {totalCombinations} items with the
              following combinations:
            </p>
            <div className="mt-2">
              {attributes.map((attr) => (
                <div key={attr.attribute.id} className="text-sm">
                  <span className="font-medium">{attr.attribute.name}:</span>{" "}
                  {attr.values.map((v) => v.value).join(", ")}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <Dialog
        open={isNewAttributeDialogOpen}
        onOpenChange={setIsNewAttributeDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Attribute</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="attributeName">Attribute Name</Label>
              <Input
                id="attributeName"
                value={newAttributeName}
                onChange={(e) => setNewAttributeName(e.target.value)}
                placeholder="Enter attribute name"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewAttributeDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateAttribute}
              disabled={!newAttributeName.trim() || isCreatingAttribute}
            >
              {isCreatingAttribute ? "Creating..." : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MatrixAttributes;
