"use client";

import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { createRetailStoreClient } from "@/lib/api/clients/service";
import { CustomerForm } from "@/app/app/customers/CustomerForm";
import { type NewCustomerSchema } from "@/lib/api/customers/models";

export default function CreateCustomerPage() {
  const router = useRouter();

  const handleSubmit = async (data: NewCustomerSchema) => {
    const token = Cookies.get('auth_token');
    const store_id = Cookies.get("active_store");

    if (!token || !store_id) {
      throw new Error('Authentication token or store ID is missing.');
    }

    await createRetailStoreClient(token, store_id, data);
    router.push('/app/customers');
  };

  return (
    <CustomerForm onSubmit={handleSubmit} />
  );
}
