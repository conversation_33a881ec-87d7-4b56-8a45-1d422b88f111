"use client";

import React, { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Item, ItemSchema } from "@/lib/api/items/models";
import { Brand } from "@/lib/api/brands/models";
import { Category } from "@/lib/api/categories/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import Cookies from "js-cookie";
import { uploadFile } from "@/lib/api/uploads/service";
import { useRouter } from "next/navigation";
import { Scan, Loader2 } from "lucide-react";
import dynamic from "next/dynamic";
import { toast } from "sonner";

// Dynamic import for barcode scanner
const BarcodeScanner = dynamic(
  () => import("react-qr-barcode-scanner"),
  { ssr: false }
);

interface ItemFormProps {
  initialData?: ItemSchema;
  onSubmit(data: ItemSchema[] | Item[]): Promise<void>;
  isEditing?: boolean;
  categories: Category[];
  suppliers: Supplier[];
  brands: Brand[];
}

export function ItemForm({
  initialData,
  onSubmit,
  isEditing = false,
  categories,
  suppliers,
  brands,
}: ItemFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
  } = useForm<ItemSchema>({
    defaultValues: initialData || {
      name: "",
      quantity: 0,
      default_cost: 0,
      default_price: 0,
      brand: "",
      has_discount: false,
      tags: [],
      is_variant: false,
      category: "",
      store_id: "",
      vendor_id: "",
      image: "",
      description: "",
      sku: "",
      barcode: "",
      notes: "",
      date_created: "",
      parent_item_id: null,
    },
  });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const router = useRouter();

  // Barcode scanning state
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [cameraReady, setCameraReady] = useState(false);
  const [cameraError, setCameraError] = useState<string>("");

  // Update form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      reset(initialData);
    }
  }, [initialData, reset]);

  // Barcode scanning functions
  const handleBarcodeScanned = useCallback((barcode: string) => {
    if (isScanning) return;

    setIsScanning(true);
    console.log("Scanned barcode:", barcode);

    // Set the barcode value in the form
    setValue("barcode", barcode);
    toast.success(`Barcode scanned: ${barcode}`);

    // Close scanner after successful scan
    setTimeout(() => {
      setIsScannerOpen(false);
      setIsScanning(false);
      setCameraReady(false);
      setCameraError("");
    }, 1000);
  }, [isScanning, setValue]);

  const openBarcodeScanner = useCallback(() => {
    setIsScannerOpen(true);
    setCameraError("");
    setCameraReady(false);
  }, []);

  const closeBarcodeScanner = useCallback(() => {
    setIsScannerOpen(false);
    setIsScanning(false);
    setCameraReady(false);
    setCameraError("");
  }, []);

  const handleImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        setValue("image", file);
      }
    },
    [setValue],
  );

  const handleFormSubmit = async (data: ItemSchema) => {
    setError(null);
    setSuccess(false);
    setUploading(true);

    try {
      // Handle image upload if a new file was selected
      if (data.image instanceof File) {
        toast.info("Uploading new image...");
        const authToken = Cookies.get("auth_token") || "";
        const folder = "products";
        const imageUrl = await uploadFile(authToken, folder, data.image);
        data.image = imageUrl;
        toast.success("Image uploaded successfully!");
      }
      // If data.image is already a string (existing URL), keep it as is

      await onSubmit([data]);
      setSuccess(true);
      router.push("/app/items");
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-5xl">
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Card>
          {error && <AlertError message={error} />}
          {success && <AlertSuccess message={`Product ${isEditing ? 'updated' : 'added'} successfully`} />}
          <CardHeader>
            <CardTitle>{isEditing ? 'Edit Product' : 'Add Product'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="required">
                    Product Name
                  </Label>
                  <Input id="name" {...register("name", { required: true })} />
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    onValueChange={(value) => setValue("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="warehouse">Brand/Model</Label>
                  <Select onValueChange={(value) => setValue("brand", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="image">Image</Label>
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Detail</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Product details..."
                    className="h-32"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="quantity" className="required">
                  Quantity
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  step="1"
                  {...register("quantity", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div>
                <Label htmlFor="default_price" className="required">
                  Sell Price
                </Label>
                <Input
                  id="default_price"
                  type="number"
                  step="0.01"
                  {...register("default_price", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div>
                <Label htmlFor="default_cost" className="required">
                  Supplier Price
                </Label>
                <Input
                  id="default_cost"
                  type="number"
                  step="0.01"
                  {...register("default_cost", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div>
                <Label htmlFor="sku" className="required">
                  SKU
                </Label>
                <Input id="sku" {...register("sku", { required: true })} />
              </div>

              <div>
                <Label htmlFor="vendor">Supplier</Label>
                <Select onValueChange={(value) => setValue("vendor_id", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <div>
                <Label htmlFor="tags">Tags (comma-separated)</Label>
                <Input
                  id="tags"
                  value={watch("tags")?.join(", ") || ""} // Display tags as a comma-separated string
                  onChange={(e) => {
                    const tagsArray = e.target.value
                      .split(",")
                      .map((tag) => tag.trim())
                      .filter((tag) => tag.length > 0);
                    setValue("tags", tagsArray); // Update the form state with the array
                  }}
                  placeholder="e.g., tag1, tag2, tag3"
                />
              </div>
            </div>
            <div>
              <div>
                <Label htmlFor="barcode">Barcode</Label>
                <div className="flex gap-2">
                  <Input
                    id="barcode"
                    {...register("barcode")}
                    placeholder="Enter barcode or scan"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={openBarcodeScanner}
                    variant="outline"
                    size="icon"
                    className="shrink-0"
                  >
                    <Scan className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="has_discount"
                onCheckedChange={(checked) => setValue("has_discount", checked)}
              />
              <Label htmlFor="has_discount">Enable Discount</Label>
            </div>


            {watch("has_discount") && (
              <div className="w-full md:w-1/4">
                <Label htmlFor="discount">Discount Amount</Label>
                <Input
                  id="discount"
                  type="number"
                  step="0.01"
                  {...register("discount", { valueAsNumber: true })}
                  onChange={(e) => setValue("discount", parseFloat(e.target.value) || 0)}
                />
              </div>
            )}

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                {...register("notes")}
                placeholder="Additional notes..."
              />
            </div>

            <Button type="submit" className="w-full" disabled={uploading}>
              {uploading ? "Uploading..." : isEditing ? 'Update Product' : 'Add Product'}
            </Button>
          </CardContent>
        </Card>
      </form>

      {/* Barcode Scanner Dialog */}
      <Dialog open={isScannerOpen} onOpenChange={setIsScannerOpen}>
        <DialogContent className="w-[95vw] h-[85vh] max-w-sm p-0 overflow-hidden flex flex-col sm:w-[90vw] sm:h-[75vh] sm:max-w-md rounded-lg">
          <DialogHeader className="flex-shrink-0 p-3 sm:p-4 border-b bg-background">
            <DialogTitle className="flex items-center text-base sm:text-lg">
              <Scan className="mr-2 h-5 w-5 flex-shrink-0" />
              <span className="truncate">Scan Barcode</span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 flex flex-col items-center justify-center p-2 sm:p-4 bg-gray-900 min-h-0 overflow-hidden">
            {cameraError ? (
              <div className="text-center text-primary-foreground px-4 max-w-sm mx-auto">
                <div className="mb-6">
                  <Scan className="h-20 w-20 sm:h-16 sm:w-16 mx-auto text-muted-foreground" />
                </div>
                <h3 className="text-xl sm:text-lg font-semibold mb-4 sm:mb-2">Camera Access Required</h3>
                <p className="text-base sm:text-sm text-muted-foreground mb-6 sm:mb-4 leading-relaxed">{cameraError}</p>
                <div className="space-y-4 sm:space-y-2">
                  <Button
                    onClick={async () => {
                      try {
                        const stream = await navigator.mediaDevices.getUserMedia({
                          video: { facingMode: 'environment' }
                        });
                        stream.getTracks().forEach(track => track.stop());
                        setCameraError("");
                        setCameraReady(false);
                        toast.success("Camera access granted!");
                      } catch (error: any) {
                        setCameraError("Camera permission denied. Please allow camera access.");
                        toast.error("Camera permission denied");
                      }
                    }}
                    className="w-full h-12 sm:h-10 text-base sm:text-sm"
                  >
                    Allow Camera Access
                  </Button>
                </div>
              </div>
            ) : (
              <div className="relative w-full h-full max-w-sm mx-auto flex-1 min-h-0">
                <BarcodeScanner
                  width="100%"
                  height="100%"
                  facingMode="environment"
                  delay={300}
                  onUpdate={(err, result) => {
                    if (err) {
                      if ((err as any)?.name === "NotFoundException" ||
                          (err as any)?.message?.includes("No MultiFormat Readers were able to detect the code")) {
                        if (!cameraReady) {
                          setCameraReady(true);
                        }
                        return;
                      }
                      setCameraReady(true);
                      setCameraError("Camera error. Please check permissions and try again.");
                      return;
                    }

                    if (!cameraReady) {
                      setCameraReady(true);
                    }

                    if (result && !isScanning) {
                      let scannedText = '';
                      try {
                        if (typeof result === 'string') {
                          scannedText = result;
                        } else if (result && typeof result === 'object') {
                          scannedText = (result as any).getText?.() ||
                                       (result as any).text ||
                                       (result as any).data ||
                                       String(result);
                        }

                        if (scannedText && typeof scannedText === 'string') {
                          handleBarcodeScanned(scannedText);
                        }
                      } catch (error) {
                        console.warn('Error processing barcode result:', error);
                      }
                    }
                  }}
                />

                {/* Camera loading overlay */}
                {!cameraReady && (
                  <div className="absolute inset-0 bg-background/75 flex items-center justify-center">
                    <div className="text-center text-foreground">
                      <Loader2 className="h-10 w-10 sm:h-8 sm:w-8 animate-spin mx-auto mb-3 sm:mb-2 text-primary" />
                      <p className="text-base sm:text-sm">Initializing camera...</p>
                    </div>
                  </div>
                )}

                {/* Scanning frame overlay */}
                {cameraReady && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-56 sm:h-56 border-2 border-border rounded-lg">
                      <div className="absolute top-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
                      <div className="absolute top-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
                      <div className="absolute bottom-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
                      <div className="absolute bottom-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}