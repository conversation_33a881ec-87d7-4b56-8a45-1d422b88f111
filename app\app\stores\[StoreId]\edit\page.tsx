"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { StoreForm } from "@/app/app/stores/StoreForm";
import { Store, StoreSchema } from "@/lib/api/retailStores/models";
import { getStore, updateStore } from "@/lib/api/retailStores/service"; // Import fetchStore and updateStore


export default function EditStorePage({ params }: { params: { StoreId: string } }) {
  const [initialData, setInitialData] = useState<StoreSchema | null>(null);

  const token = Cookies.get("auth_token");

  useEffect(() => {
    if (!token) return;

    const fetchData = async () => {
      try {
        const store: Store = await getStore(params.StoreId, token);
        const storeSchema: StoreSchema = {
          ...store,
        };
        setInitialData(storeSchema);
      } catch (error) {
        console.error("Failed to fetch store:", error);
      }
    };

    fetchData();
  }, [token, params.StoreId]);

  const handleSubmit = async (data: StoreSchema) => {
    if (!token) {
      throw new Error("Authentication token or store ID is missing.");
    }
    await updateStore(params.StoreId, data, token);
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <StoreForm initialData={initialData} onSubmit={handleSubmit} isEditing />
  );
}