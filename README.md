This is a [Storeyako.com](https://storeyako.com) web client.

## Getting Started

First, run the development server:

```bash
pnpm dev
```

> Making tables (mobile responsive)[https://github.com/TanStack/table/discussions/3259]

### TODOs
- [x] Make tables (mobile responsive)[https://github.com/TanStack/table/discussions/3259]
- [x] Item Matrices
- [x] Items from Matrices
    - https://retail-support.lightspeedhq.com/hc/article_attachments/115010599208/reorder.gif
    - https://retail-support.lightspeedhq.com/hc/article_attachments/19116704245019
    - https://revelup-techpubs.s3-us-west-2.amazonaws.com/support/hc/en-us/203923925/matrix11.jpg
    - https://revelup-techpubs.s3-us-west-2.amazonaws.com/support/hc/en-us/203923925/matrix6.jpg (shows a modal for confirmation)
    - https://revelup-techpubs.s3-us-west-2.amazonaws.com/support/hc/en-us/203923925/matrix9.jpg (General overview of item matrices)
- [x] User Roles
- [ ] Receipts & PDFs
- [ ] Image Uploads (for store information) (Needs backend) (local os for now)
- [ ] Store Theming

### Features
- Dragable with sorftableJS   - https://github.com/SortableJS/react-sortablejs


### TODOS:

##### TODO refinements from testing

- [x] loading items - must be uniform
- [x] Need to make sure staff page makes sense - small task
- [x] mobile responsiveness in tables
    - https://github.com/shadcn-ui/ui/issues/763
- [ ] Returns, Exchanges, & Refunds pages - small task
- [ ] Put Sales Target - small task
- [x] Transactions table - with download options - small task

- [ ] receipts
    - [x] generate pdf receipts
    - [ ] print receipt pdf

- [x] dashboard
    - [x] layout
    - [x] functionality
- [x] charts
    - [x] layout
    - [x] functionality
- [x] 404 page
- [x] Mobile Responsive
- [ ] Live Support

- [x] SignUp workflow
- [x] Edit buttons and pages data fetching
- [x] Converting matrices to items
- [x] Adding a new user + email + password
- [x] update multiple items
- [x] When generating items from matrices, fetch only attribute values registered in the matrix
- [x] Design Roles page
- [x] selling items + sending email to user
- [x] Make returns page look better (the idea is; we only need receipts and prefill other information)
- [x] Make exchanges better. We only need receipts and item to be exchanged with then prefill other information.
- [x] design read pages
- [x] invoices
    - [x] design
    - [x] generate pdf
    - [x] update invoice
    - [x] delete invoice items
    - [x] should update in UI during any action like adding invoice item etc

- [x] Make roles page functional
- [x] returning items & refunds

- [x] Deleting Matrices & Orphaning variant items - Backend
- [x] Matrix Component - Frontend - Use AI
- [x] Attributes Section for Matrix Update - Frontend - User AI
- [x] Update & Delete Matrix Attributes
- [x] Update Matrices
- [x] Store Logos
- [x] Selling Items - Frontend
- [x] Fix Returns - UI



- [ ] linking with google analytics
- [ ] blog/articles
- [ ] case studies
- [ ] testimonials
- [ ] FAQ 
- [ ] Users / Members
- [ ] Pricing

- [ ] Facebook Ads
- [ ] Google Ads
- [ ] Medium Blogs
- [ ] Linkedin Ads
- [ ] Tiktok
- [ ] Youtube
