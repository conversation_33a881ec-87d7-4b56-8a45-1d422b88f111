"use client";

import * as React from "react";
import { useEffect, useMemo } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
// Helper to format column headers for card view
function formatHeader(id: string): string {
  return id
    .replace(/_/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Loader2, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import { useRouter } from "next/navigation";

import { getColumns } from "./columns";
import { Sale } from "@/lib/api/sales/models";
import { Item } from "@/lib/api/items/models";
import { User, UserInfoResponse } from "@/lib/api/users/models";
import { fetchSales } from "@/lib/api/sales/service";
import { fetchItems } from "@/lib/api/items/service";
import { list_users as fetchUsers } from "@/lib/api/users/service";
import Cookies from "js-cookie";
import { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";

function LoadingSpinner() {
  return <Loader2 className="h-8 w-8 animate-spin text-primary" />;
}

export default function Page() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const router = useRouter();
  const [data, setSales] = React.useState<Sale[]>([]);
  const [items, setItems] = React.useState<Item[]>([]);
  const [users, setUsers] = React.useState<UserInfoResponse[]>([]);
  const [receipts, setReceipts] = React.useState<Receipt[]>();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [isInitialLoading, setIsInitialLoading] = React.useState(true);
  const [pageIndex, setPageIndex] = React.useState(0);
  const [pageSize, setPageSize] = React.useState(10);

  useEffect(() => {
    async function fetchData() {
      setIsInitialLoading(true);
      try {
        const token = Cookies.get("auth_token");
        const store_id = Cookies.get("active_store");

        if (!token || !store_id) {
          throw new Error("Authentication token or store ID is missing");
        }

        const [salesData, itemsData, usersData, receipts] = await Promise.all([
          fetchSales(token, store_id),
          fetchItems(token, store_id),
          fetchUsers(token, store_id),
          fetchReceipts(token, store_id),
        ]);
        setSales(salesData);
        setItems(itemsData);
        setUsers(usersData);
        setReceipts(receipts);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsInitialLoading(false);
      }
    }
    fetchData();
  }, []);

  const columns = useMemo(() => getColumns(users, items, receipts!, router), [users, items, receipts, router]);


  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
        <Input
          placeholder="Filter sales..."
          value={(table.getColumn("receipt_number")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("receipt_number")?.setFilterValue(event.target.value)
          }
          className="w-full sm:max-w-sm"
          disabled={isInitialLoading}
        />

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0">
          <Button
            onClick={() => router.push("/app/sales/new")}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Sale
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                Columns <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Responsive Table/Card Layout */}
      {!isMobile && (
        <div className="rounded-md border relative w-full overflow-auto grid grid-cols-1">
          <Table className="w-full caption-bottom text-sm">
            <TableHeader>
              {table
                .getHeaderGroups()
                .map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
            </TableHeader>
            <TableBody>
              {isInitialLoading ? (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-64 p-0">
                    <div className="flex items-center justify-center h-full w-full">
                      <LoadingSpinner />
                    </div>
                  </TableCell>
                </TableRow>
              ) : paginatedRows.length ? (
                paginatedRows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
      {isMobile && (
        <div className="grid grid-cols-1 gap-4">
          {isInitialLoading && <LoadingSpinner />}
          {!isInitialLoading && paginatedRows.length === 0 && (
            <div className="text-center text-muted-foreground p-4">No results.</div>
          )}
          {paginatedRows.map(row => {
            const selectCell = row.getVisibleCells().find(cell => cell.column.id === 'select');
            const actionsCell = row.getVisibleCells().find(cell => cell.column.id === 'actions');
            const dataCells = row.getVisibleCells().filter(
              cell => cell.column.id !== 'select' && cell.column.id !== 'actions' && cell.column.id !== 'name'
            );
            return (
              <div
                key={row.id}
                className={`bg-card text-card-foreground border rounded-lg p-4 space-y-3 relative ${row.getIsSelected() ? "ring-2 ring-primary" : ""}`}
                data-state={row.getIsSelected() ? "selected" : undefined}
              >
                <div className="flex justify-between items-start mb-2">
                  <label className="flex items-center space-x-3">
                    {selectCell && flexRender(selectCell.column.columnDef.cell, selectCell.getContext())}
                    <span className="font-bold text-lg cursor-pointer hover:text-primary break-words">
                      {row.getValue("name") || row.getValue("receipt_number")}
                    </span>
                  </label>
                  <div className="absolute top-2 right-2">
                    {actionsCell && flexRender(actionsCell.column.columnDef.cell, actionsCell.getContext())}
                  </div>
                </div>
                <hr className="border-border" />
                <div className="space-y-3 pt-2">
                  {dataCells.map(cell => (
                    <div key={cell.id} className="grid grid-cols-[110px,1fr] items-center text-sm gap-x-4">
                      <div className="font-medium text-muted-foreground">
                        {formatHeader(cell.column.id)}
                      </div>
                      <div className="text-right break-words">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>
    </div>
  );
}