export interface LoginPayload{
    email: string
    password: string
    captcha_token: string
}

export interface LoginResponse{
    token: string
}

// interface UserInfo {
//     email: string,
//     id: string,
//     admin: boolean,
//     store_id?: string,
//     is_subscriber: boolean,
//     plan_id: number,
// }



export interface UserSchema {
  name: string;
  email: string;
  password: string;
  roleId: number;
  profile?: string;
  planId: number;
  storeId: string;
  userDefinedRoleId?: number;
}
