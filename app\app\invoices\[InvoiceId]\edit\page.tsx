"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { InvoiceForm } from "@/app/app/invoices/InvoiceForm";
import { Customer } from "@/lib/api/customers/models";
import { fetchCustomers } from "@/lib/api/customers/service";
import { getInvoiceById, updateInvoice } from "@/lib/api/invoices/services";
import { Invoice, InvoiceItem, AddInvoicePayload, PaymentStatus } from "@/lib/api/invoices/models"; //Import InvoiceItem
import { v4 as uuidv4 } from 'uuid';


export default function EditInvoicePage({ params }: { params: { InvoiceId: string } }) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [initialData, setInitialData] = useState<AddInvoicePayload | null>(null);
  //const [invoice, setInvoiceData] = useState<Invoice | null>(null);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      if (token && store_id) {
        try {
          const [customers, invoice] = await Promise.all([
            fetchCustomers(token, store_id),
            getInvoiceById(token, store_id, params.InvoiceId),
          ]);
          setCustomers(customers);

          const adaptedInvoice: AddInvoicePayload = {
            invoice_number: invoice.invoice_number,
            invoice_date: invoice.invoice_date,
            dueDate: invoice.due_date || undefined,
            customer_id: invoice.customer_id,
            store_id: invoice.store_id,
            subtotal: invoice.subtotal.toFixed(2),
            tax_amount: invoice.tax_amount.toFixed(2),
            total_amount: invoice.total_amount.toFixed(2),
            payment_status: invoice.payment_status,
            payment_date: invoice.payment_date || undefined,
            notes: invoice.notes || undefined,
            terms: invoice.terms || undefined,
            has_tax: invoice.has_tax,
            items: invoice.items.map(item => ({
              invoiceId: item.invoice_id,
              item_name: item.item_name,
              quantity: item.quantity,
              price: item.price,
            })),
          };
          setInitialData(adaptedInvoice);

        } catch (error) {
          console.error("Failed to fetch data:", error);
          //Potentially set some error state here
        }
      }
    }
    fetchData();
  }, [token, store_id, params.InvoiceId]);

  const handleSubmit = async (data: AddInvoicePayload) => {
    if (!token || !store_id) {
      return;
    }

    const invoiceData: Invoice = {
        id: params.InvoiceId,
        invoice_number: data.invoice_number,
        invoice_date: data.invoice_date,
        due_date: data.dueDate,
        customer_id: data.customer_id,
        store_id: data.store_id,
        subtotal: parseFloat(data.subtotal),
        tax_amount: parseFloat(data.tax_amount),
        total_amount: parseFloat(data.total_amount),
        payment_status: data.payment_status as PaymentStatus,
        payment_date: data.payment_date,
        notes: data.notes,
        terms: data.terms,
        has_tax: data.has_tax,
        items: data.items.map(item => {
          const invoiceItem: InvoiceItem = {
            id: uuidv4(),
            invoice_id: params.InvoiceId,
            item_name: item.item_name,
            quantity: item.quantity,
            price: item.price
          };
          return invoiceItem;
        })
    }

    try {
        await updateInvoice(token, store_id, params.InvoiceId, invoiceData);
        router.push(`/app/invoices/${params.InvoiceId}`);
    } catch (error) {
        console.error("Failed to update invoice", error);
        throw error;
    }

  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <InvoiceForm
      initialData={initialData}
      onSubmit={handleSubmit}
      customers={customers}
      isEditing={true}
    />
  );
}
