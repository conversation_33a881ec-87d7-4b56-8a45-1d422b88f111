import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Mail, Lock, LogIn } from 'lucide-react';
import Link from 'next/link';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

interface LoginFormProps {
  onSubmit: (captcha_token: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const { control, watch } = useFormContext();
  const email = watch('user.email');
  const password = watch('user.password');
  const { executeRecaptcha } = useGoogleReCaptcha();

  const isFormValid = email?.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && password?.length >= 8;

  const onFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;
    if (!executeRecaptcha) return;
    const captcha_token = await executeRecaptcha('login');
    onSubmit(captcha_token);
  };

  return (
    <div className="bg-card rounded-lg border p-4 shadow-sm max-w-md w-full mx-auto sm:p-6 sm:rounded-xl font-sans">
      <h2 className="text-2xl font-bold mb-4 text-center">Sign In</h2>
      <p className="text-muted-foreground mb-4 text-center text-base">Enter your credentials to access your account</p>
      <form onSubmit={onFormSubmit} className="space-y-4">
        {/* Email Field */}
        <FormField
          control={control}
          name="user.email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Mail size={18} />
                </div>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Password Field */}
        <FormField
          control={control}
          name="user.password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Lock size={18} />
                </div>
                <FormControl>
                  <Input type="password" placeholder="Your password" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full mt-4" disabled={!isFormValid}>
          Sign In
          <LogIn className="ml-2 h-4 w-4" />
        </Button>
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mt-4">
          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground">Don't have an account?</span>
            <Link href="/auth/register" className="text-sm text-primary hover:underline">Register</Link>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground">Forgot your password?</span>
            <Link href="/auth/password-reset-request" className="text-sm text-primary hover:underline">Reset Password</Link>
          </div>
        </div>
        <p className="text-xs text-muted-foreground mt-4 text-center">
          By signing in, you agree to our Terms of Service and Privacy Policy
        </p>
      </form>
    </div>
  );
};

export default LoginForm;
