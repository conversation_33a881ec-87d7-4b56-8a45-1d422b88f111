'use client';

import Link from 'next/link';
import Image from 'next/image';
import { cn } from "@/lib/utils";
import { motion } from 'framer-motion';
import { Home, ArrowLeft, RefreshCw, HelpCircle, ChevronRight } from 'lucide-react';

export default function NotFound() {
  const errorMessages = [
    "The page you're looking for doesn't exist or has been moved.",
    "This link might be broken or the page may have been removed.",
    "You might have mistyped the address or the page may have moved.",
    "We couldn't find the page you requested. It might have been relocated."
  ];

  const randomMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)];

  return (
    <div className="h-screen w-full overflow-hidden bg-background">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-small-black/[0.2] -z-10" />

      {/* Main content */}
      <div className="relative z-10 flex h-screen flex-col items-center justify-center px-6">
        <div className="w-full max-w-3xl">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-8 sm:p-12 relative overflow-hidden">
            {/* Logo Section */}
            <div className="flex justify-center mb-8">
              <Image
                src="/logo.png"
                alt="StoreYako"
                width={150}
                height={50}
                className="h-auto w-auto"
              />
            </div>

            {/* Header section */}
            <div className="mb-12 text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: "spring", stiffness: 100 }}
                className="w-20 h-20 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center"
              >
                <span className="text-4xl font-bold">404</span>
              </motion.div>

              <motion.h1
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="mb-3 text-4xl font-bold tracking-tight"
              >
                Page Not Found
              </motion.h1>

              <motion.p
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-lg text-muted-foreground"
              >
                {randomMessage}
              </motion.p>
            </div>

            {/* Suggested actions */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="mb-10"
            >
              <h2 className="text-xl font-semibold mb-4">Try one of these instead:</h2>

              <div className="space-y-3">
                <Link
                  href="/"
                  className="flex items-center p-4 rounded-lg border bg-card hover:bg-accent transition-colors group"
                >
                  <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mr-4">
                    <Home className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium">Return to Home</h3>
                    <p className="text-sm text-muted-foreground">Start over from our homepage</p>
                  </div>
                  <ChevronRight className="h-5 w-5" />
                </Link>

                <button
                  onClick={() => window.history.back()}
                  className="flex w-full items-center p-4 rounded-lg border bg-card hover:bg-accent transition-colors group text-left"
                >
                  <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mr-4">
                    <ArrowLeft className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium">Go Back</h3>
                    <p className="text-sm text-muted-foreground">Return to the previous page</p>
                  </div>
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            </motion.div>

            {/* Help and contact */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-center pt-6 border-t"
            >
              <Link
                href="/help"
                className="inline-flex items-center text-lg hover:text-accent-foreground transition-colors"
              >
                <HelpCircle className="mr-2 h-5 w-5" />
                <span>Need help? Contact our support team</span>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
