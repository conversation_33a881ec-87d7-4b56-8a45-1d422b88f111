import { BASE_URL } from "@/app/configs/constants";
import { Returns } from "../../items/returns/models";
import { Refund, RefundSchema } from "./models";


const handleApiError = async (response: Response) => {
    const errorData = await response.json();
    throw new Error(errorData.error || `API Error: ${response.status} - ${response.statusText}`);
};

export async function fetchRefunds(token: string | undefined, store_id: string | undefined): Promise<Refund[]> {
    if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return handleApiError(response);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching refunds:", error);
        throw error;
    }
}

export async function fetchRefund(token: string | undefined, store_id: string | undefined, refund_id: string | undefined): Promise<Refund> {
    if (!token || !store_id || !refund_id) {
        throw new Error("Authentication token, store ID, or refund ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}/${refund_id}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return handleApiError(response);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching refund:", error);
        throw error;
    }
}

export async function createRefund(token: string | undefined, store_id: string | undefined, refund: RefundSchema): Promise<Refund> {
    if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(refund),
        });

        if (!response.ok) {
            return handleApiError(response);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error creating refund:", error);
        throw error;
    }
}

export async function updateRefund(token: string | undefined, store_id: string | undefined, refund_id: string | undefined, refund: RefundSchema): Promise<Refund> {
    if (!token || !store_id || !refund_id) {
        throw new Error("Authentication token, store ID, or refund ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}/${refund_id}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(refund),
        });

        if (!response.ok) {
            return handleApiError(response);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error updating refund:", error);
        throw error;
    }
}

export async function deleteRefund(token: string | undefined, store_id: string | undefined, refund_id: string | undefined): Promise<void> {
    if (!token || !store_id || !refund_id) {
        throw new Error("Authentication token, store ID, or refund ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}/${refund_id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return handleApiError(response);
        }
        // No content on successful delete, so we return void
    } catch (error) {
        console.error("Error deleting refund:", error);
        throw error;
    }
}

export async function fetchPendingRefunds(token: string | undefined, store_id: string | undefined): Promise<Returns[]> {
    if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
    }

    try {
        const response = await fetch(`${BASE_URL}/sales/refunds/${store_id}/pending`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return handleApiError(response);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error fetching pending refunds:", error);
        throw error;
    }
}