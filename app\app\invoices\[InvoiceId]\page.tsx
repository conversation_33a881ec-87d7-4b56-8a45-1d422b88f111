"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { Invoice } from "@/lib/api/invoices/models";
import { getInvoiceById } from "@/lib/api/invoices/services";
import InvoiceTemplate from "./pdfTemplate";
import { Store as RetailStore } from "@/lib/api/retailStores/models";
import { Customer } from "@/lib/api/clients/models";
import { getStore } from "@/lib/api/retailStores/service";
import { getRetailStoreClient } from "@/lib/api/clients/service";
import { useRouter } from "next/navigation";

export default function InvoicePage({ params }: { params: { InvoiceId: string } }) {
  const [invoice, setInvoiceData] = useState<Invoice | null>(null);
  const [store, setStoreData] = useState<RetailStore | null>(null);
  const [customer, setCustomerData] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    const fetchInvoiceData = async () => {
      if (!token || !store_id) {
        setError("Authentication token or store ID is missing.");
        setIsLoading(false);
        return;
      }

      try {
        const fetchedInvoice = await getInvoiceById(token, store_id, params.InvoiceId);
        setInvoiceData(fetchedInvoice);

        const fetchedStore = await getStore(store_id, token);
        setStoreData(fetchedStore);

        if (fetchedInvoice?.customer_id) {
          const fetchedCustomer = await getRetailStoreClient(
            token,
            store_id,
            fetchedInvoice.customer_id.toString() // Ensure customer_id is a string
          );
          setCustomerData(fetchedCustomer);
        }
      } catch (err) {
        setError("Failed to fetch invoice data.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [token, store_id, params.InvoiceId]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!invoice || !store || !customer) {
    return <div>No invoice, store, or customer data found.</div>;
  }
  
  return (
    <div className="p-6">
      
      <div className="mt-6">
        <InvoiceTemplate invoice={invoice} store={store} customerName={customer.name} />
      </div>

    </div>
  );
}
