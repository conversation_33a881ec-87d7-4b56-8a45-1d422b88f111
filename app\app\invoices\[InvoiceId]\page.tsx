// app/app/invoices/[InvoiceId]/page.tsx (or appropriate path)
"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import Link from "next/link";
import { Invoice } from "@/lib/api/invoices/models";
import { getInvoiceById } from "@/lib/api/invoices/services";
import InvoiceTemplate from "./pdfTemplate";
import { Store as RetailStore } from "@/lib/api/retailStores/models";
import { Customer } from "@/lib/api/clients/models";
import { getStore } from "@/lib/api/retailStores/service";
import { getRetailStoreClient } from "@/lib/api/clients/service";
import { Button } from "@/components/ui/button";

export default function InvoicePage({ params }: { params: { InvoiceId: string } }) {
  const [invoice, setInvoiceData] = useState<Invoice | null>(null);
  const [store, setStoreData] = useState<RetailStore | null>(null);
  const [customer, setCustomerData] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isDataMissing, setIsDataMissing] = useState(false);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  const invoiceId = params.InvoiceId;

  useEffect(() => {
    const fetchInvoiceData = async () => {
      setError(null);
      setIsDataMissing(false);
      setIsLoading(true);

      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      try {
        const [fetchedInvoice, fetchedStore] = await Promise.all([
          getInvoiceById(token, store_id, invoiceId),
          getStore(store_id, token)
        ]);

        setInvoiceData(fetchedInvoice);
        setStoreData(fetchedStore);

        let fetchedCustomer: Customer | null = null;
        if (fetchedInvoice?.customer_id) {
          try {
            fetchedCustomer = await getRetailStoreClient(
              token,
              store_id,
              fetchedInvoice.customer_id.toString()
            );
            setCustomerData(fetchedCustomer);
          } catch (customerError) {
            console.error("Failed to fetch customer details:", customerError);
            setCustomerData(null);
          }
        } else {
          setCustomerData(null);
        }

        if (!fetchedInvoice || !fetchedStore) {
          setIsDataMissing(true);
        }

      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to fetch invoice data."));
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [token, store_id, invoiceId]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading invoice details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Invoice</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/invoices">
              <Button variant="link">Return to Invoices</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isDataMissing || !invoice || !store) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Invoice Not Found</h2>
          <p className="text-gray-600 text-center">
            The requested invoice ({invoiceId}) or associated store data could not be found. It might have been deleted or the ID is incorrect.
          </p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/invoices">
              <Button variant="link">Return to Invoices</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/invoices" className="hover:text-blue-600">Invoices</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">
              Invoice {invoice.invoice_number || `#${invoice.id}` || invoiceId}
            </span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6">
        <InvoiceTemplate
          invoice={invoice}
          store={store}
          customerName={customer?.name ?? 'N/A'}
        />
      </div>
    </div>
  );
}