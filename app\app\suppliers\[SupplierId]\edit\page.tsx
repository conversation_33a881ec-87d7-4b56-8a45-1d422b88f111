"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { SupplierForm } from '@/app/app/suppliers/SupplierForm';
import { type SupplierSchema } from '@/lib/api/suppliers/models';
import { getSupplier, updateSupplier } from '@/lib/api/suppliers/service';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function EditSupplierPage({ params }: { params: { SupplierId: string } }) {
  const router = useRouter();
  const [initialData, setInitialData] = useState<SupplierSchema | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false);
  const [supplierName, setSupplierName] = useState<string>("");

  const token = Cookies.get('auth_token');
  const store_id = Cookies.get('active_store');

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      setIsNotFound(false);

      try {
        const supplier = await getSupplier(token, store_id, params.SupplierId);
        setInitialData(supplier);
        setSupplierName(supplier.name);
      } catch (err: any) {
        if (err.message === "Supplier not found") {
          setIsNotFound(true);
        } else {
          setError(err);
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [token, store_id, params.SupplierId]);

  const handleSubmit = async (data: SupplierSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await updateSupplier(token, store_id, params.SupplierId, data);
    router.push('/app/suppliers');
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading supplier details for editing...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Supplier Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/suppliers">
              <Button variant="link">Return to Suppliers List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Supplier Not Found</h2>
          <p className="text-gray-600 text-center">The supplier with ID ({params.SupplierId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/suppliers">
              <Button variant="link">Return to Suppliers List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Supplier data could not be prepared for editing.</p>
        <Link href="/app/suppliers" className="ml-4">
          <Button variant="link">Return to Suppliers List</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/suppliers" className="hover:text-blue-600">Suppliers</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit: {supplierName || `Supplier ${params.SupplierId}`}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6 flex justify-center">
        <div className="w-full max-w-2xl">
          <SupplierForm
            initialData={initialData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isEditing={true}
          />
        </div>
      </div>
    </div>
  );
}