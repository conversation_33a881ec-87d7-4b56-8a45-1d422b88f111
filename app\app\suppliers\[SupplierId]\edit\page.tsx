"use client";

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Cookies from 'js-cookie';
import { SupplierForm } from '@/app/app/suppliers/SupplierForm';
import { type SupplierSchema } from '@/lib/api/suppliers/models';
import { getSupplier, updateSupplier } from '@/lib/api/suppliers/service';

export default function EditSupplierPage({params}: {params: {SupplierId: string}}) {
  const router = useRouter();
  const [initialData, setInitialData] = useState<SupplierSchema | null>(null);

  const token = Cookies.get('auth_token');
  const store_id = Cookies.get('active_store');

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const supplier = await getSupplier(token, store_id, params.SupplierId);
          setInitialData(supplier);
        } catch (error) {
          console.error('Failed to fetch supplier:', error);
        }
      }
    };
    fetchData();
  }, [token, store_id, params.SupplierId]);

  const handleSubmit = async (data: SupplierSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await updateSupplier(token, store_id, params.SupplierId, data);
    router.push('/app/suppliers');
  };

  const handleCancel = () => {
    router.back();
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <SupplierForm
      initialData={initialData}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isEditing={true}
    />
  );
}
