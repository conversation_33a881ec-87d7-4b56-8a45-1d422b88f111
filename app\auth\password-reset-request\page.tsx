"use client"

import { useState } from "react"
import { FormProvider, useForm } from 'react-hook-form';
import PasswordResetRequestForm from './components/PasswordResetRequestForm';
import { Alert, AlertDescription } from "@/components/ui/alert"

interface FormErrors {
  general?: string
}

interface ResetRequestPayload {
  user: {
    email: string
  }
}

export default function PasswordResetRequestPage() {
  const methods = useForm({ defaultValues: { user: { email: '' } } });
  const [isSuccess, setIsSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const onSubmit = async () => {
    setIsLoading(true);
    try {
      // Your password reset request API call will go here
      // await requestPasswordReset(methods.getValues().user)
      setIsSuccess(true);
    } catch (error) {
      setErrors({
        general: error instanceof Error
          ? error.message
          : "An error occurred while sending the reset link. Please try again."
      })
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center px-6 py-8 mx-auto">
        <div className="w-full bg-white rounded-lg shadow dark:border sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
            <Alert>
              <AlertDescription>
                If an account exists with this email address, you will receive password reset instructions shortly. 
                Please check your email and follow the instructions to reset your password.
              </AlertDescription>
            </Alert>
            <div className="text-center">
              <a
                href="/auth/login"
                className="font-medium text-blue-600 hover:underline dark:text-blue-500"
              >
                Return to login
              </a>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-6 py-8 mx-auto">
      <div className="w-full bg-white rounded-lg shadow dark:border sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
            Reset Your Password
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Enter your email address and we&apos;ll send you instructions to reset your password.
          </p>
          {errors.general && (
            <Alert variant="error">
              <AlertDescription>{errors.general}</AlertDescription>
            </Alert>
          )}
          <FormProvider {...methods}>
            <PasswordResetRequestForm onSubmit={methods.handleSubmit(onSubmit)} isLoading={isLoading} />
          </FormProvider>
        </div>
      </div>
    </div>
  )
}