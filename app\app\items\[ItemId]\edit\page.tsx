"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Cookies from "js-cookie";
import { ItemForm } from "@/app/app/items/ItemForm";
import { Category } from "@/lib/api/categories/models";
import { Supplier } from "@/lib/api/suppliers/models";
import { Brand } from "@/lib/api/brands/models";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchItem, updateItem } from "@/lib/api/items/service";
import { Item, ItemSchema } from "@/lib/api/items/models";

export default function EditItemPage({ params }: { params: { ItemId: string } }) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [initialData, setInitialData] = useState<ItemSchema | null>(null);


  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    if (!token || !store_id || !params.ItemId) return;

    const fetchData = async () => {
      try {
        const [categoriesData, suppliersData, brandsData, item] = await Promise.all([
          fetchCategories(token, store_id),
          fetchSuppliers(token, store_id),
          fetchBrands(token, store_id),
          fetchItem(token, store_id, params.ItemId),
        ]);

        const tagsArray = item.tags ? Object.keys(item.tags) : [];

        const itemSchema: ItemSchema = {
          name: item.name,
          quantity: item.quantity,
          default_cost: item.default_cost,
          default_price: item.default_price,
          brand: item.brand,
          has_discount: item.has_discount,
          date_created: item.date_created,
          tags: tagsArray,
          category: item.category,
          store_id: store_id,
          vendor_id: item.vendor_id || "",
          image: item.image || "",
          description: item.description || "",
          sku: item.sku || "",
          barcode: item.barcode || "",
          discount: item.discount || undefined,
          notes: item.notes || "",
          is_variant: item.is_variant,
          parent_item_id: item.parent_item_id || null,
        };

        setCategories(categoriesData);
        setSuppliers(suppliersData);
        setBrands(brandsData);
        setInitialData(itemSchema);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    };

    fetchData();
  }, [token, store_id, params.ItemId]);

  const handleSubmit = async (data: ItemSchema[]) => {
    if (!token || !store_id || !params.ItemId) {
      throw new Error("Authentication token, store ID, or item ID is missing.");
    }

    await updateItem(token, store_id, params.ItemId, data[0]);
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <ItemForm
      initialData={initialData}
      onSubmit={handleSubmit}
      categories={categories}
      suppliers={suppliers}
      brands={brands}
      isEditing={true}
    />
  );
}