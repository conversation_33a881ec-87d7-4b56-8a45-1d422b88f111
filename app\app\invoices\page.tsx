"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { ChevronDown, Trash2, Plus, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";

import { getRetailStoreClients } from "@/lib/api/clients/service";
import { Customer } from "@/lib/api/clients/models";

import { fetchInvoices } from "@/lib/api/invoices/services";
import { Invoice } from "@/lib/api/invoices/models";
import Cookies from "js-cookie";

import { getColumns } from "./columns";
import { useRouter } from "next/navigation";
import { getStore } from "@/lib/api/retailStores/service";
import { Store } from "@/lib/api/retailStores/models";
import { deleteCustomer } from "@/lib/api/customers/service";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function InvoicesTable() {
  const router = useRouter();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [store, setStore] = useState<Store | null>(null);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const [data, setInvoices] = useState<Invoice[]>([]);
  const [invoices, setCustomers] = useState<Customer[]>([]);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    async function getInvoices() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const fetchedInvoices = await fetchInvoices(token, store_id);
          setInvoices(fetchedInvoices);
          const fetchedCustomers = await getRetailStoreClients(token, store_id);
          setCustomers(fetchedCustomers);
          const fetchedStore = await getStore(store_id, token);
          setStore(fetchedStore);
        } catch (error) {
          console.error("Error fetching invoices:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }
    getInvoices();
  }, [store_id, token]);

  const handleDeleteSelected = async () => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedCustomerIds = selectedRows.map((row) => row.original.id);

    try {
      for (const invoiceId of selectedCustomerIds) {
        await deleteCustomer(token, store_id, invoiceId);
        router.refresh();
      }
      setCustomers((prev) =>
        prev.filter((invoice) => !selectedCustomerIds.includes(invoice.id)),
      );
      setRowSelection({});
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting invoices:", error);
    }
  };

  //const columns = getColumns({ customers: invoices, router, store: store! });

  const columns = React.useMemo(() => getColumns({ customers: invoices, router, store: store! }), [invoices, router, store]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const sortedRows = table.getSortedRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = sortedRows.slice(start, end);

  const showSelectedItems = (
    <ul className="mt-2 space-y-1">
      {table.getSelectedRowModel().rows.map((row) => (
        <li key={row.id} className="text-sm text-muted-foreground">• {row.original.invoice_number}</li>
      ))}
    </ul>
  );

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
        <Input
          placeholder="Filter invoices..."
          value={(table.getColumn("invoice_number")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("invoice_number")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm" // Full width on mobile, max-width on larger screens
          disabled={isInitialLoading}
        />

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0"> {/* Adjusted margin top */}
          <Button
            onClick={() => router.push("/app/invoices/create")}
            className="w-full" // Full width on mobile
            disabled={isInitialLoading}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Invoice
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border relative w-full overflow-auto  grid grid-cols-1">
        <Table className="w-full caption-bottom text-sm">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isInitialLoading ? (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  <LoadingSpinner />
                </TableCell>
              </TableRow>
            ) : paginatedRows.length ? (
              paginatedRows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <p>No invoices found.</p>
                    <Button
                      variant="link"
                      onClick={() => router.push("/app/invoices/create")}
                      className="mt-2"
                    >
                      Create your first invoice
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {isInitialLoading ? (
            "Loading..."
          ) : (
            `${table.getFilteredSelectedRowModel().rows.length} of 
             ${table.getFilteredRowModel().rows.length} row(s) selected.`
          )}
        </div>
        <div className="space-x-2">
          {Object.keys(rowSelection).length > 0 && (
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Invoices</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete the selected invoice(s)?
                    {showSelectedItems}
                    <p className="mt-2 text-sm font-medium text-destructive">
                      This action cannot be undone.
                    </p>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteSelected}>
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>
    </div>
  );
}