// "use client";
//
// import { Button } from "@/components/ui/button";
// import {
//   Card,
//   CardContent,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import {
//   Select,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
//   SelectContent,
// } from "@/components/ui/select";
// import { NewStaff } from "@/lib/api/users/models";
// import { UserDefinedRoles } from "@/lib/types/user_roles";
// import { useRouter } from "next/navigation"; // Updated import
// import { useState } from "react";
//
// export default function CreateStaffForm() {
//   const router = useRouter();
//   const [loading, setLoading] = useState(false);
//   const [errors, setErrors] = useState<Partial<Record<keyof NewStaff, string>>>(
//     {},
//   );
//
//   const [formData, setFormData] = useState<NewStaff>({
//     name: "",
//     email: "",
//     user_defined_role_id: UserDefinedRoles.Staff, // Set default role
//     profile: "",
//   });
//
//   const validateForm = (): boolean => {
//     const newErrors: Partial<Record<keyof NewStaff, string>> = {};
//
//     if (!formData.name.trim()) {
//       newErrors.name = "Name is required";
//     }
//
//     if (!formData.email.trim()) {
//       newErrors.email = "Email is required";
//     } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
//       newErrors.email = "Invalid email format";
//     }
//
//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };
//
//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//
//     if (!validateForm()) {
//       return;
//     }
//
//     setLoading(true);
//     try {
//       // Replace with your actual API call
//       await new Promise((resolve) => setTimeout(resolve, 2000));
//       router.push("/users");
//     } catch (error) {
//       console.error("Failed to create staff:", error);
//       setErrors({ submit: "Failed to create staff. Please try again." });
//     } finally {
//       setLoading(false);
//     }
//   };
//
//   const handleCancel = () => {
//     router.back();
//   };
//
//   // Helper function to get role name from enum
//   const getRoleName = (roleId: number): string => {
//     return (
//       Object.keys(UserDefinedRoles).find(
//         (key) =>
//           UserDefinedRoles[key as keyof typeof UserDefinedRoles] === roleId,
//       ) || ""
//     );
//   };
//
//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Create Staff</CardTitle>
//       </CardHeader>
//       <form onSubmit={handleSubmit}>
//         <CardContent className="space-y-4">
//           <div className="space-y-2">
//             <Label htmlFor="name">Name *</Label>
//             <Input
//               id="name"
//               type="text"
//               value={formData.name}
//               onChange={(e) =>
//                 setFormData({ ...formData, name: e.target.value })
//               }
//               placeholder="Enter staff name"
//               aria-invalid={!!errors.name}
//             />
//             {errors.name && (
//               <p className="text-sm text-red-500">{errors.name}</p>
//             )}
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="email">Email *</Label>
//             <Input
//               id="email"
//               type="email"
//               value={formData.email}
//               onChange={(e) =>
//                 setFormData({ ...formData, email: e.target.value })
//               }
//               placeholder="Enter staff email"
//               aria-invalid={!!errors.email}
//             />
//             {errors.email && (
//               <p className="text-sm text-red-500">{errors.email}</p>
//             )}
//           </div>
//
//           <div className="space-y-2">
//             <Label htmlFor="role">User Defined Role</Label>
//             <Select
//               value={formData.user_defined_role_id.toString()}
//               onValueChange={(value) =>
//                 setFormData((prev) => ({
//                   ...prev,
//                   user_defined_role_id: Number(value),
//                 }))
//               }
//             >
//               <SelectTrigger id="role">
//                 <SelectValue placeholder="Select Role">
//                   {getRoleName(formData.user_defined_role_id)}
//                 </SelectValue>
//               </SelectTrigger>
//               <SelectContent>
//                 {Object.entries(UserDefinedRoles)
//                   .filter(([key]) => isNaN(Number(key)))
//                   .map(([role, value]) => (
//                     <SelectItem key={role} value={value.toString()}>
//                       {role}
//                     </SelectItem>
//                   ))}
//               </SelectContent>
//             </Select>
//           </div>
//
//           {errors.submit && (
//             <p className="text-sm text-red-500">{errors.submit}</p>
//           )}
//         </CardContent>
//         <CardFooter className="flex justify-end space-x-2">
//           <Button variant="outline" onClick={handleCancel} type="button">
//             Cancel
//           </Button>
//           <Button type="submit" disabled={loading}>
//             {loading ? "Creating..." : "Create"}
//           </Button>
//         </CardFooter>
//       </form>
//     </Card>
//   );
// }


"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { StaffForm } from "@/app/app/users/StaffForm";
import { NewStaff } from "@/lib/api/users/models";
import { getUser, updateUser } from "@/lib/api/users/service";
import Cookies from "js-cookie";

export default function EditStaffPage({params}: {params: {UserId: string}}) {
  const router = useRouter();
  const { staffId } = useParams();
  const [initialData, setInitialData] = useState<NewStaff | null>(null);

  const token = Cookies.get("token");
  const store_id = Cookies.get("store_id");

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const staff = await getUser(token, store_id, params.UserId);
          setInitialData(staff);
        } catch (error) {
          console.error("Failed to fetch staff:", error);
        }
      }
    };
    fetchData();
  }, [token, store_id, staffId]);

  const handleSubmit = async (data: NewStaff) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await updateUser(token, store_id, params.UserId, data);
    router.push("/app/users");
  };

  const handleCancel = () => {
    router.back();
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <StaffForm
      initialData={initialData}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isEditing={true}
    />
  );
}
