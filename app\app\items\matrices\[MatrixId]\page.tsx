"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { fetchAttributes } from "@/lib/api/attributes/attributeService";
import { fetchStoreAttributeValues } from "@/lib/api/attributes/attributeValues";
import { fetchMatrixAttributeValues } from "@/lib/api/attributes/matrixAttributeValues";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ItemMatrix } from "@/lib/api/items/matrices/models";
import { getItemMatrix } from "@/lib/api/items/matrices/service";
import { createItems, fetchItems } from "@/lib/api/items/service";
import Link from "next/link";
import { Item } from "@/lib/api/items/models";
import { Label } from "@/components/ui/label";

export default function ItemGenerator({ params }: { params: { MatrixId: string } }) {
  const [attributes, setAttributes] = useState<{ [key: string]: string[] }>({});
  const [selectedAttributes, setSelectedAttributes] = useState<{ [key: string]: string[] }>({});
  const [customAttributes, setCustomAttributes] = useState<{ [key: string]: string[] }>({});
  const [previouslyCreatedItems, setPreviouslyCreatedItems] = useState<Item[]>([]);
  const [items, setItems] = useState<any[]>([]);
  const [newValue, setNewValue] = useState("");
  const [itemMatrix, setItemMatrix] = useState<ItemMatrix>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const router = useRouter();

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const fetchedAttributes = await fetchAttributes(token, store_id);
        const fetchedAttributeValues = await fetchStoreAttributeValues(token, store_id);
        const fetchedMatrixAttributeValues = await fetchMatrixAttributeValues(token, store_id, params.MatrixId);
        const fetchedMatrix = await getItemMatrix(token, store_id, params.MatrixId);
        const fetchedItems = await fetchItems(token, store_id);
        setItemMatrix(fetchedMatrix);

        const matrixAttributeValueIds = new Set(
          fetchedMatrixAttributeValues.map((val) => val.attribute_value_id)
        );

        const attributesMap = fetchedAttributes.reduce((acc: any, attr: any) => {
          acc[attr.name] = fetchedAttributeValues
            .filter((val) => val.attribute_id === attr.id && matrixAttributeValueIds.has(val.id))
            .map((val) => val.value);
          return acc;
        }, {});

        const items = fetchedItems.filter((item) => item.parent_item_id === params.MatrixId);
        setPreviouslyCreatedItems(items);

        setAttributes(attributesMap);
      } catch (err: any) {
        setError(err instanceof Error ? err : new Error("Failed to fetch required data."));
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [store_id, token, params.MatrixId]);

  const toggleSelection = (attr: string, value: string) => {
    setSelectedAttributes((prev) => {
      const updated = { ...prev };
      updated[attr] = updated[attr] || [];
      updated[attr] = updated[attr].includes(value)
        ? updated[attr].filter((v) => v !== value)
        : [...updated[attr], value];
      return updated;
    });
  };

  const generateItems = () => {
    const attributeKeys = Object.keys({ ...attributes, ...customAttributes });
    if (!attributeKeys.length) return;

    const combinations = attributeKeys.reduce((acc: any, key: any) => {
      const values = selectedAttributes[key] || [" "];
      if (acc.length === 0) return values.map((v) => ({ [key]: v }));
      return acc.flatMap((combo: any) => values.map((v) => ({ ...combo, [key]: v })));
    }, []);

    const existingSkus = previouslyCreatedItems
      .map(item => {
        const match = item.sku?.match(/\d+$/);
        return match ? parseInt(match[0]) : 0;
      })
      .filter(num => !isNaN(num));

    const highestSkuNumber = existingSkus.length > 0 ? Math.max(...existingSkus) : 0;

    const newCombinations = combinations.filter(
      (combo: any) =>
        !previouslyCreatedItems.some(
          (existingItem) =>
            existingItem.name === ` ${itemMatrix?.name} ${Object.values(combo).join(" ")}`.trim()
        )
    );

    const newItems = newCombinations.map((combo: any, index: any) => {
      const skuNumber = highestSkuNumber + index + 1;
      const paddedSku = skuNumber.toString().padStart(3, '0');

      return {
        name: ` ${itemMatrix?.name} ${Object.values(combo).join(" ")}`.trim(),
        sku: paddedSku,
        quantity: 0,
        brand: itemMatrix?.brand,
        category: itemMatrix?.category,
        default_cost: itemMatrix?.default_cost,
        default_price: itemMatrix?.default_price,
        supplier_id: itemMatrix?.vendor_id,
        has_discount: itemMatrix?.has_discount,
        discount: itemMatrix?.discount,
        description: itemMatrix?.description,
        tags: [],
        notes: "",
        barcode: "",
        is_variant: true,
        parent_item_id: itemMatrix?.id,
      };
    });

    setItems(newItems);
  };

  const handleQuantityChange = (index: number, value: number) => {
    const newItems = [...items];
    newItems[index].quantity = value;
    setItems(newItems);
  };

  const handleTagsChange = (index: number, values: string) => {
    const newItems = [...items];
    newItems[index].tags = values.split(",").map((tag: string) => tag.trim());
    setItems(newItems);
  };

  const handleSubmit = async () => {
    try {
      if (token && store_id) {
        await createItems(token, store_id, items);
        router.push("/app/items");
      }
    } catch (error) {
      setError(error instanceof Error ? error : new Error("Failed to create items."));
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading item matrix data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Matrix Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items">
              <Button variant="link">Return to Items List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Generate Items from Matrix</h1>
        <p className="text-gray-500 mt-1">Select attributes to generate new item variants</p>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left Panel - Attributes Selection */}
        <Card className="lg:w-1/3 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Define Attributes</h2>
            <Button
              className="text-sm h-8 px-4"
              onClick={generateItems}
              disabled={Object.keys(selectedAttributes).length === 0}
            >
              Generate Items
            </Button>
          </div>

          <div className="space-y-6">
            {Object.entries({ ...attributes, ...customAttributes }).map(([attr, values]) => (
              <div key={attr} className="space-y-2">
                <h3 className="font-medium text-sm text-gray-700">{attr}</h3>
                <div className="grid grid-cols-2 gap-2">
                  {values.map((value) => (
                    <div key={value} className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-50">
                      <Checkbox
                        checked={selectedAttributes[attr]?.includes(value)}
                        onCheckedChange={() => toggleSelection(attr, value)}
                        className="h-4 w-4"
                      />
                      <span className="text-sm text-gray-600">{value}</span>
                    </div>
                  ))}
                </div>
                {customAttributes[attr] !== undefined && (
                  <div className="mt-2">
                    <Input
                      type="text"
                      value={newValue}
                      onChange={(e) => setNewValue(e.target.value)}
                      placeholder={`Add new ${attr.toLowerCase()}`}
                      className="text-sm h-8"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>

        {/* Right Panel - Generated and Existing Items */}
        <Card className="lg:w-2/3 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Generated Items</h2>
            <Button
              className="text-sm h-8 px-4"
              onClick={handleSubmit}
              disabled={items.length === 0}
            >
              Create Items
            </Button>
          </div>

          {/* Generated Items List */}
          <div className="space-y-4 mb-8">
            {items.map((item, index) => (
              <div key={index} className="border rounded-lg p-4 bg-white shadow-sm">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium text-gray-800">{item.name}</h3>
                    <p className="text-sm text-gray-500">SKU: {item.sku}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setItems(items.filter((_, i) => i !== index))}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600">Quantity</Label>
                    <Input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleQuantityChange(index, parseInt(e.target.value))}
                      className="h-8 mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Price</Label>
                    <div className="text-sm font-medium text-gray-800 mt-1">
                      {item.default_price.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                    </div>
                  </div>
                </div>

                <div className="mt-3">
                  <Label className="text-sm text-gray-600">Tags</Label>
                  <Input
                    type="text"
                    value={item.tags}
                    onChange={(e) => handleTagsChange(index, e.target.value)}
                    placeholder="Enter tags, comma separated"
                    className="h-8 mt-1"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Previously Created Items */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Previously Created Items</h3>
            <div className="space-y-3">
              {previouslyCreatedItems.map((item, index) => (
                <div key={index} className="border rounded-lg p-3 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-800">{item.name}</h4>
                      <p className="text-sm text-gray-500">SKU: {item.sku}</p>
                    </div>
                    <div className="text-sm font-medium text-gray-800">
                      {item.default_price.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}