import { BASE_URL } from "@/app/configs/constants";
import { Category, CategorySchema } from "./models";

export async function fetchCategories(
  authToken: string,
  storeId: string,
): Promise<Category[]> {
  const url = `${BASE_URL}/categories/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch categories: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
}

export async function fetchCategory(
  authToken: string,
  storeId: string,
  categoryId: string,
): Promise<Category> {
  const url = `${BASE_URL}/categories/${storeId}/${categoryId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch category: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching category:", error);
    throw error;
  }
}

export async function CreateCategory(
  authToken: string,
  storeId: string,
  category: CategorySchema,
): Promise<Category> {
  const url = `${BASE_URL}/categories/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(category),
    });

    if (!response.ok) {
      throw new Error(`Failed to create category: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating category:", error);
    throw error;
  }
}

export async function UpdateCategory(
  authToken: string,
  storeId: string,
  category: Category,
): Promise<Category> {
  const url = `${BASE_URL}/categories/${storeId}/${category.id}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "PATCH",
      headers,
      body: JSON.stringify(category),
    });

    if (!response.ok) {
      throw new Error(`Failed to update category: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating category:", error);
    throw error;
  }
}

export async function DeleteCategory(
  authToken: string,
  storeId: string,
  categoryId: string,
): Promise<void> {
  const url = `${BASE_URL}/categories/${storeId}/${categoryId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to delete category: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error deleting category:", error);
    throw error;
  }
}
