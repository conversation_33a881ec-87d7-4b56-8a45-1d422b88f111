/** @type {import('next').NextConfig} */
const nextConfig = {
  // Improve chunk loading for network access
  experimental: {
    optimizePackageImports: ['react-qr-barcode-scanner']
  },

  // Configure webpack for better chunk handling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Improve chunk loading reliability
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            maxSize: 244000, // Smaller chunks for better loading
          },
        },
      };
    }
    return config;
  },

  images: {
    domains: ['127.0.0.1', 'localhost', 'storeyako.com', 'www.storeyako.com', 'storeyako-frontend.vercel.app', 'successive-pheasant-storeyako-41b3accb.koyeb.app'],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '127.0.0.1',
        port: '8000',
        pathname: '/uploads/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
        pathname: '/uploads/**',
      },
      {
        protocol: 'http',
        hostname: 'storeyako.com',
        pathname: '/uploads/**',
      },
      {
        protocol: 'http',
        hostname: 'www.storeyako.com',
        pathname: '/uploads/**',
      },

      {
        protocol: 'https',
        hostname: 'storeyako.com',
        pathname: '/uploads/**',
      },
      {
        protocol: 'https',
        hostname: 'storeyako-frontend.vercel.app',
        pathname: '/uploads/**',
      },
    ],
  },
  

  // async headers() {
  //   return [
  //     {
  //       source: '/(.*)', // Apply this CSP to all routes
  //       headers: [
  //         {
  //           key: 'Content-Security-Policy',
  //           value: `
  //             default-src 'self' http://localhost:* ws://localhost:* http://**************:3000;
  //             script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://www.recaptcha.net https://www.google.com http://localhost:* http://**************:3000;
  //             style-src 'self' 'unsafe-inline' https://www.gstatic.com http://localhost:* http://**************:3000;
  //             img-src 'self' data: https://www.gstatic.com https://successive-pheasant-storeyako-41b3accb.koyeb.app http://localhost:* http://**************:3000;
  //             frame-src https://www.google.com https://recaptcha.google.com http://localhost:* http://**************:3000;
  //             connect-src 'self' https://www.gstatic.com https://www.recaptcha.net https://www.google.com https://successive-pheasant-storeyako-41b3accb.koyeb.app wss://successive-pheasant-storeyako-41b3accb.koyeb.app http://localhost:* ws://localhost:* http://**************:3000;
  //             object-src 'none';
  //             base-uri 'self';
  //             form-action 'self';
  //             frame-ancestors 'none';
  //             upgrade-insecure-requests;
  //           `
  //             .replace(/\s{2,}/g, ' ')
  //             .trim(),
  //         },
  //       ],
  //     },
  //   ];
  // },
  
}

module.exports = nextConfig 