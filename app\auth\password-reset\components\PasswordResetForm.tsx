import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Lock, RefreshCcw } from 'lucide-react';

interface PasswordResetFormProps {
  onSubmit: () => void;
}

const PasswordResetForm: React.FC<PasswordResetFormProps> = ({ onSubmit }) => {
  const { control, watch } = useFormContext();
  const password = watch('user.password');
  const confirmPassword = watch('user.confirmPassword');

  const isFormValid = password?.length >= 8 && password === confirmPassword;

  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid) onSubmit();
  };

  return (
    <div className="bg-card rounded-lg border p-6 shadow-sm">
      <h2 className="text-2xl font-bold mb-6 text-center">Reset Password</h2>
      <p className="text-muted-foreground mb-6 text-center">Enter your new password below</p>
      <form onSubmit={onFormSubmit} className="space-y-4">
        {/* New Password Field */}
        <FormField
          control={control}
          name="user.password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Lock size={18} />
                </div>
                <FormControl>
                  <Input type="password" placeholder="Create a new password" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Confirm Password Field */}
        <FormField
          control={control}
          name="user.confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Lock size={18} />
                </div>
                <FormControl>
                  <Input type="password" placeholder="Confirm your new password" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full mt-6" disabled={!isFormValid}>
          Reset Password
          <RefreshCcw className="ml-2 h-4 w-4" />
        </Button>
        <p className="text-xs text-muted-foreground mt-4 text-center">
          By resetting your password, you agree to our Terms of Service and Privacy Policy
        </p>
      </form>
    </div>
  );
};

export default PasswordResetForm;
