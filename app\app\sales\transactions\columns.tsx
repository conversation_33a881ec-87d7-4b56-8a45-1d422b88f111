import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PaymentTransaction } from "@/lib/api/sales/transactions/models";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export const getColumns = (_router: AppRouterInstance): ColumnDef<PaymentTransaction>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "payment_method",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Payment Method
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("payment_method")}</div>,
    enableSorting: true,
  },
  // {
  //   accessorKey: "merchant_request_id",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Merchant Request ID
  //       <ArrowUpDown />
  //     </Button>
  //   ),
  //   cell: ({ row }) => <div>{row.getValue("merchant_request_id") || "N/A"}</div>,
  //   enableSorting: true,
  // },
  {
    accessorKey: "mpesa_receipt_number",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        M-Pesa Receipt
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("mpesa_receipt_number") ?? "N/A"}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "card_type",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Card Info
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const paymentMethod = row.getValue("payment_method") as string;
      if (paymentMethod === "CARD") {
        const cardType = row.getValue("card_type") as string;
        const lastFour = row.getValue("card_last_four") as string;
        return <div>{cardType} **** {lastFour}</div>;
      }
      return <div>N/A</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "transaction_date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Transaction Date
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const rawDate = row.getValue<string>("transaction_date");
      const formattedDate = new Date(rawDate).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
      });
      return <div>{formattedDate}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "phone_number",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Phone Number
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("phone_number") || "N/A"}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "amount",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Amount
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>KES {row.getValue("amount")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("status")}</div>,
    enableSorting: true,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const transaction = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(transaction.id)}
            >
              Copy Transaction ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View Details</DropdownMenuItem>
            {/* Add other actions as needed */}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];