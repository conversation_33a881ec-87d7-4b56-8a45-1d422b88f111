"use client";

import { useEffect } from 'react';
import { toast } from 'sonner';

export default function ChunkErrorHandler() {
  useEffect(() => {
    // Disable WebSocket reconnection attempts in development
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      // Override WebSocket to prevent excessive reconnection attempts
      const originalConsoleError = console.error;
      console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('Scheduling reconnect attempt') ||
            message.includes('WebSocket connection') ||
            message.includes('HMR')) {
          // Suppress WebSocket reconnection messages
          return;
        }
        originalConsoleError.apply(console, args);
      };
    }

    // Global error handler for chunk loading errors
    const handleChunkError = (event: ErrorEvent) => {
      const error = event.error;
      const message = event.message;

      // Filter out normal barcode scanning "errors"
      if (
        error?.name === 'NotFoundException' ||
        message?.includes('No MultiFormat Readers were able to detect the code') ||
        error?.message?.includes('No MultiFormat Readers were able to detect the code')
      ) {
        // This is normal scanning behavior, not an error
        event.preventDefault();
        return true;
      }

      // Check if it's a chunk loading error
      if (
        error?.name === 'ChunkLoadError' ||
        message?.includes('Loading chunk') ||
        message?.includes('ChunkLoadError') ||
        (error?.message && error.message.includes('Loading chunk'))
      ) {
        console.error('Chunk loading error detected:', error || message);
        
        // Show user-friendly error message
        toast.error('Loading failed. Refreshing page...', {
          description: 'Please wait while we reload the page.',
          duration: 3000,
        });
        
        // Automatically refresh the page after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 2000);
        
        // Prevent the error from propagating
        event.preventDefault();
        return true;
      }
    };

    // Global unhandled promise rejection handler
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const reason = event.reason;
      
      // Check if it's a chunk loading error in a promise
      if (
        reason?.message?.includes('Loading chunk') ||
        reason?.name === 'ChunkLoadError' ||
        (typeof reason === 'string' && reason.includes('Loading chunk'))
      ) {
        console.error('Chunk loading error in promise:', reason);
        
        toast.error('Loading failed. Refreshing page...', {
          description: 'Please wait while we reload the page.',
          duration: 3000,
        });
        
        setTimeout(() => {
          window.location.reload();
        }, 2000);
        
        event.preventDefault();
        return true;
      }
    };

    // Add event listeners
    window.addEventListener('error', handleChunkError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup
    return () => {
      window.removeEventListener('error', handleChunkError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return null; // This component doesn't render anything
}
