"use client";

import React, { useEffect, useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DollarSign,
  Lock,
  Receipt,
  UserPlus,
  Repeat,
  BarChart,
  Globe,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { fetchRolesWithPermissions } from "@/lib/api/roles/service";
import { RoleWithPermissions, Permission } from "@/lib/api/roles/models";
import Cookies from 'js-cookie';

const iconMap: { [key: string]: React.ComponentType } = {
  "Financial Operations": DollarSign,
  "Product Access": Lock,
  "Customer Management": UserPlus,
  "Reporting Access": BarChart,
  "Multi-Store Management": Globe,
  "Print": Receipt,
  "Transaction": Repeat
};

const EmployeeRolesPage = () => {
  const router = useRouter();
  const [rolesData, setRolesData] = useState<RoleWithPermissions[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<RoleWithPermissions[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const token = Cookies.get("auth_token");

        if (!token) {
          setError("Authentication token not found.");
          return;
        }
        const roles = await fetchRolesWithPermissions(token);
        setRolesData(roles);
        setFilteredRoles(roles);
        setLoading(false);
      } catch (err: any) {
        setError(err.message || "Failed to fetch roles.");
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const results = rolesData.filter(role =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredRoles(results);
  }, [searchTerm, rolesData]);

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading roles...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center min-h-screen">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-semibold mb-4 text-gray-800">Roles & Permissions</h1>

      <div className="mb-4">
        <Input
          type="text"
          placeholder="Search Roles"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="rounded-md shadow-sm focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 w-full"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {filteredRoles.map((role, index) => {
          const IconComponent = iconMap[role.name] || DollarSign;
          return (
            <Card key={index} className="shadow-md rounded-lg">
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center space-x-2">
                  <IconComponent className="h-5 w-5" />
                  <span>Role: {role.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-none pl-0">
                  {role.permissions.map((permission, permIndex) => (
                    <li key={permIndex} className="flex items-center space-x-2 py-1 text-sm text-gray-700">
                      <span>{permission.description}</span>
                    </li>
                  ))}
                </ul>
                <Button className="mt-4 text-white rounded-md shadow-sm">
                  Edit Permissions
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Button className="mt-6 text-white rounded-md shadow-sm" onClick={() => { router.push('/app/users/create') }}>
        Add New Employee
      </Button>
    </div>
  );
};

export default EmployeeRolesPage;