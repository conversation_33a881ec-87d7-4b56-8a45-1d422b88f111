"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BrandSchema } from "@/lib/api/brands/models";
import { Textarea } from "@/components/ui/textarea";
import { createBrand } from "@/lib/api/brands/service";
import Cookies from "js-cookie";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";

interface BrandModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: BrandSchema) => Promise<void>;
}

const BrandModal: React.FC<BrandModalProps> = ({
  open,
  onOpenChange,
}) => {
  const [formData, setFormData] = React.useState({
    name: "",
    description: "",
  });
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);

  const token = Cookies.get("token");
  const store_id = Cookies.get("store_id");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
      }

      if (!formData.name) {
        throw new Error("Brand name is required.");
      }

      const payload: BrandSchema = {
        name: formData.name,
        description: formData.description,
      };

      await createBrand(token, store_id, payload);
      setSuccess(true);
      setFormData({ name: "", description: "" });
      onOpenChange(false);
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Brand</DialogTitle>
        </DialogHeader>

        {error && <AlertError message={error} />}
        {success && <AlertSuccess message="Brand added successfully!" />}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              required
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Brand name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brand description"
            />
          </div>

          <DialogFooter className="sm:justify-start">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Brand"}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default BrandModal;
