export enum PaymentStatus {
    Paid = 'Paid',
    Unpaid = 'Unpaid',
}

export interface Invoice {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string;
  customer_id: string;
  store_id: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  payment_status: PaymentStatus;
  payment_date?: string;
  notes?: string;
  terms?: string;
  has_tax: boolean;
  items: InvoiceItem[];
}

export interface InvoiceItem {
    id: string;
    invoice_id: string;
    item_name: string;
    quantity: number;
    price: number;
}

export interface AddInvoicePayload {
  invoice_number: string;
  invoice_date: string;
  dueDate?: string;
  customer_id: string;
  store_id: string;
  subtotal: string;
  tax_amount: string;
  total_amount: string;
  payment_status: PaymentStatus;
  payment_date?: string;
  notes?: string;
  terms?: string;
  has_tax: boolean;
  items: AddInvoiceItemPayload[];
}

export interface AddInvoiceItemPayload {
  invoiceId: string;
  item_name: string;
  quantity: number;
  price: number;
}
