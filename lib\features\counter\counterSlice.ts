import { createSlice } from "@reduxjs/toolkit";

interface CounterState{
    value: number;
}

const initialState: CounterState = {
    value: 0
}


const counterSlice = createSlice({
    name: 'counter',
    initialState,
    reducers: {
        initializeCount: (state, action) => {
            state.value = action.payload;
        },
        increment: (state) =>{
            state.value += 1;
            // mutated state behind the scenes
        },
        decrement: (state) =>{
            state.value -= 1;
        },
    }
});

export const { increment, decrement, initializeCount } = counterSlice.actions;

export default counterSlice.reducer;