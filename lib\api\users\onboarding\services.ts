// lib/api/users/onboarding/service.ts

import { BASE_URL } from '@/app/configs/constants';
import { OnboardingData, OnboardingResponse } from './models';

export async function submitOnboardingData(data: OnboardingData): Promise<OnboardingResponse> {
  try {
    const response = await fetch(`${BASE_URL}/api/v1/users/setup/new`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error: ${response.status}`);
    }

    const responseData: OnboardingResponse = await response.json();
    return responseData;

  } catch (error) {
    console.error("Error submitting onboarding data:", error);
    let message = 'An unexpected error occurred.';
    if (error instanceof Error) {
      message = error.message;
    }
    return { success: false, message };
  }
}
