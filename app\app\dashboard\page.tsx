"use client";

import React, { useState, useEffect } from 'react';
import DailyAverageSales from './DailyAverageSales';
import StorePerformance from './StorePerformance';
import RecentSales from './RecentSales';
import QuickStats from './QuickStats';
import {
    CalendarIcon
} from "@heroicons/react/24/outline";

import { fetchDailyStoreAggregatedAverageSales } from '@/lib/api/reports/service';
import { fetchStoreMetrics } from '@/lib/api/reports/service';
import { getMe } from '@/lib/api/users/service';
import { getStores } from '@/lib/api/retailStores/service';
import { fetchDashboardStats } from '@/lib/api/reports/service';

import { Store } from '@/lib/api/retailStores/models';
import Cookies from 'js-cookie';
import { DailyStoreAggregatedAverageSales, DashboardStats } from '@/lib/api/reports/models';

function LoadingSpinner() {
    return (
        <div className="min-h-screen flex justify-center items-center bg-gray-50">
            <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
                <p className="mt-4 text-gray-600 font-medium">Loading dashboard...</p>
            </div>
        </div>
    );
}

export default function DashboardPage() {
    const [activeTab, setActiveTab] = React.useState('overview');
    const [storePerformanceData, setStorePerformanceData] = useState<Store[] | null>(null);
    const [dailyAverageSalesData, setDailyAverageSalesData] = useState<DailyStoreAggregatedAverageSales | null>(null);
    const [dashboardStatsData, setDashboardStatsData] = useState<DashboardStats | null>(null);

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const token = Cookies.get('auth_token');
    const storeId = Cookies.get('active_store');

    useEffect(() => {
        async function fetchData() {
            if (!token || !storeId) {
                setError("Authentication token or store ID is missing.");
                setLoading(false);
                return;
            }

            try {
                const [
                    storePerformance,
                    dailyAverageSales,
                    dashboardStats,
                ] = await Promise.all([
                    getStores(token),
                    fetchDailyStoreAggregatedAverageSales(token, storeId),
                    getDashboardStats(token, storeId)
                ]);

                setStorePerformanceData(storePerformance);
                setDailyAverageSalesData(dailyAverageSales);
                setDashboardStatsData(dashboardStats);
            } catch (err: any) {
                console.error("Error fetching data:", err);
                setError(err.message || "Failed to load reports. Please try again.");
            } finally {
                setLoading(false);
            }
        }

        fetchData();
    }, [token, storeId]);

    async function getDashboardStats(authToken: string, storeId: string) {
        const user = await getMe(authToken);
        return await fetchDashboardStats(authToken, user.id);
    }

    if (loading) {
        return <LoadingSpinner />;
    }

    if (error) {
        return <div className="p-4 text-red-500">{error}</div>;
    }

    return (
        <div className="p-4">
            {/* Top Bar */}
            <div className="flex items-center justify-between mb-4">
                <h1 className="text-2xl font-bold">Dashboard</h1>
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5 text-gray-500" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">Jan 20, 2023 - Feb 09, 2023</span>
                    </div>
                    <button className="rounded-md bg-blue-500 px-4 py-2 text-sm font-semibold text-white hover:bg-blue-600">
                        Download
                    </button>
                </div>
            </div>

            {/* Content (Overview Tab) */}
            {activeTab === 'overview' && (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-12">
                    {/* Quick Stats (Top Row) */}
                    <QuickStats stats={dashboardStatsData} />

                    {/* Store Performance (Middle Row, 2/3 width) */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-8 rounded-lg border bg-card p-4 shadow-sm min-h-[300px]">
                        <StorePerformance stores={storePerformanceData} />
                    </div>

                    {/* Daily Average Sales (Middle Row, 1/3 width) */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-4 rounded-lg border bg-card p-4 shadow-sm">
                        <DailyAverageSales dailySales={dailyAverageSalesData} />
                    </div>

                    {/* Recent Sales (Bottom Row, Full Width) */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-12 rounded-lg border bg-card p-4 shadow-sm">
                        <RecentSales />
                    </div>
                </div>
            )}
        </div>
    );
}