"use client"

import { useState } from "react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { FormProvider, useForm } from 'react-hook-form';
import PasswordResetForm from './components/PasswordResetForm';

interface FormErrors {
  password?: string
  passwordConfirm?: string
  general?: string
}

interface ResetPasswordPayload {
  password: string
  passwordConfirm: string
  token: string
}

export default function PasswordResetPage() {
  const methods = useForm({ defaultValues: { user: { password: '', confirmPassword: '' } } });
  const [isSuccess, setIsSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const onSubmit = async () => {
    setIsLoading(true);
    try {
      // Extract token from URL query params
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');
      if (!token) throw new Error('Reset token is missing or invalid');
      // await resetPassword({ ...methods.getValues().user, token });
      setIsSuccess(true);
      setTimeout(() => { window.location.href = '/auth/login'; }, 3000);
    } catch (error) {
      setErrors({ general: error instanceof Error ? error.message : 'An error occurred during password reset. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center px-6 py-8 mx-auto">
        <div className="w-full bg-white rounded-lg shadow dark:border sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
            <Alert>
              <AlertDescription>
                Password has been successfully reset. Redirecting to login page...
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-6 py-8 mx-auto">
      <div className="w-full bg-white rounded-lg shadow dark:border sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
          <FormProvider {...methods}>
            <PasswordResetForm onSubmit={methods.handleSubmit(onSubmit)} />
          </FormProvider>
        </div>
      </div>
    </div>
  );
}