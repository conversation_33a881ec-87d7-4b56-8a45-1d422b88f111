import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage";
import { storage } from "./config";

/**
 * Upload a file to Firebase Storage
 * @param file - The file to upload
 * @param folder - The folder path (e.g., 'products', 'store_logos')
 * @param storeId - The store ID for organizing files
 * @param fileName - Optional custom filename, if not provided, will generate one
 * @returns Promise<string> - The download URL of the uploaded file
 */
export async function uploadFileToFirebase(
  file: File,
  folder: string,
  storeId: string,
  fileName?: string
): Promise<string> {
  try {
    // Generate filename if not provided
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    const finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

    // Create storage reference based on folder type
    let storagePath: string;
    if (folder === 'products') {
      // Products: products/<storeId>/<filename>
      storagePath = `${folder}/${storeId}/${finalFileName}`;
    } else if (folder === 'store_logos') {
      // Store logos: store_logos/<storeId>.png (or other extension)
      const logoFileName = `${storeId}.${fileExtension}`;
      storagePath = `${folder}/${logoFileName}`;
    } else {
      // Fallback for other folders
      storagePath = `${folder}/${storeId}/${finalFileName}`;
    }

    const storageRef = ref(storage, storagePath);

    // Upload file
    const snapshot = await uploadBytes(storageRef, file);

    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    console.log('File uploaded successfully to Firebase:', downloadURL);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading file to Firebase:', error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to Firebase Storage
 * @param files - Array of files to upload
 * @param folder - The folder path
 * @param storeId - The store ID for organizing files
 * @returns Promise<string[]> - Array of download URLs
 */
export async function uploadFilesToFirebase(
  files: File[],
  folder: string,
  storeId: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadFileToFirebase(file, folder, storeId));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple files to Firebase:', error);
    throw error;
  }
}

/**
 * Delete a file from Firebase Storage
 * @param fileUrl - The download URL of the file to delete
 * @param folder - The folder path where the file is stored
 * @param storeId - The store ID for organizing files
 */
export async function deleteFileFromFirebase(fileUrl: string, folder: string, storeId: string): Promise<void> {
  try {
    // Extract the path from the URL
    const url = new URL(fileUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)$/);
    if (!pathMatch) {
      throw new Error('Invalid Firebase Storage URL');
    }

    // Decode the path
    const fullPath = decodeURIComponent(pathMatch[1]);

    // Create storage reference using the full path
    const storageRef = ref(storage, fullPath);

    // Delete file
    await deleteObject(storageRef);
    console.log('File deleted successfully from Firebase');
  } catch (error) {
    console.error('Error deleting file from Firebase:', error);
    throw error;
  }
}

/**
 * Get file size limit for uploads (10MB default)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Validate file before upload
 * @param file - File to validate
 * @param allowedTypes - Array of allowed MIME types
 * @param maxSize - Maximum file size in bytes
 */
export function validateFile(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  maxSize: number = MAX_FILE_SIZE
): { isValid: boolean; error?: string } {
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    };
  }
  
  return { isValid: true };
}
