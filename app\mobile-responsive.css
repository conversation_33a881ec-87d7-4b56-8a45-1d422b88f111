/* Mobile-First Responsive Utilities for Capacitor Apps */

/* Touch-friendly minimum sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Mobile-optimized text sizes */
.text-mobile-xs { font-size: 0.75rem; line-height: 1rem; }
.text-mobile-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-mobile-base { font-size: 1rem; line-height: 1.5rem; }
.text-mobile-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-mobile-xl { font-size: 1.25rem; line-height: 1.75rem; }

@media (min-width: 640px) {
  .text-mobile-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-mobile-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .text-mobile-base { font-size: 0.875rem; line-height: 1.25rem; }
  .text-mobile-lg { font-size: 1rem; line-height: 1.5rem; }
  .text-mobile-xl { font-size: 1.125rem; line-height: 1.75rem; }
}

/* Mobile-optimized spacing */
.space-mobile-xs > * + * { margin-top: 0.5rem; }
.space-mobile-sm > * + * { margin-top: 0.75rem; }
.space-mobile-md > * + * { margin-top: 1rem; }
.space-mobile-lg > * + * { margin-top: 1.5rem; }

@media (min-width: 640px) {
  .space-mobile-xs > * + * { margin-top: 0.25rem; }
  .space-mobile-sm > * + * { margin-top: 0.5rem; }
  .space-mobile-md > * + * { margin-top: 0.75rem; }
  .space-mobile-lg > * + * { margin-top: 1rem; }
}

/* Mobile-optimized padding */
.p-mobile-xs { padding: 0.5rem; }
.p-mobile-sm { padding: 0.75rem; }
.p-mobile-md { padding: 1rem; }
.p-mobile-lg { padding: 1.5rem; }

@media (min-width: 640px) {
  .p-mobile-xs { padding: 0.25rem; }
  .p-mobile-sm { padding: 0.5rem; }
  .p-mobile-md { padding: 0.75rem; }
  .p-mobile-lg { padding: 1rem; }
}

/* Mobile-optimized button heights */
.btn-mobile-sm { height: 2.5rem; padding: 0 0.75rem; }
.btn-mobile-md { height: 3rem; padding: 0 1rem; }
.btn-mobile-lg { height: 3.5rem; padding: 0 1.5rem; }

@media (min-width: 640px) {
  .btn-mobile-sm { height: 2.25rem; padding: 0 0.75rem; }
  .btn-mobile-md { height: 2.5rem; padding: 0 1rem; }
  .btn-mobile-lg { height: 2.75rem; padding: 0 1.25rem; }
}

/* Mobile-optimized input heights */
.input-mobile { height: 3rem; padding: 0.75rem 1rem; font-size: 1rem; }

@media (min-width: 640px) {
  .input-mobile { height: 2.5rem; padding: 0.5rem 0.75rem; font-size: 0.875rem; }
}

/* Mobile-optimized grid gaps */
.gap-mobile-xs { gap: 0.5rem; }
.gap-mobile-sm { gap: 0.75rem; }
.gap-mobile-md { gap: 1rem; }
.gap-mobile-lg { gap: 1.5rem; }

@media (min-width: 640px) {
  .gap-mobile-xs { gap: 0.25rem; }
  .gap-mobile-sm { gap: 0.5rem; }
  .gap-mobile-md { gap: 0.75rem; }
  .gap-mobile-lg { gap: 1rem; }
}

/* Mobile-optimized border radius */
.rounded-mobile { border-radius: 0.75rem; }
.rounded-mobile-lg { border-radius: 1rem; }

@media (min-width: 640px) {
  .rounded-mobile { border-radius: 0.5rem; }
  .rounded-mobile-lg { border-radius: 0.75rem; }
}

/* Mobile-optimized shadows */
.shadow-mobile { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.shadow-mobile-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.shadow-mobile-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }

/* Prevent horizontal scroll on mobile */
.mobile-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Mobile-optimized table wrapper */
.mobile-table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
}

.mobile-table-wrapper table {
  min-width: 100%;
  width: max-content;
}

/* Mobile-optimized modal/dialog */
.mobile-dialog {
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
  margin: 0;
}

@media (min-width: 640px) {
  .mobile-dialog {
    width: auto;
    height: auto;
    max-width: 32rem;
    max-height: 90vh;
    border-radius: 0.75rem;
    margin: auto;
  }
}

/* Mobile-optimized form layouts */
.mobile-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .mobile-form-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

/* Mobile-optimized navigation */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid hsl(var(--border));
  padding: 0.75rem;
  z-index: 50;
}

@media (min-width: 768px) {
  .mobile-nav {
    display: none;
  }
}

/* Mobile-optimized card layouts */
.mobile-card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .mobile-card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .mobile-card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile-optimized list items */
.mobile-list-item {
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 4rem;
}

@media (min-width: 640px) {
  .mobile-list-item {
    padding: 0.75rem;
    min-height: 3rem;
    gap: 0.5rem;
  }
}

/* Mobile-optimized search bars */
.mobile-search {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
}

@media (min-width: 640px) {
  .mobile-search {
    position: static;
    padding: 0.75rem;
    border-bottom: none;
    background: transparent;
  }
}

/* Mobile-optimized action buttons */
.mobile-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid hsl(var(--border));
  padding: 1rem;
  display: flex;
  gap: 0.75rem;
  z-index: 40;
}

@media (min-width: 640px) {
  .mobile-action-bar {
    position: static;
    background: transparent;
    border-top: none;
    padding: 0;
  }
}

/* Utility classes for common mobile patterns */
.mobile-full-width { width: 100vw; margin-left: calc(-50vw + 50%); }
.mobile-safe-area-bottom { padding-bottom: env(safe-area-inset-bottom); }
.mobile-safe-area-top { padding-top: env(safe-area-inset-top); }

/* Mobile-optimized focus states */
@media (max-width: 639px) {
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}
