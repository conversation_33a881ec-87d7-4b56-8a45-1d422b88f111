// export interface StoreSchema {
//   name: string;
//   currency?: string;
//   street_address?: string;
//   city?: string;
//   state?: string;
//   postal_code?: string;
//   address?: string;
//   country?: string;
//   phone_number?: string;
//   email?: string;
//   opening_hours?: string;
//   category?: string;
//   //tax_rate?: number;
//   payment_methods?: string;
//   notes?: string;
//   status?: string; // default incomplete
//   image_url?: string;
// }

export interface StoreSchema {
  name: string;
  currency?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  address?: string;
  country?: string;
  phone_number?: string;
  email?: string;
  opening_hours?: string;
  category?: string;
  payment_methods?: string;
  notes?: string;
  status?: string;
  image_url?: string;
  image?: File;
  id?: string
}

export interface Store extends StoreSchema {
  date_created: string
}

export interface Store {
  id: string;
  name: string;
  currency: string;
  city?: string;
  state?: string;
  country?: string;
  address?: string;
  phone_number?: string;
  postal_code?: string;
  street_address?: string;
  opening_hours?: string;
  category?: string;
  email?: string;
  tax_rate?: number;
  payment_methods?: string;
  notes?: string;
  status: string; // 'incomplete' or 'complete'
  image_url?: string;
}
