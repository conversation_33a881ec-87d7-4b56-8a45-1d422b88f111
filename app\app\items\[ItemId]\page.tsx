"use client";

import { fetchItem } from '@/lib/api/items/service';
import { Item } from '@/lib/api/items/models';
import { getStore } from '@/lib/api/retailStores/service';
import { Store } from '@/lib/api/retailStores/models';
import { fetchBrands } from '@/lib/api/brands/service';
import { Brand } from '@/lib/api/brands/models';
import { fetchCategories } from '@/lib/api/categories/service';
import { Category } from '@/lib/api/categories/models';
import { fetchSuppliers } from '@/lib/api/suppliers/service';
import { Supplier } from '@/lib/api/suppliers/models';
import { notFound } from 'next/navigation';
import Cookies from 'js-cookie';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PaymentMethod, SellItemsSchema } from "@/lib/api/sales/models";
import { sellItems } from "@/lib/api/sales/service";
import { Receipt } from "@/lib/api/receipts/models";
import { Loader2, ShoppingCart } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckoutDialog } from "../../../components/CheckoutDialog";

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  has_discount: boolean;
  discount: number;
}

interface ItemPageProps {
  params: {
    ItemId: string;
  };
}

export default function ItemPage({ params }: ItemPageProps) {
  const router = useRouter();
  const itemId = params.ItemId;
  const [item, setItem] = useState<Item | null>(null);
  const [store, setStore] = useState<Store | null>(null);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSellModal, setShowSellModal] = useState(false);
  const [sellQuantity, setSellQuantity] = useState(1);
  const [sellPrice, setSellPrice] = useState(0);
  const [applyDiscount, setApplyDiscount] = useState(false);
  const [sellDiscount, setSellDiscount] = useState(0);
  // Cart state
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [mpesaPhoneNumber, setMpesaPhoneNumber] = useState("");
  const [cartDiscount, setCartDiscount] = useState(0);
  const [itemDiscounts, setItemDiscounts] = useState<{ [itemId: string]: boolean }>({});

  useEffect(() => {
    const authToken = Cookies.get("auth_token");
    const storeId = Cookies.get("active_store");

    const fetchData = async () => {
      if (!authToken || !storeId) {
        notFound();
        return;
      }

      try {
        const fetchedItem = await fetchItem(authToken, storeId, itemId);
        setItem(fetchedItem);
        if (fetchedItem) {
          setSellPrice(fetchedItem.default_price);
          if (fetchedItem.has_discount) setSellDiscount(fetchedItem.discount || 0);
        }
        const fetchedStore = await getStore(storeId, authToken);
        setStore(fetchedStore);

        const fetchedBrands = await fetchBrands(authToken, storeId);
        setBrands(fetchedBrands);

        const fetchedCategories = await fetchCategories(authToken, storeId);
        setCategories(fetchedCategories);

        const fetchedSuppliers = await fetchSuppliers(authToken, storeId);
        setSuppliers(fetchedSuppliers);

      } catch (err: any) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [itemId]);


  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: store?.currency || 'USD',
    }).format(price);
  };

  const handleDelete = async () => {
    const authToken = Cookies.get("auth_token");
    const storeId = Cookies.get("active_store");

    if (!authToken || !storeId) {
      alert("Authentication error. Please log in again.");
      return;
    }
    try {
      const response = await fetch(`/api/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete item');
      }

      router.push('/app/items');
      router.refresh();

    } catch (err: any) {
      console.error("Delete error:", err);
      setError(err);
    } finally {
      setShowDeleteModal(false);
    }
  };

  const handleSell = () => {
    if (!item) return;
    setCart([{
      id: item.id,
      name: item.name,
      price: item.default_price,
      quantity: 1,
      image: item.image,
      has_discount: item.has_discount,
      discount: item.discount || 0
    }]);
    setIsCartOpen(true);
  };

  // Cart functions
  const addToCart = () => {
    if (!item || item.quantity === 0) return;
    setCart([{
      id: item.id,
      name: item.name,
      price: item.default_price,
      quantity: 1,
      image: item.image,
      has_discount: item.has_discount,
      discount: item.discount || 0
    }]);
    setIsCartOpen(true);
  };

  const updateCartItemQuantity = (itemId: string, quantity: number) => {
    if (!item) return;
    setCart(prevCart => {
      const newCart = prevCart.map(cartItem =>
        cartItem.id === itemId
          ? { ...cartItem, quantity: Math.max(1, Math.min(quantity, item.quantity)) }
          : cartItem
      );
      if (newCart[0]?.quantity === 0) {
        setIsCartOpen(false);
        return [];
      }
      return newCart;
    });
  };

  const updateCartItemPrice = (itemId: string, price: number) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId
          ? { ...item, price: Math.max(0, price) }
          : item
      )
    );
  };

  const getItemTotal = (item: CartItem) => {
    const baseTotal = item.price * item.quantity;
    if (itemDiscounts[item.id] && item.has_discount && item.discount) {
      return baseTotal * (1 - item.discount / 100);
    }
    return baseTotal;
  };

  const toggleItemDiscount = (itemId: string) => {
    setItemDiscounts(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const cartTotal = cart.reduce((total, item) => total + getItemTotal(item), 0);

  // Handle checkout
  const handleCheckout = async () => {
    const authToken = Cookies.get("auth_token");
    const storeId = Cookies.get("active_store");

    if (!authToken || !storeId) {
      setErrorMessage("Authentication token or store ID is missing.");
      return;
    }

    if (cart.length === 0) {
      setErrorMessage("Cart is empty.");
      return;
    }

    setIsLoading(true);
    try {
      const sellItemsPayload: SellItemsSchema = {
        items: cart.map(item => ({
          item_uuid: item.id,
          price: itemDiscounts[item.id] && item.has_discount && item.discount 
            ? item.price * (1 - item.discount / 100)
            : item.price,
          quantity: item.quantity,
          store_id: storeId,
        })),
        payment_method: paymentMethod,
        mpesa_payload: paymentMethod === "mpesa" ? {
          amount: cartTotal,
          phone_number: mpesaPhoneNumber,
        } : null,
      };

      if (paymentMethod === "mpesa" && !mpesaPhoneNumber) {
        setErrorMessage("Please enter M-Pesa phone number");
        setIsLoading(false);
        return;
      }

      if (!paymentMethod) {
        setErrorMessage("Please select a payment method");
        setIsLoading(false);
        return;
      }

      const receipt: Receipt = await sellItems(authToken, sellItemsPayload);
      setSuccessMessage(`Successfully sold ${cart.length} items! Receipt Number: ${receipt.receipt_number}`);
      setCart([]);
      setPaymentMethod(null);
      setMpesaPhoneNumber("");
      setIsCartOpen(false);
      router.refresh();
    } catch (error: any) {
      console.error("Error selling items:", error);
      setErrorMessage(error.message || "Failed to sell items.");
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading item details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items">
              <Button variant="link">Return to Items</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!item || !store) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Item Not Found</h2>
          <p className="text-gray-600 text-center">The requested item could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items">
              <Button variant="link">Return to Items</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const brandName = brands.find((b) => b.id === item.brand)?.name || '—';
  const categoryName = categories.find((c) => c.id === item.category)?.name || '—';
  const supplierName = suppliers.find((s) => s.id === item.vendor_id)?.name || '—';

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Success and Error Alerts */}
      {successMessage && (
        <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {successMessage}
        </div>
      )}
      {errorMessage && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {errorMessage}
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      )}

      {/* Breadcrumb navigation */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/items" className="hover:text-blue-600">Items</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">{item.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        {/* Header */}
        <div className="mb-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{item.name}</h1>
            <p className="text-gray-500 text-sm">SKU: {item.sku} • Added on {new Date(item.date_created).toLocaleDateString()}</p>
          </div>
          <Button
            variant="outline"
            onClick={handleSell}
            className="px-5 py-2"
            disabled={item.quantity === 0}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Sell
          </Button>
        </div>

        {/* Main content */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Image column */}
            <div className="md:col-span-1 p-6 flex items-center justify-center bg-gray-50 border-b md:border-b-0 md:border-r border-gray-100">
              {item.image ? (
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-auto rounded-lg object-contain max-h-80"
                />
              ) : (
                <div className="w-full h-64 bg-gray-100 flex flex-col items-center justify-center rounded-lg text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>No Image Available</span>
                </div>
              )}
            </div>

            {/* Details columns */}
            <div className="md:col-span-2 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-4">
                <div>
                  <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Product Information</h2>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500 font-medium">Brand</p>
                      <p className="text-gray-800 text-sm">{brandName}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 font-medium">Category</p>
                      <p className="text-gray-800 text-sm">{categoryName}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 font-medium">Supplier</p>
                      <p className="text-gray-800 text-sm">{supplierName}</p>
                    </div>

                    {item.is_variant && (
                      <div>
                        <p className="text-sm text-gray-500 font-medium">Parent Item ID</p>
                        <p className="text-gray-800 text-sm">{item.parent_item_id}</p>
                      </div>
                    )}

                    <div>
                      <p className="text-sm text-gray-500 font-medium">Description</p>
                      <p className="text-gray-800 text-sm">{item.description || '—'}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 font-medium">Notes</p>
                      <p className="text-gray-800 text-sm">{item.notes || '—'}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Inventory & Pricing</h2>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center bg-blue-50 p-3 rounded-lg">
                      <p className="text-sm text-blue-700 font-medium">Price</p>
                      <p className="text-lg font-bold text-blue-700 text-sm">{formatPrice(item.default_price)}</p>
                    </div>

                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500 font-medium">Cost</p>
                      <p className="text-gray-800 font-medium text-sm">{formatPrice(item.default_cost)}</p>
                    </div>

                    {item.has_discount && (
                      <div className="flex justify-between items-center bg-green-50 p-3 rounded-lg">
                        <p className="text-sm text-green-700 font-medium">Discount</p>
                        <p className="text-green-700 font-medium text-sm">{item.discount}%</p>
                      </div>
                    )}

                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500 font-medium">Quantity</p>
                      <div className="flex items-center">
                        <span className={`inline-block h-3 w-3 rounded-full mr-2 ${item.quantity > 10 ? 'bg-green-500' :
                            item.quantity > 0 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></span>
                        <p className="text-gray-800 text-sm">{item.quantity}</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 font-medium">SKU</p>
                      <p className="text-gray-800 text-sm">{item.sku}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 font-medium">Barcode</p>
                      <p className="text-gray-800 text-sm">{item.barcode || '—'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions at the bottom */}
          <div className="p-4 border-t border-gray-200 flex justify-end gap-3">
              <Button variant="default" className="px-4 py-2" onClick={() => {router.push(`/app/items/${itemId}/edit`);}}>
                Edit
              </Button>
            <Button
              variant="destructive"
              onClick={() => setShowDeleteModal(true)}
              className="px-4 py-2"
            >
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 animate-fade-in">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="mt-5 text-lg font-medium text-gray-900">Delete Item</h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete "{item.name}"? This action cannot be undone.
              </p>
            </div>
            <div className="mt-6 flex justify-end gap-3">
              <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Cart Dialog */}
      <CheckoutDialog
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        cart={cart}
        onUpdateQuantity={updateCartItemQuantity}
        onUpdatePrice={updateCartItemPrice}
        onRemoveItem={(itemId) => {
          setCart([]);
          setIsCartOpen(false);
        }}
        onCheckout={handleCheckout}
        isLoading={isLoading}
        storeCurrency={store?.currency}
        paymentMethod={paymentMethod}
        mpesaPhoneNumber={mpesaPhoneNumber}
        onPaymentMethodChange={setPaymentMethod}
        onMpesaPhoneNumberChange={setMpesaPhoneNumber}
      />
    </div>
  );
}
