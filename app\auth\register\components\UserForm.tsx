// app/auth/registration/components/UserForm.tsx
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Button } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from "@/components/ui/input";
import { Mail, User, Lock, ArrowRight } from 'lucide-react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

interface UserFormProps {
  onSubmit: () => void;
}

const UserForm: React.FC<UserFormProps> = ({ onSubmit }) => {
  const { control, handleSubmit, formState: { errors }, watch, setValue } = useFormContext();
  const name = watch('user.name');
  const email = watch('user.email');
  const password = watch('user.password');
  const { executeRecaptcha } = useGoogleReCaptcha();

  const isFormValid = name?.length >= 2 &&
                     email?.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) &&
                     password?.length >= 8;

  const onFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;
    if (!executeRecaptcha) return;
    const captcha_token = await executeRecaptcha('register');
    setValue('captcha_token', captcha_token);
    onSubmit();
  };

  return (
    <div className="bg-card rounded-lg border p-6 shadow-sm">
      <h2 className="text-2xl font-bold mb-6 text-center">Create Your Account</h2>
      <p className="text-muted-foreground mb-6 text-center">Enter your information to get started</p>

      <form onSubmit={onFormSubmit} className="space-y-4">
        {/* Name Field */}
        <FormField
          control={control}
          name="user.name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <User size={18} />
                </div>
                <FormControl>
                  <Input placeholder="John Doe" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email Field */}
        <FormField
          control={control}
          name="user.email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Mail size={18} />
                </div>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Password Field */}
        <FormField
          control={control}
          name="user.password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Lock size={18} />
                </div>
                <FormControl>
                  <Input type="password" placeholder="Create a strong password" {...field} className="pl-10" />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full mt-6"
          disabled={!isFormValid}
        >
          Continue to Store Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>

        <p className="text-xs text-muted-foreground mt-4 text-center">
          By creating an account, you agree to our Terms of Service and Privacy Policy
        </p>
      </form>
    </div>
  );
};

export default UserForm;