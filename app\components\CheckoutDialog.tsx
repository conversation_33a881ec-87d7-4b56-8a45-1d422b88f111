import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
    Trash2,
    Loader2,
    ShoppingCart,
    Plus,
    Minus,
    X,
    ArrowLeft,
    ArrowRight,
    Check,
    Smartphone,
    QrCode,
    Receipt,
    Banknote
} from "lucide-react";
import { PaymentMethod } from "@/lib/api/sales/models";
import Image from 'next/image';
import { toast } from "sonner";

interface CartItem {
    id: string;
    name: string;
    price: number;
    quantity: number;
    image?: string;
    has_discount: boolean;
    discount: number;
}

interface CheckoutDialogProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    cart: CartItem[];
    onUpdateQuantity: (itemId: string, quantity: number) => void;
    onUpdatePrice: (itemId: string, price: number) => void;
    onRemoveItem: (itemId: string) => void;
    onCheckout: () => Promise<void>;
    isLoading: boolean;
    storeCurrency?: string;
    paymentMethod: PaymentMethod | null;
    mpesaPhoneNumber: string;
    onPaymentMethodChange: (method: PaymentMethod | null) => void;
    onMpesaPhoneNumberChange: (phone: string) => void;
    itemDiscounts: { [itemId: string]: boolean };
    onToggleItemDiscount: (itemId: string) => void;
    cartTotal: number;
    saleStatusMessage?: string | null;
}

type CheckoutStep = 'cart' | 'summary' | 'payment';

export function CheckoutDialog({
    isOpen,
    onOpenChange,
    cart,
    onUpdateQuantity,
    onUpdatePrice,
    onRemoveItem,
    onCheckout,
    isLoading,
    storeCurrency = "KES",
    paymentMethod,
    mpesaPhoneNumber,
    onPaymentMethodChange,
    onMpesaPhoneNumberChange,
    itemDiscounts,
    onToggleItemDiscount,
    cartTotal,
    saleStatusMessage
}: CheckoutDialogProps) {
    const [currentStep, setCurrentStep] = useState<CheckoutStep>('cart');

    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discountTotal = cart.reduce((sum, item) => {
        if (item.has_discount && itemDiscounts[item.id]) {
            return sum + ((item.price * item.quantity) * (item.discount / 100));
        }
        return sum;
    }, 0);

    // Reset to first step when dialog opens
    useEffect(() => {
        if (isOpen) {
            setCurrentStep('cart');
        }
    }, [isOpen]);

    const handleCheckoutClick = useCallback(() => {
        let errorMsg: string | null = null;
        if (!paymentMethod) errorMsg = "Please select a payment method.";
        else if (paymentMethod === "mpesa") {
            const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;

            if (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber)) {
                errorMsg = "Please enter a valid M-Pesa number (e.g., 07..., 01..., 2547..., 2541...).";
            }
        }

        if (errorMsg) { toast.error(errorMsg); return; }
        onCheckout();
    }, [paymentMethod, mpesaPhoneNumber, onCheckout]);

    useEffect(() => {
        if (saleStatusMessage && saleStatusMessage.toLowerCase().includes('failed')) {
            toast.error(saleStatusMessage);
        }
    }, [saleStatusMessage]);

    const getStepTitle = () => {
        switch (currentStep) {
            case 'cart': return 'Review Cart';
            case 'summary': return 'Order Summary';
            case 'payment': return 'Payment';
            default: return 'Checkout';
        }
    };

    const getStepDescription = () => {
        switch (currentStep) {
            case 'cart': return 'Review items and quantities';
            case 'summary': return 'Confirm totals and discounts';
            case 'payment': return 'Choose payment method';
            default: return '';
        }
    };

    const canProceedToNext = () => {
        switch (currentStep) {
            case 'cart': return cart.length > 0;
            case 'summary': return true;
            case 'payment': return paymentMethod && (paymentMethod !== 'mpesa' || mpesaPhoneNumber);
            default: return false;
        }
    };

    const handleNext = () => {
        if (currentStep === 'cart') setCurrentStep('summary');
        else if (currentStep === 'summary') setCurrentStep('payment');
    };

    const handleBack = () => {
        if (currentStep === 'payment') setCurrentStep('summary');
        else if (currentStep === 'summary') setCurrentStep('cart');
    };

    const formattedCurrency = (amount: number) => {
        return amount.toLocaleString('en-US', {
            style: 'currency',
            currency: storeCurrency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => {
            if (!isLoading) {
                onOpenChange(open);
            }
        }}>
            <DialogContent className="max-w-2xl w-[95vw] h-[90vh] sm:h-auto sm:max-h-[90vh] flex flex-col p-0 overflow-hidden">
                {/* Header with Steps */}
                <DialogHeader className="flex-shrink-0 p-4 sm:p-6 border-b bg-background">
                    <div className="flex items-center justify-between mb-4">
                        <DialogTitle className="text-xl font-semibold flex items-center">
                            <ShoppingCart className="mr-2 h-5 w-5 text-primary" />
                            {getStepTitle()}
                        </DialogTitle>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onOpenChange(false)}
                            className="h-8 w-8"
                            disabled={isLoading}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Step Indicator */}
                    <div className="flex items-center space-x-4 mb-2">
                        {(['cart', 'summary', 'payment'] as CheckoutStep[]).map((step, index) => (
                            <div key={step} className="flex items-center">
                                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                                    currentStep === step
                                        ? 'bg-primary text-primary-foreground'
                                        : index < (['cart', 'summary', 'payment'] as CheckoutStep[]).indexOf(currentStep)
                                        ? 'bg-primary/20 text-primary'
                                        : 'bg-muted text-muted-foreground'
                                }`}>
                                    {index < (['cart', 'summary', 'payment'] as CheckoutStep[]).indexOf(currentStep) ? (
                                        <Check className="h-4 w-4" />
                                    ) : (
                                        index + 1
                                    )}
                                </div>
                                {index < 2 && (
                                    <div className={`w-12 h-0.5 mx-2 ${
                                        index < (['cart', 'summary', 'payment'] as CheckoutStep[]).indexOf(currentStep)
                                            ? 'bg-primary'
                                            : 'bg-muted'
                                    }`} />
                                )}
                            </div>
                        ))}
                    </div>

                    <DialogDescription className="text-sm text-muted-foreground">
                        {getStepDescription()}
                    </DialogDescription>
                </DialogHeader>

                {/* Main Content Area */}
                <div className="flex-1 overflow-y-auto">
                    {cart.length === 0 ? (
                        <div className="flex-1 flex flex-col items-center justify-center p-8">
                            <ShoppingCart className="h-16 w-16 text-muted-foreground/30 mb-4" />
                            <p className="text-muted-foreground mb-4">Your cart is empty</p>
                            <Button variant="outline" onClick={() => onOpenChange(false)}>
                                Continue Shopping
                            </Button>
                        </div>
                    ) : (
                        <>
                            {/* Step 1: Cart Review */}
                            {currentStep === 'cart' && (
                                <div className="p-4 sm:p-6 space-y-4">
                                    {cart.map((item) => {
                                        const itemTotal = item.price * item.quantity;
                                        const itemDiscountAmount = item.has_discount && itemDiscounts[item.id]
                                            ? itemTotal * (item.discount / 100)
                                            : 0;
                                        const finalItemPrice = itemTotal - itemDiscountAmount;

                                        return (
                                            <Card key={item.id} className="p-4">
                                                <div className="flex gap-4">
                                                    <div className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                                                        <Image
                                                            src={item.image || '/placeholder-image.png'}
                                                            alt={item.name}
                                                            fill
                                                            className="object-cover"
                                                        />
                                                        {item.has_discount && (
                                                            <Badge className="absolute -top-1 -right-1 text-xs bg-primary px-1.5 py-0.5">
                                                                {item.discount}% off
                                                            </Badge>
                                                        )}
                                                    </div>

                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex justify-between items-start mb-3">
                                                            <h3 className="font-semibold text-base sm:text-lg leading-tight text-card-foreground pr-2">
                                                                {item.name}
                                                            </h3>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => onRemoveItem(item.id)}
                                                                className="text-destructive hover:text-destructive/80 h-8 w-8 rounded-full flex-shrink-0"
                                                                disabled={isLoading}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>

                                                        <div className="space-y-3">
                                                            <div className="flex items-center justify-between">
                                                                <Label htmlFor={`price-${item.id}`} className="text-sm font-medium text-muted-foreground">
                                                                    Unit Price
                                                                </Label>
                                                                <div className="flex items-center space-x-2">
                                                                    <span className="text-sm text-muted-foreground">{storeCurrency}</span>
                                                                    <Input
                                                                        id={`price-${item.id}`}
                                                                        type="number"
                                                                        value={item.price}
                                                                        onChange={(e) => onUpdatePrice(item.id, parseFloat(e.target.value) || 0)}
                                                                        className="w-24 h-9 text-right"
                                                                        step="0.01"
                                                                        min="0"
                                                                        disabled={isLoading}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="flex items-center justify-between">
                                                                <Label className="text-sm font-medium text-muted-foreground">Quantity</Label>
                                                                <div className="flex items-center space-x-2">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="icon"
                                                                        onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                                                        className="h-8 w-8"
                                                                        disabled={isLoading || item.quantity <= 1}
                                                                    >
                                                                        <Minus className="h-4 w-4" />
                                                                    </Button>
                                                                    <span className="w-12 text-center font-medium">{item.quantity}</span>
                                                                    <Button
                                                                        variant="outline"
                                                                        size="icon"
                                                                        onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                                                                        className="h-8 w-8"
                                                                        disabled={isLoading}
                                                                    >
                                                                        <Plus className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                            </div>

                                                            {item.has_discount && (
                                                                <div className="flex items-center justify-between">
                                                                    <Label className="text-sm font-medium text-muted-foreground">Apply Discount</Label>
                                                                    <div className="flex items-center space-x-2">
                                                                        <Checkbox
                                                                            checked={itemDiscounts[item.id] || false}
                                                                            onCheckedChange={() => onToggleItemDiscount(item.id)}
                                                                            disabled={isLoading}
                                                                        />
                                                                        <span className="text-sm text-primary font-medium">
                                                                            {item.discount}% off
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            )}

                                                            <Separator />

                                                            <div className="flex justify-between items-center pt-2">
                                                                <span className="text-sm font-medium text-muted-foreground">Subtotal</span>
                                                                <div className="text-right">
                                                                    {itemDiscountAmount > 0 && (
                                                                        <div className="text-sm text-muted-foreground line-through">
                                                                            {formattedCurrency(itemTotal)}
                                                                        </div>
                                                                    )}
                                                                    <div className="text-lg font-bold">
                                                                        {formattedCurrency(finalItemPrice)}
                                                                    </div>
                                                                    {itemDiscountAmount > 0 && (
                                                                        <div className="text-sm text-primary font-medium">
                                                                            Saved {formattedCurrency(itemDiscountAmount)}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Card>
                                        );
                                    })}
                                </div>
                            )}

                            {/* Step 2: Order Summary */}
                            {currentStep === 'summary' && (
                                <div className="p-4 sm:p-6">
                                    <Card className="p-6">
                                        <div className="space-y-4">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Items ({cart.length})</span>
                                                <span>{formattedCurrency(subtotal)}</span>
                                            </div>
                                            {discountTotal > 0 && (
                                                <div className="flex justify-between text-primary">
                                                    <span>Discounts</span>
                                                    <span>-{formattedCurrency(discountTotal)}</span>
                                                </div>
                                            )}
                                            <Separator />
                                            <div className="flex justify-between text-lg font-bold">
                                                <span>Total</span>
                                                <span>{formattedCurrency(cartTotal)}</span>
                                            </div>
                                        </div>
                                    </Card>

                                    <div className="mt-6">
                                        <h3 className="font-semibold mb-4">Order Items</h3>
                                        <div className="space-y-3">
                                            {cart.map((item) => (
                                                <div key={item.id} className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{item.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {item.quantity} × {formattedCurrency(item.price)}
                                                        </p>
                                                    </div>
                                                    <span className="font-medium">
                                                        {formattedCurrency(item.price * item.quantity)}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Step 3: Payment */}
                            {currentStep === 'payment' && (
                                <div className="p-4 sm:p-6">
                                    <div className="space-y-6">
                                        <div>
                                            <h3 className="font-semibold mb-4">Choose Payment Method</h3>
                                            <div className="grid gap-4">
                                                {/* M-Pesa */}
                                                <Card
                                                    className={`p-4 cursor-pointer transition-colors ${
                                                        paymentMethod === 'mpesa' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                                                    }`}
                                                    onClick={() => onPaymentMethodChange('mpesa')}
                                                >
                                                    <div className="flex items-center space-x-3">
                                                        <div className="bg-green-100 p-2 rounded-lg">
                                                            <Smartphone className="h-6 w-6 text-green-600" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <h4 className="font-medium">M-Pesa</h4>
                                                            <p className="text-sm text-muted-foreground">Pay with M-Pesa mobile money</p>
                                                        </div>
                                                        <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                                                            paymentMethod === 'mpesa' ? 'bg-primary border-primary' : 'border-muted-foreground'
                                                        }`}>
                                                            {paymentMethod === 'mpesa' && (
                                                                <Check className="h-3 w-3 text-primary-foreground" />
                                                            )}
                                                        </div>
                                                    </div>
                                                </Card>

                                                {paymentMethod === 'mpesa' && (
                                                    <div className="ml-4 space-y-2">
                                                        <Label htmlFor="mpesa-phone">M-Pesa Phone Number</Label>
                                                        <Input
                                                            id="mpesa-phone"
                                                            type="tel"
                                                            placeholder="07XXXXXXXX or 2547XXXXXXXX"
                                                            value={mpesaPhoneNumber}
                                                            onChange={(e) => onMpesaPhoneNumberChange(e.target.value)}
                                                            disabled={isLoading}
                                                        />
                                                        {saleStatusMessage && (
                                                            <p className={`text-sm mt-2 ${saleStatusMessage.toLowerCase().includes('failed') ? 'text-destructive' : 'text-primary'}`}>
                                                                {saleStatusMessage}
                                                            </p>
                                                        )}
                                                    </div>
                                                )}

                                                {/* Cash */}
                                                <Card
                                                    className={`p-4 cursor-pointer transition-colors ${
                                                        paymentMethod === 'cash' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                                                    }`}
                                                    onClick={() => onPaymentMethodChange('cash')}
                                                >
                                                    <div className="flex items-center space-x-3">
                                                        <div className="bg-blue-100 p-2 rounded-lg">
                                                            <Banknote className="h-6 w-6 text-blue-600" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <h4 className="font-medium">Cash</h4>
                                                            <p className="text-sm text-muted-foreground">Pay with cash</p>
                                                        </div>
                                                        <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                                                            paymentMethod === 'cash' ? 'bg-primary border-primary' : 'border-muted-foreground'
                                                        }`}>
                                                            {paymentMethod === 'cash' && (
                                                                <Check className="h-3 w-3 text-primary-foreground" />
                                                            )}
                                                        </div>
                                                    </div>
                                                </Card>

                                                {/* Virtual Terminal (Placeholder) */}
                                                <Card className="p-4 opacity-60">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="bg-purple-100 p-2 rounded-lg">
                                                            <QrCode className="h-6 w-6 text-purple-600" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <h4 className="font-medium">Virtual Terminal</h4>
                                                            <p className="text-sm text-muted-foreground">Pay with card (Coming Soon)</p>
                                                        </div>
                                                        <Badge variant="secondary">Soon</Badge>
                                                    </div>
                                                </Card>
                                            </div>
                                        </div>

                                        {/* Order Summary in Payment Step */}
                                        <Card className="p-4 bg-muted/30">
                                            <div className="flex justify-between items-center">
                                                <span className="font-medium">Total Amount</span>
                                                <span className="text-xl font-bold">{formattedCurrency(cartTotal)}</span>
                                            </div>
                                        </Card>
                                    </div>
                                </div>
                            )}
                        </>
                    )}
                </div>

                {/* Footer with Navigation */}
                {cart.length > 0 && (
                    <div className="flex-shrink-0 p-4 sm:p-6 border-t bg-background">
                        <div className="flex justify-between items-center gap-4">
                            {currentStep !== 'cart' && (
                                <Button
                                    variant="outline"
                                    onClick={handleBack}
                                    disabled={isLoading}
                                    className="flex items-center"
                                >
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back
                                </Button>
                            )}

                            <div className="flex-1" />

                            {currentStep === 'payment' ? (
                                <Button
                                    onClick={handleCheckoutClick}
                                    disabled={isLoading || !canProceedToNext()}
                                    className="flex items-center"
                                >
                                    {isLoading ? (
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    ) : (
                                        <Receipt className="h-4 w-4 mr-2" />
                                    )}
                                    {isLoading ? 'Processing...' : 'Complete Order'}
                                </Button>
                            ) : (
                                <Button
                                    onClick={handleNext}
                                    disabled={!canProceedToNext()}
                                    className="flex items-center"
                                >
                                    Next
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            )}
                        </div>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    );
}
