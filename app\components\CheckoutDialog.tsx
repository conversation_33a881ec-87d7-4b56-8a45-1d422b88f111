import React, { useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Trash2, Loader2, ShoppingCart, Plus, Minus, CreditCard, Banknote, X } from "lucide-react";
import { PaymentMethod } from "@/lib/api/sales/models";
import Image from 'next/image';
import toast from 'react-hot-toast';
import { Badge } from "@/components/ui/badge";

interface CartItem {
    id: string;
    name: string;
    price: number;
    quantity: number;
    image?: string;
    has_discount: boolean;
    discount: number;
}

interface CheckoutDialogProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    cart: CartItem[];
    onUpdateQuantity: (itemId: string, quantity: number) => void;
    onUpdatePrice: (itemId: string, price: number) => void;
    onRemoveItem: (itemId: string) => void;
    onCheckout: () => Promise<void>;
    isLoading: boolean;
    storeCurrency?: string;
    paymentMethod: PaymentMethod | null;
    mpesaPhoneNumber: string;
    onPaymentMethodChange: (method: PaymentMethod | null) => void;
    onMpesaPhoneNumberChange: (phone: string) => void;
    itemDiscounts: { [itemId: string]: boolean };
    onToggleItemDiscount: (itemId: string) => void;
    cartTotal: number;
    saleStatusMessage?: string | null;
}

export function CheckoutDialog({
    isOpen,
    onOpenChange,
    cart,
    onUpdateQuantity,
    onUpdatePrice,
    onRemoveItem,
    onCheckout,
    isLoading,
    storeCurrency = "KES",
    paymentMethod,
    mpesaPhoneNumber,
    onPaymentMethodChange,
    onMpesaPhoneNumberChange,
    itemDiscounts,
    onToggleItemDiscount,
    cartTotal,
    saleStatusMessage
}: CheckoutDialogProps) {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discountTotal = cart.reduce((sum, item) => {
        if (item.has_discount && itemDiscounts[item.id]) {
            return sum + ((item.price * item.quantity) * (item.discount / 100));
        }
        return sum;
    }, 0);

    const handleCheckoutClick = useCallback(() => {
        let errorMsg: string | null = null;
        if (!paymentMethod) errorMsg = "Please select a payment method.";
        else if (paymentMethod === "mpesa") {
            const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;

            if (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber)) {
                errorMsg = "Please enter a valid M-Pesa number (e.g., 07..., 01..., 2547..., 2541...).";
            }
        }

        if (errorMsg) { toast.error(errorMsg); return; }
        onCheckout();
    }, [paymentMethod, mpesaPhoneNumber, onCheckout]);

    const formattedCurrency = (amount: number) => {
        return amount.toLocaleString('en-US', {
            style: 'currency',
            currency: storeCurrency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    };

    useEffect(() => {
        if (saleStatusMessage && saleStatusMessage.toLowerCase().includes('failed')) {
            toast.error(saleStatusMessage);
        }
    }, [saleStatusMessage]);

    return (
        <Dialog open={isOpen} onOpenChange={(open) => {
            if (!isLoading) {
                onOpenChange(open);
            }
        }}>
            <DialogContent className="max-w-lg w-[100vw] h-[100vh] sm:w-[95vw] sm:h-auto sm:max-h-[95vh] flex flex-col p-0 overflow-hidden rounded-none sm:rounded-lg">
                <DialogHeader className="flex-shrink-0 p-4 sm:p-6 border-b bg-background sticky top-0 z-10">
                    <DialogTitle className="text-lg font-semibold flex items-center justify-between">
                        <div className="flex items-center">
                            <ShoppingCart className="mr-2 h-5 w-5 text-primary" />
                            <span>Checkout ({cart.length} items)</span>
                        </div>
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onOpenChange(false)}
                            className="h-8 w-8 rounded-full sm:hidden"
                            disabled={isLoading}
                        >
                            <X className="h-5 w-5" />
                        </Button>
                    </DialogTitle>
                    {cart.length === 0 ? (
                        <DialogDescription className="text-base text-center py-2 text-muted-foreground">Your cart is empty.</DialogDescription>
                    ) : (
                        <DialogDescription className="text-base">Review and complete your purchase</DialogDescription>
                    )}
                </DialogHeader>

                {cart.length > 0 ? (
                    <>
                        <div className="flex-1 overflow-y-auto p-4 sm:p-6 pt-2 sm:pt-4 space-y-4">
                            {cart.map((item) => {
                                const itemTotal = item.price * item.quantity;
                                const itemDiscountAmount = item.has_discount && itemDiscounts[item.id]
                                    ? itemTotal * (item.discount / 100)
                                    : 0;
                                const finalItemPrice = itemTotal - itemDiscountAmount;

                                return (
                                    <div key={item.id} className="bg-card border rounded-xl p-4 shadow-sm">
                                        <div className="flex gap-4">
                                            <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                                                <Image
                                                    src={item.image || '/placeholder-image.png'}
                                                    alt={item.name}
                                                    layout="fill"
                                                    objectFit="cover"
                                                />
                                                {item.has_discount && (
                                                    <Badge className="absolute -top-1 -right-1 text-xs bg-green-600 hover:bg-green-700 px-1.5 py-0.5">
                                                        {item.discount}% off
                                                    </Badge>
                                                )}
                                            </div>

                                            <div className="flex-1 min-w-0">
                                                <div className="flex justify-between items-start mb-3">
                                                    <h3 className="font-semibold text-lg leading-tight text-card-foreground pr-2">
                                                        {item.name}
                                                    </h3>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => onRemoveItem(item.id)}
                                                        className="text-destructive hover:text-destructive/80 h-9 w-9 rounded-full flex-shrink-0"
                                                        disabled={isLoading}
                                                    >
                                                        <Trash2 className="h-5 w-5" />
                                                    </Button>
                                                </div>

                                                <div className="space-y-3">
                                                    <div className="flex items-center gap-3">
                                                        <Label className="text-sm font-medium text-muted-foreground min-w-0">Price:</Label>
                                                        <div className="flex items-center gap-2">
                                                            <span className="text-sm text-muted-foreground">{storeCurrency}</span>
                                                            <Input
                                                                type="number"
                                                                value={item.price}
                                                                onChange={(e) => {
                                                                    const newPrice = parseFloat(e.target.value);
                                                                    if (!isNaN(newPrice) && newPrice >= 0) {
                                                                        onUpdatePrice(item.id, newPrice);
                                                                    }
                                                                }}
                                                                className="w-24 h-10 text-base px-3 bg-background"
                                                                min="0"
                                                                step="0.01"
                                                                disabled={isLoading}
                                                            />
                                                        </div>
                                                    </div>

                                                    {item.has_discount && (
                                                        <div className="flex items-center">
                                                            <Checkbox
                                                                id={`discount-${item.id}`}
                                                                checked={!!itemDiscounts[item.id]}
                                                                onCheckedChange={() => onToggleItemDiscount(item.id)}
                                                                className="h-5 w-5 mr-3"
                                                                disabled={isLoading}
                                                            />
                                                            <Label htmlFor={`discount-${item.id}`} className="text-sm text-green-600 font-medium">
                                                                Apply {item.discount}% discount
                                                            </Label>
                                                        </div>
                                                    )}

                                                    <div className="flex justify-between items-center pt-2">
                                                        <div className="flex items-center bg-muted rounded-lg p-1">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
                                                                className="h-10 w-10 rounded-md"
                                                                disabled={item.quantity <= 1 || isLoading}
                                                            >
                                                                <Minus className="h-5 w-5" />
                                                            </Button>
                                                            <span className="w-12 text-center text-lg font-semibold">{item.quantity}</span>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                                                                className="h-10 w-10 rounded-md"
                                                                disabled={isLoading}
                                                            >
                                                                <Plus className="h-5 w-5" />
                                                            </Button>
                                                        </div>

                                                        <div className="text-right">
                                                            {itemDiscountAmount > 0 && (
                                                                <div className="text-sm text-muted-foreground line-through">
                                                                    {formattedCurrency(itemTotal)}
                                                                </div>
                                                            )}
                                                            <div className={`text-lg font-bold ${itemDiscountAmount > 0 ? 'text-green-600' : 'text-foreground'}`}>
                                                                {formattedCurrency(finalItemPrice)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        <div className="flex-shrink-0 border-t bg-background/80 backdrop-blur-sm sticky bottom-0 z-10">
                            <div className="p-4 sm:p-6 border-b">
                                <h3 className="font-medium text-sm mb-3">Order Summary</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Subtotal:</span>
                                        <span>{formattedCurrency(subtotal)}</span>
                                    </div>

                                    {discountTotal > 0 && (
                                        <div className="flex justify-between text-green-600">
                                            <span>Discount:</span>
                                            <span>-{formattedCurrency(discountTotal)}</span>
                                        </div>
                                    )}

                                    <div className="flex justify-between font-medium text-base pt-2 border-t">
                                        <span>Total:</span>
                                        <span className="text-lg font-bold text-primary">{formattedCurrency(cartTotal)}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="p-4 sm:p-6">
                                <Label className="text-sm font-medium block mb-3">Payment Method</Label>
                                <div className="grid grid-cols-2 gap-3 mb-4">
                                    <Button
                                        variant={paymentMethod === "mpesa" ? "default" : "outline"}
                                        onClick={() => onPaymentMethodChange("mpesa")}
                                        className="h-12 text-base flex items-center justify-center"
                                        disabled={isLoading}
                                    >
                                        <CreditCard className="mr-2 h-5 w-5" />
                                        M-Pesa
                                    </Button>

                                    <Button
                                        variant={paymentMethod === "cash" ? "default" : "outline"}
                                        onClick={() => onPaymentMethodChange("cash")}
                                        className="h-12 text-base flex items-center justify-center"
                                        disabled={isLoading}
                                    >
                                        <Banknote className="mr-2 h-5 w-5" />
                                        Cash
                                    </Button>
                                </div>

                                {paymentMethod === "mpesa" && (
                                    <div className="mb-4">
                                        <Label htmlFor="mpesa-phone-dialog" className="text-sm font-medium">M-Pesa Phone Number</Label>
                                        <Input
                                            id="mpesa-phone-dialog"
                                            type="tel"
                                            placeholder="e.g. ************, ***********"
                                            value={mpesaPhoneNumber}
                                            onChange={(e) => onMpesaPhoneNumberChange(e.target.value)}
                                            className="mt-1.5 h-12 text-base"
                                            disabled={isLoading}
                                        />
                                        <p className="text-xs text-muted-foreground mt-1.5">
                                            {isLoading && paymentMethod === "mpesa" 
                                                ? "Waiting for M-Pesa payment confirmation..." 
                                                : "Customer will receive a prompt to enter PIN."}
                                        </p>
                                        {saleStatusMessage && (
                                            <p className={`text-sm mt-2 ${saleStatusMessage.toLowerCase().includes('failed') ? 'text-destructive' : 'text-primary'}`}>
                                                {saleStatusMessage}
                                            </p>
                                        )}
                                    </div>
                                )}

                                <DialogFooter className="flex flex-col gap-3 mt-4">
                                    <Button
                                        onClick={handleCheckoutClick}
                                        disabled={isLoading || !paymentMethod || (paymentMethod === 'mpesa' && !mpesaPhoneNumber)}
                                        className="w-full h-12 text-base font-semibold"
                                        size="lg"
                                    >
                                        {isLoading ? (
                                            <><Loader2 className="mr-2 h-5 w-5 animate-spin" /> Processing...</>
                                        ) : (
                                            <>Complete Purchase {formattedCurrency(cartTotal)}</>
                                        )}
                                    </Button>

                                    <Button
                                        variant="outline"
                                        onClick={() => onOpenChange(false)}
                                        className="w-full h-12 text-base"
                                        disabled={isLoading}
                                    >
                                        Cancel
                                    </Button>
                                </DialogFooter>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="flex-1 flex flex-col items-center justify-center p-8">
                        <ShoppingCart className="h-16 w-16 text-muted-foreground/30 mb-4" />
                        <p className="text-muted-foreground mb-4">Your cart is empty</p>
                        <Button variant="outline" onClick={() => onOpenChange(false)}>Continue Shopping</Button>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    );
}