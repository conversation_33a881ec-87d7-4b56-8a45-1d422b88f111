"use client";

import { useEffect, useState } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Trash2, Plus, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from "next/navigation";
import { getColumns } from "./columns";
import { Supplier } from "@/lib/api/suppliers/models";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import Cookies from "js-cookie";

function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );
}

export default function Page() {
  const router = useRouter();
  const [data, setSuppliers] = useState<Supplier[]>([]);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState({});
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");

  useEffect(() => {
    async function getSuppliers() {
      if (token && store_id) {
        setIsInitialLoading(true);
        try {
          const fetchedSuppliers = await fetchSuppliers(token, store_id);
          setSuppliers(fetchedSuppliers);
        } catch (error) {
          console.error("Error fetching suppliers:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }
    getSuppliers();
  }, [store_id, token]);

  const handleDeleteSelected = async () => {
    if (!token || !store_id) {
      console.error("Authentication token or store ID is missing.");
      return;
    }
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedSupplierIds = selectedRows.map((row) => row.original.id);

    try {
      for (const customerId of selectedSupplierIds) {
        // await deleteSupplier(token, store_id, customerId);
        router.refresh();
      }
      setSuppliers((prev) =>
        prev.filter((customer) => !selectedSupplierIds.includes(customer.id)),
      );
      setRowSelection({});
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting customers:", error);
    }
  };

  const columns = getColumns(router);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const filteredRows = table.getFilteredRowModel().rows;
  const pageCount = Math.ceil(filteredRows.length / pageSize);
  const start = pageIndex * pageSize;
  const end = start + pageSize;
  const paginatedRows = filteredRows.slice(start, end);

  const showSelectedItems = (
    <ul>
      {table
        .getSelectedRowModel()
        .rows.map((row) => (
          <li key={row.id}>- {row.original.name}</li>
        ))}
    </ul>
  );

  return (
    <div className="w-full">
      {/* Top Section: Input, New Supplier Button, and Columns Dropdown */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-8 py-4">
        <Input
          placeholder="Filter suppliers..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) => table.getColumn("name")?.setFilterValue(event.target.value)}
          className="w-full sm:max-w-sm"
          disabled={isInitialLoading}
        />

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0">
          <Button
            onClick={() => router.push("/app/suppliers/create")}
            className="w-full"
            disabled={isInitialLoading}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Supplier
          </Button>

          {/* Columns Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto" disabled={isInitialLoading}>
                Columns <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border relative w-full overflow-auto grid grid-cols-1">
        <Table className="w-full caption-bottom text-sm">
          <TableHeader>
            {table
              .getHeaderGroups()
              .map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
          </TableHeader>
          <TableBody>
            {isInitialLoading ? (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  <LoadingSpinner />
                </TableCell>
              </TableRow>
            ) : paginatedRows.length ? (
              paginatedRows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() ? "selected" : undefined}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Delete Button and Pagination at the Bottom */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          {Object.keys(rowSelection).length > 0 && (
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" size="sm" disabled={isInitialLoading}>
                  <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Suppliers</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete the selected suppliers(s)?
                    {showSelectedItems}
                    <b>This action cannot be undone. </b>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteSelected}>
                    Delete
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <TablePagination
            pageIndex={pageIndex}
            pageSize={pageSize}
            pageCount={pageCount}
            totalItems={filteredRows.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>
    </div>
  );
}