"use client";

import React from "react";


import * as z from "zod";
import { useRouter } from "next/navigation";
import CategoryModal from "../CategoryModal";
// import CategoryModal from "./CategoryModal";

const categoryFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;

const CreateCategoryPage = ({ params, searchParams }: { params: any; searchParams: any }) => {
  const router = useRouter();
  const [open, setOpen] = React.useState(true);

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      router.back();
    }
  };

  return (
    <CategoryModal open={open} onOpenChange={handleOpenChange} onSubmit={async () => {}} />
  );
};

export default CreateCategoryPage;
